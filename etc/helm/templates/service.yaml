apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/path: "{{ .Values.metricsUrl }}"
    prometheus.io/port: '{{ .Values.service.port }}'
    prometheus.io/scheme: http
    prometheus.io/scrape: 'true'
  labels:
    {{- include "application.labels" . | nindent 4 }}
  name: {{ include "application.fullname" . }}
spec:
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "application.selectorLabels" . | nindent 4 }}
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}