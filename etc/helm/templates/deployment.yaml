apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "application.fullname" . }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "application.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "application.selectorLabels" . | nindent 8 }}
      annotations:
        timestamp: "{{ .Values.timestamp }}"
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      securityContext:
        {{- toYaml .Values.securityContext | nindent 8 }}
      automountServiceAccountToken: false
      serviceAccountName: {{ .Chart.Name }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          securityContext:
            {{- toYaml .Values.podSecurityContext | nindent 12 }}
          envFrom:
          - configMapRef:
              name: {{ include "application.fullname" . }}-config
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: {{ .Values.healthUrl }}
              port: http
            failureThreshold: 3
            initialDelaySeconds: 140
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 3
          readinessProbe:
            httpGet:
              path: {{ .Values.healthUrl }}
              port: http
            failureThreshold: 3
            initialDelaySeconds: 140
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 3
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
