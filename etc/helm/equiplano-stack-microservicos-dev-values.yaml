replicaCount: 1
image:
  repository: sa-saopaulo-1.ocir.io/gr3m6keec5b3/devops/dev/compra-front
  pullPolicy: Always
imagePullSecrets:
  - name: docker-login
nameOverride: ''
fullnameOverride: ''
podSecurityContext:
  runAsNonRoot: false
  capabilities:
    add:
      - CHOWN
      - SETGID
      - SETUID
      - NET_BIND_SERVICE
    drop:
      - ALL
  readOnlyRootFilesystem: false
  allowPrivilegeEscalation: false
securityContext:
  runAsNonRoot: false
  runAsUser: 0
service:
  type: ClusterIP
  port: 80
resources:
  limits:
    cpu: 50m
    memory: 50Mi
  requests:
    cpu: 50m
    memory: 50Mi
tolerations: []
nodeSelector: {}
affinity: {}
#config map variables
configmaps:
  SERVICE_NAME: 'compra-front'
secrets:
## Enable persistence using Persistent Volume Claims
## ref: http://kubernetes.io/docs/user-guide/persistent-volumes/
##
#probes endpoint
healthUrl: '/actuator/health'
metricsUrl: '/actuator/prometheus'
#namespace
namespace: 'equiplano'
ingress:
  annotations:
    kubernetes.io/ingress.class: nginx
  hosts:
    - host: 'compra-front-dev.ops.equiplano.com.br'
      paths:
        - '/'
component: 'application'
partof: 'comum'
tags:
  ORIGEM-FINANCEIRA: 'PROJETOS'
  SISTEMA: 'SISTEMA-EXEMPLO'
  CENTRO-CUSTO: 'X'
  ENVIRONMENT: 'DEV'
