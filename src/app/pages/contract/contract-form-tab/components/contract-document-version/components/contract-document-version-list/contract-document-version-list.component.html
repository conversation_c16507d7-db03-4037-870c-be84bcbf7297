<eqp-standard-page
  title="Versões"
  mainTitle="Versões"
  [spinnerActive]="loading"
  [spinnerSize]="'large'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonType]="'info'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonTitle]="'Voltar'"
  (bottomLeftButtonEmitter)="dispose()"
>
  <dx-data-grid
    [allowColumnResizing]="true"
    [columnAutoWidth]="true"
    [dataSource]="dataSource"
    [showColumnLines]="false"
    [showRowLines]="false"
    [showBorders]="false"
    [rowAlternationEnabled]="true"
    [wordWrapEnabled]="true"
    [loadPanel]="false"
    [columnHidingEnabled]="true"
    [remoteOperations]="true"
    keyExpr="uuid"
    (onToolbarPreparing)="onToolbarPreparing($event)"
  >
    <dxo-export
      [enabled]="true"
      [fileName]="'Versões'"
    ></dxo-export>

    <dxo-sorting mode="multiple"></dxo-sorting>

    <dxo-header-filter [visible]="false"> </dxo-header-filter>
    <dxo-filter-row [visible]="false"></dxo-filter-row>

    <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

    <dxo-search-panel
      [visible]="true"
      placeholder="Buscar versão"
    ></dxo-search-panel>

    <dxo-editing
      mode="form"
      [allowUpdating]="false"
      [allowDeleting]="false"
      [allowAdding]="true"
      [useIcons]="true"
    >
    </dxo-editing>
    <dxi-column
      caption="Ordem"
      dataField="numero"
      alignment="left"
      [allowSorting]="false"
    ></dxi-column>
    <dxi-column
      caption="Nome"
      dataField="nome"
      [allowSorting]="false"
    ></dxi-column>
    <dxi-column
      caption="Publicar"
      dataField="flagPublicar"
      [allowSorting]="false"
    >
      <dxo-lookup
        [dataSource]="[{key: 'S', value: 'Sim'}, {key: 'N', value: 'Não'}]"
        valueExpr="key"
        displayExpr="value"
      ></dxo-lookup>
    </dxi-column>

    <dxi-column
      dataField="uuid"
      caption=""
      [width]="150"
      [allowFiltering]="false"
      [allowSorting]="false"
      cellTemplate="acaoColumn"
    ></dxi-column>

    <div *dxTemplate="let data of 'acaoColumn'">
      <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
        <a
          title="Editar"
          (click)="edit(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          title="Remover"
          (click)="remove(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </div>
  </dx-data-grid>
</eqp-standard-page>
