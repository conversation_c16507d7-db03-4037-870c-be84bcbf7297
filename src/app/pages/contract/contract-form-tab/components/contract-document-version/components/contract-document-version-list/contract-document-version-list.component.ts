import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit
} from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { filter, finalize, take } from 'rxjs/operators'
import { ContractDocumentVersionService } from '../../services/contract-document-version.service'
import { ContractDocumentVersionFormComponent } from '../contract-document-version-form/contract-document-version-form.component'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component';

@Component({
  selector: 'app-licitation-document-version-list',
  templateUrl: './contract-document-version-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractDocumentVersionListComponent implements OnInit {
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
    private crudService: CrudService,
    private service: ContractDocumentVersionService,
    private cd: ChangeDetectorRef,
  ) {}

  loading: boolean = false

  dataSource: DataSource
  parentUuid: string
  contractUuid: string
  documentModelUuid: string

  ngOnInit(): void {
    this.parentUuid = this.route.snapshot.params?.documentUuid
    this.contractUuid = this.route.snapshot.params?.uuid
    this.fetchGrid()
    this.route.queryParams.subscribe(params => {
      this.documentModelUuid = params['documentoModeloUuid']
    })
  }

  fetchGrid() {
    this.loading = true
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `licitacao/contrato/documento/${this.parentUuid}/versao`,
        10,
      ),
      onLoadingChanged: isLoading => {
        this.loading = isLoading
        this.cd.markForCheck()
      },
      paginate: true,
      pageSize: 10,
    })
  }

  onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Versão'
      event.toolbarOptions.items[0].options.hint = 'Nova Versão'
      event.toolbarOptions.items[0].options.onClick = () => this.edit()
    }
  }

  edit(uuid?: string) {
    this.dialogService
      .open(ContractDocumentVersionFormComponent, {
        context: {
          parentUuid: this.parentUuid,
          contractUuid: this.contractUuid,
          documentModelUuid: this.documentModelUuid,
          uuid,
        },
        closeOnEsc: false,
        closeOnBackdropClick: false,
      })
      .onClose.pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(() => this.dataSource.reload())
  }

  remove(uuid: string) {
    this.dialogService
      .open(ModalConfirmarComponent, {
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(
        take(1),
        filter(res => res == 'S'),
      )
      .subscribe(() => {
        this.loading = true
        this.cd.markForCheck()
        this.service
          .delete(this.contractUuid, this.parentUuid, uuid)
          .pipe(
            take(1),
            finalize(() => {
              this.loading = false
              this.cd.markForCheck()
            }),
          )
          .subscribe({
            next: () => {
              this.dataSource.reload()
              this.toastr.send({
                success: true,
                message: 'Versão removida com sucesso.',
              })
            },
          })
      })
  }

  dispose() {
    this.router.navigate([
      'contrato',
      'edit',
      this.contractUuid,
      'documento',
    ])
  }
}
