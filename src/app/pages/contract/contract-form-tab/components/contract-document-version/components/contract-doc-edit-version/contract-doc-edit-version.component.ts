import { Clipboard } from '@angular/cdk/clipboard';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SafeResourceUrl } from '@angular/platform-browser';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef, NbDialogService } from '@nebular/theme';
import { ContractDocumentService } from '@pages/contract/services/contract-document.service';
import DataSource from 'devextreme/data/data_source';
import { Subject } from 'rxjs';
import { filter, finalize, switchMap, take, takeUntil } from 'rxjs/operators';
import { ContractDocumentVersionService } from '../../services/contract-document-version.service';
import { ContractComissionSignatureDialogComponent } from './components/contract-comission-signature-dialog/contract-comission-signature-dialog.component';
import { ContractProviderSignatureDialogComponent } from './components/contract-provider-signature-dialog/contract-provider-signature-dialog.component';
import { memberCommitteeTemplate } from './templates/contract-commision-signature';
import { licitationProviderSignatureTemplate } from './templates/contract-provider-signature';


@Component({
  selector: 'app-contract-doc-edit-version',
  templateUrl: './contract-doc-edit-version.component.html',
  styleUrls: ['./contract-doc-edit-version.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractDocEditVersionComponent implements OnInit, OnDestroy {
  constructor(
    private builder: FormBuilder,
    private documentService: ContractDocumentService,
    private documentVersionService: ContractDocumentVersionService,
    private crudService: CrudService,
    private dialogRef: NbDialogRef<ContractDocEditVersionComponent>,
    private toastr: ToastrService,
    private cd: ChangeDetectorRef,
    private clipboard: Clipboard,
    private dialogService: NbDialogService
  ) {}

  @Input() nome: string
  @Input() numero: number

  unsub$ = new Subject<void>()

  @Input() versaoUuid: string
  @Input() documentModelUuid: string
  @Input() documentUuid: string
  @Input() contractUuid: string
  @Input() flagPublicarPortal: boolean

  options: Object = {
    heightMin: 300,
    charCounterMax: 1000000,
    dragInline: false,
    disableRightClick: true,
    pastePlain: true,
    placeholderText: 'Insira o texto aqui...',
    language: 'pt_br',
    events: {
      'image.beforeUpload': function (files) {
        var editor = this
        if (files.length) {
          // Create a File Reader.
          var reader = new FileReader()
          // Set the reader to insert images when they are loaded.
          reader.onload = function (e) {
            var result = e.target.result
            editor.image.insert(result, null, null, editor.image.get())
          }
          // Read image as base64.
          reader.readAsDataURL(files[0])
        }
        editor.popups.hideAll()
        // Stop default upload chain.
        return false
      },
    },
    pluginsEnabled: [
      'align',
      'draggable',
      'fontSize',
      'fullscreen',
      'image',
      'imageTUI',
      'imageManager',
      'inlineClass',
      'lists',
      'paragraphStyle',
      'table',
      'paragraphFormat',
    ],
    toolbarButtons: {
      moreText: {
        buttons: [
          'bold',
          'italic',
          'underline',
          'strikeThrough',
          'subscript',
          'superscript',
          'fontFamily',
          'fontSize',
          'inlineClass',
          'inlineStyle',
          'clearFormatting',
        ],
      },

      moreParagraph: {
        buttons: [
          'alignLeft',
          'alignCenter',
          'formatOLSimple',
          'alignRight',
          'alignJustify',
          'formatOL',
          'formatUL',
          'paragraphFormat',
          'paragraphStyle',
          'lineHeight',
          'outdent',
          'indent',
          'quote',
        ],
      },

      moreRich: {
        buttons: ['insertImage', 'insertTable'],
      },

      moreMisc: {
        buttons: ['undo', 'redo', 'fullscreen'],

        align: 'right',

        buttonsVisible: 3,
      },
    },
  }

  loading = false

  documentModelValue: any

  contentPreview: SafeResourceUrl

  editorSelecionado: string

  model?: FormGroup
  processoTagsData?: DataSource
  processoSubTagsData?: DataSource

  @Input() content: string;

  ngOnInit(): void {
    this.model = this.getNewForm()
    this.model.get('publicarPortal').patchValue(this.flagPublicarPortal)
    this.model.get('conteudo').patchValue(this.content);
    this.fetchDocumentData()
  }

  ngOnDestroy(): void {
    this.unsub$.next()
    this.unsub$.complete()
  }

  private getNewForm(): FormGroup {
    return this.builder.group({
      processoSubTagUuid: [],
      processoTagUuid: [],
      cabecalho: [],
      publicarPortal: [],
      conteudo: [''],
      rodape: []
    })
  }

  fetchDocumentData() {
    this.loading = true
    this.documentService
      .getIndividual(this.contractUuid, this.documentUuid)
      .pipe(
        take(1),
        finalize(() => {
          this.loading = false
          this.cd.markForCheck()
        }),
      )
      .subscribe(res => {
        if (!this.versaoUuid) {
          this.loadContent(res.dados.processoDocumentoModelo)
        } else {
          this.fetchPageData(this.versaoUuid)
        }
        this.documentModelValue = res.dados.processoDocumentoModelo
        this.loadTagOptionsData(
          res.dados?.processoDocumentoModelo?.processoDocumento?.processo?.uuid,
        )
        this.startTagHandler()
        this.startSubTagHandler()
      })
  }

  loadTagOptionsData(processDocumentUuid: string) {
    this.processoTagsData = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'processo_tag',
        10,
        'processo.uuid',
        processDocumentUuid,
      ),
      paginate: true,
      pageSize: 10,
      map: data => {
        data.valor = data.uuid + '/' + data.nome
        data.descricao += ' - ' + data.nome
        return data
      },
    })
  }

  startTagHandler() {
    this.model
      .get('processoTagUuid')
      .valueChanges.pipe(takeUntil(this.unsub$))
      .subscribe(uuid => {
        const tagUuid = uuid.split('/')[0]
        const tag = uuid.split('/')[1]
        this.updateContent(tag)
        this.ajusteChangeProcessoTag(tagUuid)
        this.model
          .get('processoSubTagUuid')
          .patchValue('', { emitEvent: false })
      })
  }

  startSubTagHandler() {
    this.model
      .get('processoSubTagUuid')
      .valueChanges.pipe(takeUntil(this.unsub$))
      .subscribe(tag => {
        const tagParent = this.model.get('processoTagUuid').value.split('/')[1]
        this.updateContent(`${tagParent}&${tag}`)
      })
  }

  updateContent(processoTag: any) {
    const tag = `<${processoTag}>`
    this.clipboard.copy(tag)
    this.toastr.send({
      title: 'Tag copiada com sucesso',
      info: true,
      message: 'Tag copiada para a área de transferência',
    })
  }

  loadContent(processoDocumentoModelo: any) {
    const content =
      processoDocumentoModelo.cabecalho +
      processoDocumentoModelo.conteudo +
      processoDocumentoModelo.rodape
    this.model.get('conteudo').patchValue(content)
    this.loading = false
    this.cd.markForCheck()
  }

  fetchPageData(versaoUuid: string) {
    this.documentVersionService
      .getIndividual(this.contractUuid, this.documentUuid, versaoUuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        const content = res.dados?.conteudo.toString()
        this.model.get('conteudo').patchValue(content)
        this.cd.markForCheck()
      })
  }

  focusEditor(event, nome) {
    this.editorSelecionado = nome
  }

  cancel() {
    this.dialogRef.close(null)
  }

  ajusteChangeProcessoTag(uuid: string): void {
    this.processoSubTagsData = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'processo_subtag',
        10,
        'processoTag.uuid',
        uuid,
      ),
      paginate: true,
      pageSize: 10,
      map: data => {
        data.descricao += ' - ' + data.nome
        return data
      },
    })
  }


  mergeTags() {
    this.loading = true;
    const dto = this.prepare(this.model.getRawValue())
    if (this._issetCustomFlowTagInText(dto.conteudo)) {
      this._mergeCustomFlowTags(dto.conteudo)
    } else {
      this.documentVersionService
        .put(this.contractUuid, this.documentUuid, dto, this.versaoUuid)
        .pipe(
          switchMap(() =>
            this.documentVersionService.mergeFlags(
              this.documentUuid,
              this.versaoUuid,
              this.model.get('conteudo')?.value,
            ),
          ),
        )
        .pipe(
          take(1),
          finalize(() => this.loading = false)
        )
        .subscribe({
          next: (res: any) => {
            this.model.get('conteudo').patchValue(res.dados?.conteudo)
            this.cd.markForCheck()
          },
          error: (err: any) => {
            this.toastr.send({
              title: 'Error',
              error: true,
              message: 'Erro ao mesclar tags',
            })
          },
        })
    }
  }

  viewPreview() {
    var win = window.open('', '', 'height=700,width=700')
    let content =
      '<html><body>' + this.model.get('conteudo')?.value + '</body></html>'
    if (content.charAt(13) !== '<') {
      this.convertToPdf(this.model.get('conteudo')?.value as HTMLElement, win)
    }
    const parser = new DOMParser()
    const doc = parser.parseFromString(content, 'text/html')
    win.close()
    const element = doc.body.firstChild
    this.convertToPdf(element as HTMLElement, win)
  }

  saveWordFile() {
    this.export2Word(
      this.model.get('conteudo')?.value,
      this.snakeCaseString(this.documentModelValue?.nome),
    )
  }

  snakeCaseString(str) {
    return (
      str &&
      str
        .match(
          /[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g,
        )
        .map(s => s.toLowerCase())
        .join('_')
    )
  }

  convertToPdf(element: HTMLElement | string, win: any) {
    var style = '<style>'
    style = style + 'table {width: 100%;font: 20px Calibri;}'
    style =
      style +
      'table, th, td {border: solid 1px DDD; border-collapse: collapse;'
    style = style + 'padding: 2px 3px;text-align: center;}'
    style = style + '</style>'
    win.document.write('<html><head>')
    win.document.write('<title></title>')
    win.document.write(style)
    win.document.write('</head>')
    win.document.write('<body>')
    if (typeof element === 'string') {
      win.document.write(element)
    } else {
      win.document.write((element as HTMLElement).innerHTML)
    }
    win.document.write('</body></html>')
    win.document.close()
    win.print()
  }

  prepare(formData: any) {
    const dto = {
      conteudo: formData.conteudo,
      flagPublicar: formData.publicarPortal ? 'S' : 'N',
      nome: this.nome,
      numero: this.numero,
    }
    return dto
  }

  confirm() {
    const dto = this.prepare(this.model.getRawValue())
    this.dialogRef.close({
      conteudo: dto?.conteudo,
      flagPublicar: dto?.flagPublicar,
    })
  }

  export2Word(element: string, filename: string = ''): void {
    const wordStyles = `
      <style>
        .WordSection1 {
          width:100vw !important;
          height:50% !important;
        }
  
        table {
          width: 100vw !important;
          border-collapse: collapse !important;
          table-layout: fixed !important;
          margin: 5pt 0 !important;
          border: 1pt solid windowtext !important;
          mso-border-alt: solid windowtext .5pt !important;
          mso-padding-alt: 0cm 5.4pt 0cm 5.4pt !important;
          mso-border-insideh: .5pt solid windowtext !important;
          mso-border-insidev: .5pt solid windowtext !important;
          mso-table-layout-alt: fixed !important;
          mso-table-overlap: never !important;
          word-wrap: break-word !important;
          page-break-inside: avoid !important;
          mso-table-spacing-before: 25pt !important;
          mso-table-spacing-after: 25pt !important;
          height: auto !important;
        }
        
        td, th {
          border: 1pt solid windowtext !important;
          padding: 8pt !important;
          vertical-align: top !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
          max-width: 100% !important;
          height: auto !important;
        }
        
        tr {
          mso-yfti-irow: 0 !important;
          mso-row-margin-left: 0 !important;
          mso-row-margin-right: 0 !important;
          height: auto !important;
        }
  
        p {
          margin: 6pt 0 !important;
          padding: 0 !important;
        }
  
        body, div {
          height: auto !important;
        }
      </style>
    `;
  
    const processElement = (htmlContent: string): string => {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      
      const tables = Array.from(tempDiv.getElementsByTagName('table'));
      tables.forEach(table => {
        table.setAttribute('border', '1');
        table.setAttribute('cellspacing', '0');
        table.setAttribute('cellpadding', '0');
        table.setAttribute('style', 'width:100% !important;');
        table.setAttribute('class', 'MsoTableGrid');
        table.setAttribute('mso-table-spacing-before', '25pt');
        table.setAttribute('mso-table-spacing-after', '25pt');
        
        const cells = Array.from(table.getElementsByTagName('td'));
        cells.forEach(cell => {
          if (cell.hasAttribute('colspan')) {
            const firstRow = table.rows[0];
            if (firstRow) {
              const totalCells = firstRow.cells.length;
              const colspanValue = parseInt(cell.getAttribute('colspan') || '1');
              const cellWidth = Math.floor(100 / totalCells * colspanValue);
              cell.style.overflowWrap = 'break-word';
              cell.setAttribute('width', `${cellWidth}%`);
              cell.setAttribute('style', `width:${cellWidth}%;word-wrap:break-word;padding:1pt !important;height:auto !important;`);
            }
          } else {
            const firstRow = table.rows[0];
            if (firstRow) {
              cell.style.overflowWrap = 'break-word';
              const cellWidth = Math.floor(100 / firstRow.cells.length);
              cell.setAttribute('width', `${cellWidth}%`);
              cell.setAttribute('style', `width:${cellWidth}%;word-wrap:break-word;padding:1pt !important;height:auto !important;`);
            }
          }
        });
      });
      
      return tempDiv.innerHTML;
    };
  
    const header = 
      "<html xmlns:o='urn:schemas-microsoft-com:office:office' " +
      "xmlns:w='urn:schemas-microsoft-com:office:word' " +
      "xmlns:m='http://schemas.microsoft.com/office/2004/12/omml' " +
      "xmlns='http://www.w3.org/TR/REC-html40'>" +
      "<head>" +
      "<meta charset='utf-8'>" +
      "<meta http-equiv='Content-Type' content='text/html; charset=utf-8'>" +
      "<meta name='ProgId' content='Word.Document'>" +
      "<meta name='Generator' content='Microsoft Word 15'>" +
      "<meta name='Originator' content='Microsoft Word 15'>" +
      wordStyles +
      "</head><body class='WordSection1'>";
    
    const footer = "</body></html>";
    
    const processedElement = processElement(element);
    const sourceHTML = header + processedElement + footer;
    
    const source = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(sourceHTML);
    
    const fileDownload = document.createElement('a');
    document.body.appendChild(fileDownload);
    fileDownload.href = source;
    fileDownload.download = `${filename}.doc`;
    fileDownload.click();
    document.body.removeChild(fileDownload);
  }

  private _mergeCustomFlowTags(content: string) {
    if (this._issetSpecificTag(content, 'PROPONENTES.LICITACAOA')) {
      this._openLicitationProponentSignatureDialog()
    }else if (this._issetSpecificTag(content, 'MEMBROSCOMISSAO.LICITACAOA')) {
      this._mergeCommissionDataSignature();
    }else {
      this.toastr.send({
        title: 'Error',
        error: true,
        message: "Marcação de tag personalizada '?' inválida",
      })
    }
  }

  private _openLicitationProponentSignatureDialog() {
    const dialogRef = this.dialogService.open(
     ContractProviderSignatureDialogComponent,
    )
    dialogRef.componentRef.instance.licitationUuid = this.contractUuid
    dialogRef.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        const newTagContent = licitationProviderSignatureTemplate(res)
        const newContent = this._overrideTag(
          'PROPONENTES.LICITACAOA',
          newTagContent,
          this.model.get('conteudo').value,
        )
        this.model.get('conteudo').patchValue(newContent)
        this.cd.markForCheck()
      })
  }

  private _mergeCommissionDataSignature() {
    const dialogRef = this.dialogService.open(
      ContractComissionSignatureDialogComponent
    );
    dialogRef.componentRef.instance.licitationUuid = this.contractUuid;
    dialogRef.componentRef.instance.pageTitle = "Membros comissão";
    dialogRef.onClose
    .pipe(
      take(1),
      filter(res => res),
    )
    .subscribe(res => {
      const newTagContent = this._parseDataToTagTemplate(
        res,
        'MEMBROSCOMISSAO.LICITACAOA',
      )
      const newContent = this._overrideTag(
        'MEMBROSCOMISSAO.LICITACAOA',
        newTagContent,
        this.model.get('conteudo').value,
      )
      this.model.get('conteudo').patchValue(newContent);
      this.model.markAsDirty();
      this.cd.markForCheck();
    });
  }

  /**
   *
   * @param content Text to search tag
   * Insert all custom flow tag search rule.
   * Example: content.includes('A&gt;') || content.includes('OTHERT&gt;')
   * @returns boolean
   */
  private _issetCustomFlowTagInText(content: string): boolean {
    return content.includes('#A&gt;')
  }

  /**
   *
   * @param content Text to search tag
   * @param tag Tag code without '<' and '>'
   * @returns boolean
   */
  private _issetSpecificTag(content: string, tag: string): boolean {
    return content.includes(`&lt;${tag}&gt;`)
  }

  /**
   *
   * @param tag Tag code without '<' and '>' to override
   * @param value String value to override 'tag'
   * @param content Origin text to replace tags
   * @returns 'content' with new values
   */
  private _overrideTag(tag: string, value: string, content: string) {
    content = content.replace(`&lt;${tag}&gt;`, value)
    if (this._issetSpecificTag(content, tag)) {
      return this._overrideTag(tag, value, content)
    }
    return content
  }

  private _parseDataToTagTemplate(data: any, tag: string): string {
    switch (tag) {
      case 'PROPONENTES.LICITACAOA':
        return licitationProviderSignatureTemplate(data)
      case 'MEMBROSCOMISSAO.LICITACAOA':
        return  memberCommitteeTemplate(data);
    }
  }

}
