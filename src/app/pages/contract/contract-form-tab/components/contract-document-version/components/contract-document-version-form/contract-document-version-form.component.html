<eqp-nebular-dialog
  dialogTitle="Versão"
  dialogSize="medium "
  [spinnerActive]="loading"
  [spinnerSize]="'large'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="model.invalid || model.pristine"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonTitle]="'Voltar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="cancel()"
>
  <ng-container [formGroup]="model">
    <div class="row mb-3">
      <div class="col-3">
        <eqp-nebular-input
          [style]="'basic'"
          [type]="'number'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Sequência"
          placeholder="Sequência"
          formControlName="numero"
          disabled="true"
        ></eqp-nebular-input>
      </div>
      <div class="col-9">
        <eqp-nebular-input
          [style]="'basic'"
          [type]="'text'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Nome"
          placeholder="Nome"
          formControlName="nome"
          [required]="true"
        ></eqp-nebular-input>
      </div>
    </div>
    <div class="d-flex align-items-center" style="gap: 1rem">
      <eqp-nebular-button
        [buttonVisible]="true"
        [buttonType]="'primary'"
        [buttonTitle]="'Editar documento'"
        [buttonText]="'Editar documento'"
        [buttonIcon]="'fas fa-edit'"
        [buttonIconVisible]="true"
        [buttonDisabled]="!uuid"
        [buttonClass]="'float-right'"
        (buttonEmitter)="openDocumentEditor()"
        buttonId="upload-file"
      >
      </eqp-nebular-button>
      <eqp-field-toggle
        class="mt-2"
        formControlName="flagPublicar"
        nebularLabelPosition="right"
        label="Publicar no portal"
        title="Publicar no portal"
      ></eqp-field-toggle>
    </div>
  </ng-container>
</eqp-nebular-dialog>
