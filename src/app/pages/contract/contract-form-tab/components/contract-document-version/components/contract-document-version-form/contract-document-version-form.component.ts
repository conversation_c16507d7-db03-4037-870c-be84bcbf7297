import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit
} from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { Observable } from 'rxjs'
import { filter, finalize, take } from 'rxjs/operators'
import { ContractDocumentVersionService } from '../../services/contract-document-version.service'
import { ContractDocEditVersionComponent } from '../contract-doc-edit-version/contract-doc-edit-version.component'

@Component({
  selector: 'app-licitation-document-version-form',
  templateUrl: './contract-document-version-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractDocumentVersionFormComponent implements OnInit {
  constructor(
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ContractDocumentVersionFormComponent>,
    private service: ContractDocumentVersionService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    private cd: ChangeDetectorRef,
    private crudService: CrudService
  ) {}

  @Input() uuid: string
  @Input() parentUuid: string
  @Input() contractUuid: string
  @Input() documentModelUuid: string

  loading = false
  model?: FormGroup

  ngOnInit(): void {
    this.model = this.getNewModel()
    if (this.uuid) {
      this.loadForm()
    } else {
      this._loadContent();
    }
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      flagPublicar: ['N'],
      nome: [undefined, Validators.required],
      numero: [],
      conteudo: [],
    })
  }

  loadForm() {
    this.loading = true
    this.service
      .getIndividual(this.contractUuid, this.parentUuid, this.uuid)
      .pipe(
        take(1),
        finalize(() => {
          this.loading = false
          this.cd.markForCheck()
        }),
      )
      .subscribe(res => {
        let dto: any = res.dados
        this.model.patchValue(dto)
      })
  }

  private _loadContent() {
    this.crudService.getSingleData(`licitacao/contrato/${this.contractUuid}/documento/${this.parentUuid}`)
    .pipe(take(1), finalize(() => this.loading  = false))
    .subscribe((res: any) => {
      const data: any = res.dados?.processoDocumentoModelo;
      const content = data.cabecalho + data.conteudo +  data.rodape;
      this.model.get('conteudo').patchValue(content);
      this.model.markAsDirty();
    });
  }

  confirm() {
    this.loading = true
    let req: Observable<any>
    if (this.uuid) {
      req = this.service.put(
        this.contractUuid,
        this.parentUuid,
        this.model.getRawValue(),
        this.uuid,
      )
    } else {
      req = this.service.post(
        this.contractUuid,
        this.parentUuid,
        this.model.getRawValue(),
      )
    }
    req
      .pipe(
        take(1),
        finalize(() => {
          this.loading = false
          this.cd.markForCheck()
        }),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: `Versão ${
            this.uuid ? 'atualizada' : 'cadastrada'
          } com sucesso`,
        })
        if (!this.uuid) {
          this.uuid = res.body.dados.uuid
          this.model.patchValue(res.body.dados)
          this.openDocumentEditor()
        }
        else {
          this.dialogRef.close(true)
        }
      })
  }

  cancel() {
    this.dialogRef.close(null)
  }

  openDocumentEditor() {
    this.dialogService
      .open(ContractDocEditVersionComponent, {
        context: {
          versaoUuid: this.uuid,
          contractUuid: this.contractUuid,
          documentUuid: this.parentUuid,
          nome: this.model.get('nome')?.value,
          numero: this.model.get('numero')?.value,
          flagPublicarPortal: this.model.get('flagPublicar')?.value === 'S',
        },
      })
      .onClose.pipe(
        filter(res => res),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('conteudo').patchValue(res?.conteudo)
          this.model.get('flagPublicar').patchValue(res?.flagPublicar)
          this.model.markAsDirty();
          this.cd.detectChanges();
          if(this.uuid){
            this.confirm();
          }
        }
      })
  }

}
