/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ContractComissionSignatureDialogComponent } from './contract-comission-signature-dialog.component';

describe('ContractComissionSignatureDialogComponent', () => {
  let component: ContractComissionSignatureDialogComponent;
  let fixture: ComponentFixture<ContractComissionSignatureDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ContractComissionSignatureDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractComissionSignatureDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
