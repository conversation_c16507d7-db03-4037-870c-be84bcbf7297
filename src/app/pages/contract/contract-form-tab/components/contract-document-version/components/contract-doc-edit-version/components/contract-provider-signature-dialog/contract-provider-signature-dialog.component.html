<eqp-nebular-dialog
  [dialogTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonIcon]="'fas fa-check'"
  [rightFirstButtonDisabled]="selectedRowKeys.length == 0"
  (rightFirstButtonEmitter)="confirm()"
>
  <ng-container>
    <dx-data-grid
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [selectedRowKeys]="selectedRowKeys"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onSelectionChanged)="onSelectionChanged($event)"
    >
      <dxo-selection
        selectAllMode="allPages"
        showCheckBoxesMode="onClick"
        [mode]="'multiple'"
      ></dxo-selection>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar fornecedor"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        alignment="left"
        dataField="name"
        caption="Nome"
      ></dxi-column>
      <dxi-column
        alignment="left"
        dataField="cpfOrCnpj"
        caption="Documento"
      ></dxi-column>
      <dxi-column
      alignment="left"
      dataField="district"
      caption="Bairro"
      ></dxi-column>
      <dxi-column
        alignment="left"
        dataField="city"
        caption="Cidade"
      ></dxi-column>
      <dxi-column
        alignment="left"
        dataField="siglaState"
        caption="UF"
      ></dxi-column>
      <dxi-column
        alignment="left"
        dataField="cep"
        caption="CEP"
      ></dxi-column>
    </dx-data-grid>
  </ng-container>
</eqp-nebular-dialog>
