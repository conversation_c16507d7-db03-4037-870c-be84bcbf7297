import { Component, Input, OnInit, inject } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { take } from 'rxjs/operators'

@Component({
  selector: 'eqp-contract-provider-signature-dialog',
  templateUrl: './contract-provider-signature-dialog.component.html',
  styleUrls: ['./contract-provider-signature-dialog.component.scss'],
})
export class ContractProviderSignatureDialogComponent implements OnInit {
  constructor(private dialogRef: NbDialogRef<ContractProviderSignatureDialogComponent>, private crudService: CrudService){}

  pageTitle: string
  loading: boolean
  dataSource: DataSource
  selectedRowKeys: string[] = []

  @Input() licitationUuid: string

  ngOnInit(): void {
    this.fetchGrid();
  }

  fetchGrid() {
    this.crudService
      .getSingleData( `licitacao/licitacao/${this.licitationUuid}/assinatura_fornecedor`)
      .pipe(take(1))
      .subscribe(res => {
        this.dataSource = new DataSource({
          store: {
            data: res.dados,
            key: 'uuid',
            type: 'array',
          },
          paginate: true,
          pageSize: 10,
        })
      })
  }


  async confirm() {
    const items = []
    const dataSource: any = await this.dataSource.store().load()
    this.selectedRowKeys.forEach(key => {
      const item = dataSource.find(item => item.uuid === key)
      items.push(item)
    })
    this.dialogRef.close(items)
  }

  cancel() {
    this.dialogRef.close(null)
  }

  onSelectionChanged(event: any) {
    this.selectedRowKeys = event.selectedRowKeys
  }
}
