import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { ResponseDto } from '@common/interfaces/dtos/response-dto';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ContractDocumentVersionService {

  #http = inject(HttpClient)

  getIndividual(contractUuid: string, documentUuid, uuid: string): Observable<ResponseDto<any>> {
    return this.#http.get<ResponseDto<any>>(
      `licitacao/contrato/documento/${documentUuid}/versao/${uuid}`,
      {
        headers: new HttpHeaders(),
      },
    )
  }

  put(contractUuid: string, documentUuid, dto: any, uuid: string): Observable<any> {
    return this.#http.put<ResponseDto<any>>(
      `licitacao/contrato/documento/${documentUuid}/versao/${uuid}`,
      dto,
      {
        headers: new HttpHeaders(),
        observe: 'response',
      },
    )
  }

  post(contractUuid: string, documentUuid, dto: any): Observable<any> {
    return this.#http.post<ResponseDto<any>>(
      `licitacao/contrato/documento/${documentUuid}/versao`,
      dto,
      {
        headers: new HttpHeaders(),
        observe: 'response',
      },
    )
  }

  delete(contractUuid: string, documentUuid, uuid: string): Observable<any> {
    const headers = new HttpHeaders()
    return this.#http.delete<any>(
      `licitacao/contrato/documento/${documentUuid}/versao/${uuid}`,
      {
        headers,
      },
    )
  }

  public mergeFlags(documentUuid: string, versionUuid: string, dto: any = null): Observable<any> {
    return this.#http.put<ResponseDto<any>>(
      `licitacao/contrato/documento/${documentUuid}/versao/${versionUuid}/mesclar_tags`,
      dto,
    )
  }
}
