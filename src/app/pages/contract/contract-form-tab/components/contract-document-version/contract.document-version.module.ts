import { NgModule } from "@angular/core";
import { ContractDocumentVersionFormComponent } from "./components/contract-document-version-form/contract-document-version-form.component";
import { ContractDocumentVersionListComponent } from "./components/contract-document-version-list/contract-document-version-list.component";
import { ContractDocEditVersionComponent } from "./components/contract-doc-edit-version/contract-doc-edit-version.component";
import { CommonModule } from '@angular/common';
import { ContractDocumentVersionRoutingModule } from "./contract-document-version.routing.module";

@NgModule({
   declarations: [
   ],
   imports: [
      ContractDocumentVersionRoutingModule,
   ],
 })
 export class ContractDocumentVersionModule {}
 