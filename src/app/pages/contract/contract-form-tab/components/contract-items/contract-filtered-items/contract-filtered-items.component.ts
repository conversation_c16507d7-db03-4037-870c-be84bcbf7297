import { NbDialogRef } from '@nebular/theme'
import { finalize, take, takeUntil } from 'rxjs/operators'
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { ContractItemService } from '../../../../services/contract-item.service'
import { ProductInterface } from '@pages/shared/interfaces/product'
import { FormBuilder, FormGroup } from '@angular/forms'
import { Subject } from 'rxjs'
import { CellPreparedEvent, EditorPreparingEvent, RowUpdatedEvent, RowValidatingEvent } from 'devextreme/ui/data_grid'

@Component({
  selector: 'eqp-contract-filtered-items',
  templateUrl: './contract-filtered-items.component.html',
  styleUrls: ['./contract-filtered-items.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractFilteredItemsComponent implements OnInit, OnD<PERSON>roy {
  @Input() parentUuid: string

  private unsubs$ = new Subject<void>()
  model: FormGroup
  validateHasItem: boolean = false
  isEditingCell = false

  uri: string
  title: string
  nameKey: string
  codeKey: string
  columnsSearchName: string = 'solicitation'
  gridByType: string = 'SOLICITACAO'
  data: any
  changedItems: any[] = []
  loading: boolean = false
  itens: ProductInterface[] = []
  originalData: any
  controlTypeIsValue: boolean
  itemsUpdated = false
  itemsLoaded = false;

  invalidUpdate: boolean = true

  //formats
  decimalFormat = {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }
  currencyFormat = currencyFormat

  constructor(
    private toastr: ToastrService,
    private builder: FormBuilder,
    private crudService: CrudService,
    private dialogRef: NbDialogRef<ContractFilteredItemsComponent>,
    private service: ContractItemService,
    private cd: ChangeDetectorRef,
  ) { }

  ngOnInit(): void {
    this.model = this.newModel()
    this.fetchGrid()
    this.changesValuesObservable()
    this.setValuesBySearchData('SOLICITACAO')
  }

  private newModel(): FormGroup {
    return this.builder.group({
      entidade: [''],
      fornecedor: [''],
      tipoAto: [''],
      local: [''],
      tipoFiltro: ['SOLICITACAO'],
      uuidFiltro: [''],
      solicitacao: [''],
      requisicaoCompra: [''],
      solicitante: [''],
      numeroContrato: [''],
      exercicioContrato: [''],
      exercicioModalidade: [''],
      numeroModalidade: [''],
      modalidade: [''],
    })
  }

  fetchGrid() {
    this.loadChange(true)
    this.crudService
      .getSingleData<any>(`licitacao/contrato/${this.parentUuid}`)
      .pipe(take(1), finalize(() => this.loadChange(false)))
      .subscribe(res => {
        this.itens = res.dados
        this.patchModel(this.itens)
      })
  }

  private changesValuesObservable() {
    this.model.get('tipoFiltro').valueChanges
    .pipe(takeUntil(this.unsubs$))
    .subscribe(res => {
      this.gridByType = res
      this.setValuesBySearchData(res)
      this.model.get('uuidFiltro').reset()
      this.cd.markForCheck()
    })
  }

  onRowUpdated() {
    this.itemsUpdated = true
    this.cd.markForCheck()
  }

  calculateValueRequested = (item: any) => {
    if(this.controlTypeIsValue){
      return item?.valorDesejado
    } else {
      return ''
    }
  }

  private validateBalanceControlTypeEdit(typeCode: number, newData: Object) {
    if (typeCode == 1 && 'quantidadeDesejada' in newData) {
      this.toastr.send({
        info: true,
        message: 'Quantidade desejada não é editável quando o controle de saldo for por valor'
      })
      return false
    }
    if (typeCode == 0 && 'valorDesejado' in newData) {
      this.toastr.send({
        info: true,
        message: 'Valor desejado não é editável quando o controle de saldo for por quantidade'
      })
      return false
    }
    return true
  }

  onRowValidating(event: RowValidatingEvent) {
    this.isEditingCell = false
    const quantidadeDisponivel = event.oldData?.quantidadeDisponivel
    const valorDisponivel = event.oldData?.valorDisponivel
    const balanceControlType = event.oldData?.tipoControleSaldo?.codigo

    if (!this.validateBalanceControlTypeEdit(balanceControlType, event.newData)) {
      event.component.cancelEditData()
    }

    if (balanceControlType == 0) {
      const newRequest = event?.newData?.quantidadeDesejada

      if (newRequest == 0 || newRequest == null) {
        this.itemsUpdated = false
      }

      if (newRequest > quantidadeDisponivel) {
        event.component.cancelEditData()
        this.toastr.send({
          success: false,
          info: true,
          message:
            'A quantidade requisitada não deve exceder a quantidade disponível',
        })
      }
    } else {
      const newRequest = event?.newData?.valorDesejado

      if (newRequest == 0 || newRequest == null) {
        this.itemsUpdated = false
      }

      if (newRequest > valorDisponivel) {
        event.component.cancelEditData()
        this.toastr.send({
          success: false,
          info: true,
          message: 'O valor requisitado não deve exceder o valor disponível',
        })
      }
    }
  }

  onCellPrepared(e: CellPreparedEvent) {
    if (e.rowType != 'data') {
      return
    }
    if (
      e.column.dataField === 'quantidadeDesejada' &&
      e?.data?.tipoControleSaldo.codigo === 0
    ) {
      e.cellElement.classList.add('cell-highlighted')
    }
    else if (
      e.column.dataField === 'valorDesejado' &&
      e?.data?.tipoControleSaldo.codigo === 1
    ) {
      e.cellElement.classList.add('cell-highlighted')
    }
  }

  onEditorPreparing(e: EditorPreparingEvent) {
    if (e.parentType == 'dataRow' && (e.dataField == 'valorDesejado' || e.dataField == 'quantidadeDesejada' )) {  
      e.editorOptions.onFocusOut = () => {
        this.isEditingCell = false
      }
    }  
  }

  setValuesBySearchData(type) {
    switch (type) {
      case 'SOLICITACAO':
        this.uri = 'solicitacao'
        this.title = 'Solicição'
        this.columnsSearchName = 'solicitation'
        this.nameKey = 'exercicio.exercicio'
        this.codeKey = 'numero'
        break
      case 'SOLICITANTE':
        this.uri = 'solicitante'
        this.title = 'Solicitante'
        this.columnsSearchName = 'requester'
        this.nameKey = 'nome'
        this.codeKey = 'codigo'
        break
      case 'LOCAL':
        this.uri = 'local'
        this.title = 'Local'
        this.columnsSearchName = 'local'
        this.nameKey = 'nome'
        this.codeKey = 'codigo'
        break
      case 'REQUISICAO_COMPRA':
        this.uri = 'requisicao_compra'
        this.title = 'Requisição de compra'
        this.columnsSearchName = 'Nomes das colunas para REQUISICAO_COMPRA'
        this.nameKey = 'name'
        this.codeKey = 'code'
        break
    }
  }

  private patchModel(data) {
    this.model.patchValue({
      entidade: data?.licitacao?.entidadeOrigem?.nome,
      tipoAto: data.tipoAtoContratual.nome,
      fornecedor: data.fornecedor,
      local: data.local,
      numeroContrato: data.numero,
      exercicioContrato: data.exercicio,
      exercicioModalidade: data.licitacao?.exercicio.exercicio,
      modalidade: data.licitacao?.tipoModalidadeLicitacaoTce.nome,
      numeroModalidade: data.licitacao?.numero,
    })
  }

  combineSolicitacao(data: any) {
    return `${data.solicitacaoNumero} / ${data.solicitacaoExercicio} - ${data.solicitacaoCodigoEntidade}`
  }

  validateDesiredQuantity(data: any): any {
    if (data.tipoControleSaldo.codigo == 1) {
      return data.quantidadeDisponivel
    } else {
      return data.quantidadeDesejada
    }
  }

  validateAllowedValue(data: any): any {
    if (data.tipoControleSaldo.codigo == 0) {
      return data.valorDisponivel
    } else {
      return data.valorDesejado
    }
  }

  onEditingStart(e) {
    this.isEditingCell = true
  }

  resetValues() {
    this.setGridValues(0)
    this.cd.markForCheck()
  }

  setGridValues(value: number) {
    this.data = this.data.map(batch => ({
      ...batch,
      items: batch.items.map(item => {
        if (item.tipoControleSaldo.codigo == 0) {
          item.quantidadeDesejada = value
        } else {
          item.valorDesejado = value
        }
        return item
      })
    }))
  }

  prepare() {
    return this.data.flatMap(lote => lote.items
      .filter(item => {
        if (item.tipoControleSaldo.codigo == 0) {
          return item.quantidadeDesejada > 0
        }
        return item.valorDesejado > 0
      })
      .map(item => ({
        uuid: item.uuid,
        valorDesejado: item.valorDesejado || 0,
        quantidadeDesejada: item.quantidadeDesejada || 1,
        solicitacaoItem: item?.processoLoteItem
      })),
    )
  }

  confirm() {
    const dto = this.prepare()
    if (dto.length == 0) {
      return
    }
    this.loadChange(true)
    this.service
      .postItem(this.parentUuid, dto)
      .pipe(take(1), finalize(() => this.loadChange(false)))
      .subscribe(res => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Itens carregados com sucesso!`,
          success: true,
        })
        this.dialogRef.close(res)
      })
  }

  cancel() {
    this.dialogRef.close()
  }

  loadItems() {
    this.loadChange(true)
    const dto = this.model.getRawValue()
    this.crudService
      .getSingleData<any>(
        `licitacao/contrato/${this.parentUuid}/item_carregar`,
        {
          uuidFiltro: dto.uuidFiltro.uuid,
          tipoFiltro: dto['tipoFiltro'],
        },
      )
      .pipe(take(1), finalize(() => this.loadChange(false)))
      .subscribe(res => {
        const response = res?.dados.sort((a, b) => a.lote - b.lote)

        response.forEach(batch => {
          batch.items.sort((a, b) => a.item - b.item)
        })

        if(response.length > 0){
            response.forEach((batch: any) => {
              batch.items.forEach((item: any) => {
                item.idSolicitacaoItem = `${item.item}-${item.solicitacaoNumero}`;
                if (item.tipoControleSaldo.codigo == 1) {
                  item.quantidadeDesejada = undefined
                  item.valorDesejado = item.valorDisponivel;
                } else {
                  item.valorDesejado = undefined
                }
              })
            });
        }

        this.data = response;
        this.itemsLoaded = true;
        this.controlTypeIsValue = response[0]?.items[0]?.tipoControleSaldo.codigo === 1
        this.validateHasItem = true
      })
  }

  clear() {
    this.loadItems()
  }

  ngOnDestroy(): void {
    this.unsubs$.next()
    this.unsubs$.complete()
  }

  private loadChange(loading: boolean) {
    this.loading = loading
    this.cd.markForCheck()
  }
}
