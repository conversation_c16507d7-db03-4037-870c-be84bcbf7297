<dx-data-grid
  [dataSource]="dataSource"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="false"
  [remoteOperations]="true"
  [keyExpr]="'uuidUnique'"
  (onToolbarPreparing)="onToolbarPreparing($event)"
>
  <dxo-paging [pageSize]="10"></dxo-paging>

  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-sorting mode="multiple"></dxo-sorting>
  <dxo-filter-row [visible]="true"></dxo-filter-row>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel
    [visible]="false"
    [emptyPanelText]="''"
  ></dxo-group-panel>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar..."
  ></dxo-search-panel>

  <dxo-editing
    mode="cell"
    [allowUpdating]="true"
    [allowDeleting]="false"
    [allowAdding]="exercise == 'CURRENT'"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column
    alignment="left"
    dataField="previsaoInicialDespesaFonte.codigo"
    dataType="number"
    caption="Conta despesa"
    [width]="120"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    alignment="left"
    dataField="grupoFonte.nome"
    caption="Grupo fonte"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    alignment="left"
    dataField="planoDespesa.codigoNome"
    caption="Natureza de despesa"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    alignment="left"
    dataField="quantidade"
    dataType="number"
    caption="Quantidade"
    [allowEditing]="allowEditQuantity"
    [cssClass]="allowEditQuantity ? 'cell-highlighted' : ''"
    cellTemplate="quantityTemplate"
    [width]="120"
  ></dxi-column>

  <dxi-column
    dataField="preco"
    caption="Valor unitário"
    cellTemplate="unitValueTemplate"
    [width]="120"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    caption="Valor total"
    alignment="right"
    [width]="120"
    cellTemplate="totalValueTemplate"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    alignment="left"
    dataField="uuid"
    caption=""
    [width]="80"
    [allowFiltering]="false"
    cellTemplate="acaoColumn"
  ></dxi-column>

  <div *dxTemplate="let data of 'quantityTemplate'">
    {{data.value | number: '1.4-4'}}
  </div>

  <div *dxTemplate="let data of 'unitValueTemplate'">
    {{data.value | currency: 'BRL'}}
  </div>

  <div *dxTemplate="let data of 'totalValueTemplate'">
    {{(data.data.quantidade * data.data.preco) | currency: 'BRL'}}
  </div>

  <div *dxTemplate="let data of 'acaoColumn'">
    <div
      class="w-100 d-flex justify-content-center"
      style="gap: 0.5rem"
      *ngIf="exercise == 'CURRENT' && !allowEditQuantity"
    >
      <a
        title="Editar"
        (click)="edit(data)"
        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
      >
      </a>
      <a
        title="Remover"
        (click)="remove(data.value)"
        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
      >
      </a>
    </div>
  </div>
</dx-data-grid>