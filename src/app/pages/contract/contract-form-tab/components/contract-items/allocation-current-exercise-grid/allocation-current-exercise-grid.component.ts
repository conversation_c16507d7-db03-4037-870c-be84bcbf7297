import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, inject, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { ToolbarPreparingEvent } from 'devextreme/ui/data_grid';

@Component({
  selector: 'app-allocation-current-exercise-grid',
  templateUrl: './allocation-current-exercise-grid.component.html',
  styleUrls: ['./allocation-current-exercise-grid.component.scss'],
})
export class AllocationCurrentExerciseGridComponent implements OnChanges {

  @Input() @Output() dataSource = []
  @Input() exercise: 'CURRENT' | 'OTHERS'

  @Output() editItemEvent = new EventEmitter<any>()
  @Output() removeItemEvent = new EventEmitter<string>()

  allowEditQuantity = false

  constructor(
    private _toastr: ToastrService,
  ) {}

  ngOnChanges(): void {
    if (this.dataSource?.length > 0) {
      this.allowEditQuantity = this.exercise == 'CURRENT' && !this.dataSource[0].uuid
    }
  }

  onToolbarPreparing(event: ToolbarPreparingEvent) {
    if (this.exercise == 'CURRENT') {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Adicionar'
      event.toolbarOptions.items[0].options.hint = 'Gerar adicionais de itens'
      event.toolbarOptions.items[0].options.onClick = () => this.edit()
    }
  }

  edit(data?: any) {
    if (!data && !this.allowEditQuantity) {
      this._toastr.send({
        error: true,
        message: 'Não é permitido adicionar novas dotaçãoes depois de salvar'
      })
    }
    else {
      this.editItemEvent.emit(data)
    }
  }

  remove(uuid: string) {
    this.removeItemEvent.emit(uuid)
  }
}
