import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { ContractItemService } from '@pages/contract/services/contract-item.service'
import { ContractItemDialogComponent } from '../contract-item-dialog/contract-item-dialog.component'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import { take, finalize, takeUntil } from 'rxjs/operators'
import { CrudService } from '@common/services/crud.service'
import { DefaultColumnSearchInterface } from '@pages/shared/interfaces/default-search'
import { Subject } from 'rxjs'
import { SelectDto } from '@core/interfaces/select-dto'
import { UserDataService } from '@guards/services/user-data.service'

@Component({
  selector: 'eqp-edit-allocation-dialog',
  templateUrl: './edit-allocation-dialog.component.html',
})
export class EditAllocationDialogComponent implements OnInit, OnDestroy {
  model: FormGroup

  @Input() parentUuid: string
  @Input() uuidItem: string
  @Input() uuid: string
  @Input() data: any
  @Input() unitaryValue: any

  currencyFormat = currencyFormat
  loading: boolean = false
  disabledEditDelete: boolean = false
  sourceGroupData: SelectDto[] = []
  expenseNatureData = []

  private readonly _unsub$ = new Subject<void>()

  constructor(
    private toastr: ToastrService,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ContractItemDialogComponent>,
    private service: ContractItemService,
    private crudService: CrudService,
    private _userService: UserDataService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    if (this.data) {
      this.patchValue()
    } else {
      this.model.get('unitario').patchValue(this.unitaryValue)
    }
    this._loadSourceGroup()
    this._expenseHandler()
    this._calculateTotalValue()
  }

  ngOnDestroy(): void {
    this._unsub$.next()
    this._unsub$.complete()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [''],
      total: [''],
      quantidade: [''],
      unitario: [''],
      naturezaDespesa: [''],
      grupoFonte: [''],
      despesa: [''],
    })
  }

  patchValue() {
    const expense: any = {
      uuid: this.data.uuid,
      code: this.data.previsaoInicialDespesaFonte.codigo,
      expensePlanName: this.data.planoDespesa.nome,
    }
    this.model.patchValue({
      uuid: this.data.uuid,
      total: this.data.total,
      quantidade: this.data.quantidade,
      unitario: this.data.preco,
      naturezaDespesa: this.data.planoDespesa.codigoNome,
      grupoFonte: this.data.grupoFonte.uuid,
      despesa: expense,
    })
  }

  private _loadSourceGroup() {
    this.loading = true
    this.crudService
      .getSingleData<SelectDto>(`licitacao/contrato/grupo_fonte`)
      .pipe(take(1), finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.sourceGroupData = res.data
      })
  }

  private _expenseHandler() {
    this.model.get('despesa').valueChanges
      .pipe(takeUntil(this._unsub$))
      .subscribe(value => {
        this.model.get('naturezaDespesa').reset()
        this.expenseNatureData = []
        if (value?.uuid) {
          this.crudService.getSingleData<SelectDto>(
            `/licitacao/contrato/natureza_despesa/${value.uuid}`,
          )
            .pipe(take(1))
            .subscribe(res => this.expenseNatureData = res.dados)
        }
      })
  }

  private _calculateTotalValue() {
    this.model.get('quantidade').valueChanges
      .pipe(takeUntil(this._unsub$))
      .subscribe(value => {
        this.model.get('total').patchValue(value * this.model.get('unitario').value)
      })
  }

  get despesaValue() {
    return this.model.get('despesa').value
  }

  cancel() {
    this.dialogRef.close()
  }

  private _prepare(): any {
    const dto = this.model.getRawValue()
    if (this.data) {
      return {
        ...this.data,
        quantidade: dto.quantidade
      }
    }
    const selectedSourceGroup = this.sourceGroupData.find(item => item.uuid == dto.grupoFonte)
    const selectedExpensePlan = this.expenseNatureData.find(item => item.uuid == dto.naturezaDespesa)
    const allowEdit = dto.despesa.exercicioUuid == this._userService.userData.exercicioUuid ? 'S' : 'N'
    return {
      grupoFonte: selectedSourceGroup,
      planoDespesa: {
        uuid: selectedExpensePlan.uuid,
        codigoNome: selectedExpensePlan.codigoNome,
      },
      permissaoEditar: allowEdit,
      previsaoInicialDespesaFonte: {
        uuid: dto.despesa.uuid,
        codigo: dto.despesa.code,
        exercicioUuid: dto.despesa.exerciseUuid  
      },
      quantidade: +dto.quantidade,
      preco: dto.unitario,
    }
  }

  putItem(dto) {
    this.service.putItemAllocation(this.parentUuid, this.uuidItem, this.uuid, dto)
      .pipe(take(1), finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: `Dotação atualizada com sucesso.`,
        })
        this.dialogRef.close(res)
      })
  }

  confirm() {
    this.loading = true
    const dto = this._prepare()
    if (this.data) {
      this.putItem(dto)
    } else {
      this.dialogRef.close(dto)
    }
  }

  expenseFilter = (dataSource: any[]) => {
    return dataSource
      .filter(item => item.exerciseUuid == this._userService.userData.exercicioUuid)
  }

  get expenseAccountUri() {
    return `licitacao/contrato/${this.parentUuid}/item/${this.uuidItem}/previsao_inicial_despesa`
  }

  get expenseAccountColumns(): DefaultColumnSearchInterface[] {
    return [
      {
        caption: 'Código',
        dataField: 'code',
        align: 'left',
      },
      {
        caption: 'Nome',
        dataField: 'expensePlanName',
      },
      {
        caption: 'Órgão',
        dataField: 'organCode',
        align: 'left',
      },
      {
        caption: 'Unidade',
        dataField: 'unityCode',
        align: 'left',
      },
      {
        caption: 'Função',
        dataField: 'functionCode',
        align: 'left',
      },
      {
        caption: 'Subfunção',
        dataField: 'functionSubCode',
        align: 'left',
      },
      {
        caption: 'Projeto ou atividade',
        dataField: 'projectName',
      },
      {
        caption: 'Natureza',
        dataField: 'expensePlanCode',
        align: 'left',
      },
      {
        caption: 'Fonte recurso',
        dataField: 'sourceCode',
        align: 'left',
      },
    ]
  }
}
