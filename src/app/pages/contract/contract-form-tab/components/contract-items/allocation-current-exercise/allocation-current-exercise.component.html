<eqp-nebular-dialog
  dialogTitle="Dotação"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  dialogSize="extra-large"
>
  <nb-tabset>
    <nb-tab tabTitle="Exercício Atual">
      <app-allocation-current-exercise-grid
        [dataSource]="filteredDataCurrentExercise"
        [exercise]="'CURRENT'"
        (editItemEvent)="edit($event)"
        (removeItemEvent)="remove($event)"
      ></app-allocation-current-exercise-grid>
      <div class="d-flex flex-wrap justify-content-end mt-3" style="gap: 0.5rem;">
        <button
          type="button"
          class="btn btn-success"
          (click)="relinkItems()"
          [disabled]="enableSaveButton || loading"
        >Refazer vinculação Itens X Dotação</button>
        <button
          type="button"
          class="btn btn-success"
          (click)="save()"
          [disabled]="!enableSaveButton || filteredDataCurrentExercise.length == 0 || loading"
        >Salvar carregamento</button>
      </div>
    </nb-tab>
    <nb-tab tabTitle="Outros exercícios">
      <app-allocation-current-exercise-grid
        [dataSource]="filteredDataAnotherExercise"
        [exercise]="'OTHERS'"
      ></app-allocation-current-exercise-grid>
    </nb-tab>
  </nb-tabset>
</eqp-nebular-dialog>
