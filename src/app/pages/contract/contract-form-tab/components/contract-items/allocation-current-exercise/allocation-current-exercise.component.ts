import { Component, Input, OnInit } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService, NbDialogRef } from '@nebular/theme'
import { ContractItemService } from '@pages/contract/services/contract-item.service'
import { ContractFilteredItemsComponent } from '../contract-filtered-items/contract-filtered-items.component'
import { filter, finalize, take } from 'rxjs/operators'
import { EditAllocationDialogComponent } from '../edit-allocation-dialog/edit-allocation-dialog.component'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import { UserDataService } from '@guards/services/user-data.service'

@Component({
  selector: 'eqp-allocation-current-exercise',
  templateUrl: './allocation-current-exercise.component.html',
})
export class AllocationCurrentExerciseComponent implements OnInit {
  @Input() uuidItem: string
  @Input() parentUuid: string

  quantityLoads = 0
  enableSaveButton: boolean
  data = []
  filteredDataCurrentExercise = []
  filteredDataAnotherExercise = []
  uuidUnique: string

  currencyFormat = currencyFormat
  decimalFormat = {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }

  constructor(
    private toastr: ToastrService,
    private crudService: CrudService,
    private dialogService: NbDialogService,
    private dialogRef: NbDialogRef<ContractFilteredItemsComponent>,
    private service: ContractItemService,
    private userService: UserDataService,
  ) {}

  ngOnInit(): void {
    this.fetchGrid()
  }

  fetchGrid() {
    this.quantityLoads++
    this.filteredDataCurrentExercise = []
    this.filteredDataAnotherExercise = []
    this.crudService
      .getSingleData<any>(
        `licitacao/contrato/${this.parentUuid}/item/${this.uuidItem}/dotacao_atual`,
      )
      .pipe(take(1), finalize(() => this.quantityLoads--))
      .subscribe(res => {
        this.data = res.dados
          .sort((a, b) => a.previsaoInicialDespesaFonte.codigo - b.previsaoInicialDespesaFonte.codigo)
        this.data.forEach((item, index) => {
          item.uuidUnique = index + 1
          if (item.previsaoInicialDespesaFonte.exercicioUuid == this.userService.userData.exercicioUuid) {
            this.filteredDataCurrentExercise.push(item)
          } else {
            this.filteredDataAnotherExercise.push(item)
          }
        })
        this.enableSaveButton = !this.filteredDataCurrentExercise[0].uuid
      })
  }

  cancel() {
    this.dialogRef.close()
  }

  private _prepare() {
    return this.filteredDataCurrentExercise.map(item => ({
      ...item,
      previsaoInicialDespesaFonte: {
        uuid: item.previsaoInicialDespesaFonte.uuid,
        exercicioUuid: item.previsaoInicialDespesaFonte.exercicioUuid,
      },
      grupoFonte: {
        uuid: item.grupoFonte.uuid
      },
      planoDespesa: {
        uuid: item.planoDespesa.uuid
      },
    }))
  }

  save() {
    this.quantityLoads++
    this.service
      .postLoading(this.parentUuid, this.uuidItem, this._prepare())
      .pipe(take(1), finalize(() => this.quantityLoads--))
      .subscribe(() => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Carregamento salvo com sucesso!`,
          success: true,
        })
        this.fetchGrid()
      })
  }

  relinkItems() {
    this.quantityLoads++
    this.service
      .deleteAllocationBatch(this.parentUuid, this.uuidItem)
      .pipe(take(1), finalize(() => this.quantityLoads--))
      .subscribe({next: () => {
        this.toastr.send({
          success: true,
          message: `A vinculação foi refeita com sucesso.`,
        })
        this.fetchGrid()
      }})
  }

  edit(value?: any) {
    this.dialogService.open(EditAllocationDialogComponent, {
      context: {
        data: value?.data,
        uuid: value?.data.uuid,
        parentUuid: this.parentUuid,
        uuidItem: this.uuidItem,
        unitaryValue: this.data[0].preco,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose
      .pipe(take(1), filter(res => res))
      .subscribe(res => this._updateGrid(!value, res))
  }

  private _updateGrid(addItem: boolean, item) {
    if (addItem) {
      const l = this.filteredDataCurrentExercise.length
      const nextId = this.filteredDataCurrentExercise[l - 1].uuidUnique + 1
      this.filteredDataCurrentExercise.push({
        ...item,
        uuidUnique: nextId
      })
    }
    else {
      this.fetchGrid()
    }
  }

  remove(data: any) {
    this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose
      .pipe(take(1), filter(res => res == 'S'))
      .subscribe(() => {
        this.quantityLoads++
        this.service
          .deleteAllocation(this.parentUuid, this.uuidItem, data)
          .pipe(take(1), finalize(() => this.quantityLoads--))
          .subscribe({next: res => {
            this.toastr.send({
              success: true,
              message: `Dotação removida com sucesso.`,
            })
            this.fetchGrid()
          }})
    })
  }

  get loading() {
    return !!this.quantityLoads
  }
}
