import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { ContractAdditionService } from '@pages/contract/services/contract-addition.service'
import { ContractItemService } from '@pages/contract/services/contract-item.service'
import { Observable } from 'rxjs'
import { debounceTime, finalize, take } from 'rxjs/operators'

@Component({
  selector: 'eqp-contract-item-dialog',
  templateUrl: './contract-item-dialog.component.html',
  styleUrls: ['./contract-item-dialog.component.scss'],
})
export class ContractItemDialogComponent implements OnInit {
  model: FormGroup
  @Input() parentUuid: string
  @Input() itemUuid: string
  @Input() uuid: string

  loading: boolean = false
  data: any
  enableButtonSend: boolean = true
  balanceControlType?: 'value' | 'quantity'

  constructor(
    private toastr: ToastrService,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ContractItemDialogComponent>,
    private service: ContractItemService,
    private itemContractService: ContractItemService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.fetchData()
    this.initialObservable()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      deQuantidade: [],
      deValor: [],
      deTotal: [],
      paraQuantidade: [],
      paraValor: [],
      paraTotal: [],
    })
  }

  fetchData() {
    this.loading = true
    this.itemContractService
      .getItemIndividual(this.parentUuid, this.uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.data = res.dados
        this.patchValuesForm(res.dados)
      })
  }

  initialObservable() {
    this.model.get('paraQuantidade').valueChanges.subscribe(res => {
      this.model.patchValue({
        paraTotal: res * this.model.get('paraValor').value,
      })
    })

    this.model.get('paraValor').valueChanges.subscribe(res => {
      this.model.patchValue({
        paraTotal: res * this.model.get('paraQuantidade').value,
      })
    })
  }

  hasChangesAfterLoad(): boolean {
    return this.model.dirty
  }

  patchValuesForm(data) {
    this.model.patchValue({
      deQuantidade: data.quantidade,
      deValor: data.preco,
      deTotal: data.quantidade * data.preco,
      paraQuantidade: data.quantidade,
      paraValor: data.preco,
      paraTotal: data.quantidade * data.preco,
    })
    if (this.data.licitacaoResultado.ProcessoLoteItem.tipoControleSaldo.codigo == 0) {
      this.balanceControlType = 'quantity'
    } else {
      this.balanceControlType = 'value'
    }
  }

  cancel() {
    this.dialogRef.close()
  }

  public get toQuantity() {
    return this.model.get('paraQuantidade').value
  }

  public get toValue() {
    return this.model.get('paraValor').value
  }

  confirm() {
    this.loading = true
    const dto = {
      uuid: this.uuid,
      solicitacaoItem: this.data.solicitacaoItem.id,
      valorDesejado: +this.model.get('paraValor').value,
      quantidadeDesejada: +this.model.get('paraQuantidade').value,
    }
    this.itemContractService
      .putItem(this.parentUuid, dto, this.uuid)
      .pipe(take(1), finalize(() => this.loading = false))
      .subscribe(res => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Item salvo com sucesso!`,
          success: true,
        })
        this.dialogRef.close(res)
      })
  }
}
