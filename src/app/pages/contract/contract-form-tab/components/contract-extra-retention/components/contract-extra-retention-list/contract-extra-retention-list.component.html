<div class="mt-3">
  <dx-data-grid id="contract-extra-retention-list" [allowColumnResizing]="true" [columnAutoWidth]="true"
    [dataSource]="dataSource" [showColumnLines]="false" [showRowLines]="false" [showBorders]="false"
    [rowAlternationEnabled]="true" [wordWrapEnabled]="true" [loadPanel]="false" [columnHidingEnabled]="true"
    [remoteOperations]="true" keyExpr="uuid" (onToolbarPreparing)="onToolbarPreparing($event)">
    <dxo-export [enabled]="true" [excelWrapTextEnabled]="true" [excelFilterEnabled]="true"
      [fileName]="pageTitle"></dxo-export>

    <dxo-paging [pageSize]="10"></dxo-paging>

    <dxo-pager [showInfo]="true" [showNavigationButtons]="true" [showPageSizeSelector]="false">
    </dxo-pager>

    <dxo-header-filter [visible]="false"> </dxo-header-filter>
    <dxo-filter-row [visible]="true"></dxo-filter-row>

    <dxo-sorting mode="multiple"></dxo-sorting>

    <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

    <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

    <dxo-search-panel [visible]="false" placeholder="Buscar retenção extra"></dxo-search-panel>

    <dxo-editing mode="form" [allowUpdating]="false" [allowDeleting]="false" [allowAdding]="true" [useIcons]="true">
    </dxo-editing>

    <dxi-column
      alignment="left"
      dataField="planoContabilExtra.planoContabil.codigoReduzido"
      caption="Conta contábil"
      [allowSorting]="false"
      [allowFiltering]="false"
    ></dxi-column>
    <dxi-column
      dataField="planoContabilExtra.planoContabil.codigo"
      caption=""
      [allowSorting]="false"
      [allowFiltering]="false"
    ></dxi-column>
    <dxi-column
      dataField="planoContabilExtra.planoContabil.nome"
      caption=""
      [allowSorting]="false"
      [allowFiltering]="false"
    ></dxi-column>
    <!-- <dxi-column
      alignment="left"
      dataField="fonteRecurso.codigoEhNome"
      caption="Fonte"
      [allowSorting]="false"
      [allowFiltering]="false"
    ></dxi-column> -->
    <dxi-column
      dataField="fonteRecurso.uuid"
      caption="Fonte"
      cellTemplate="resourceSourceTemplate"
      [allowSorting]="false"
      [allowSearch]="false"
    >
      <dxo-lookup
        [dataSource]="resourceSourceDataSource"
        displayExpr="nome"
        valueExpr="uuid"
      >
      </dxo-lookup>
    </dxi-column>
    <dxi-column
      dataField="fornecedor.uuid"
      caption="Credor"
      cellTemplate="providerTemplate"
      [allowSorting]="false"
      [allowSearch]="false"
    >
      <dxo-lookup
        [dataSource]="providerDataSource"
        displayExpr="nome"
        valueExpr="uuid"
      >
      </dxo-lookup>
    </dxi-column>
    <dxi-column
      alignment="left"
      dataField="planoContabilExtra.tipoRetencao.nome"
      caption="Tipo de retenção"
      [allowSorting]="false"
      [allowFiltering]="false"
    ></dxi-column>
    <dxi-column
      alignment="left"
      dataField="percentualRetencao"
      caption="Retenção %"
      cellTemplate="retentionPercentage"
    ></dxi-column>
    <div *dxTemplate="let data of 'retentionPercentage'">
      {{data.value/100 | percent:'1.2-2' }}
    </div>
    <dxi-column
      alignment="left"
      dataField="percentualBaseCalculo"
      caption="Base de cálculo %" 
      cellTemplate="baseCalculationPercentage"
    ></dxi-column>
    
    <dxi-column
      dataField="uuid"
      caption=""
      [width]="80"
      [allowFiltering]="false"
      [allowEditing]="false"
      [allowSorting]="false" cellTemplate="acaoColumn"
    ></dxi-column>

    <div *dxTemplate="let data of 'baseCalculationPercentage'">
      {{data.value/100 | percent:'1.2-2' }}
    </div>

    <div *dxTemplate="let data of 'providerTemplate'">
      {{ provider(data) }}
    </div>
    <div *dxTemplate="let data of 'resourceSourceTemplate'">
      {{ resourceSource(data) }}
    </div>

    <div *dxTemplate="let data of 'acaoColumn'">
      <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
        <a title="Editar" (click)="edit(data.data)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid">
        </a>
        <a *ngIf="nivelPermissao === 'FULL'" title="Remover" (click)="remove(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid">
        </a>
      </div>
    </div>
  </dx-data-grid>
</div>
