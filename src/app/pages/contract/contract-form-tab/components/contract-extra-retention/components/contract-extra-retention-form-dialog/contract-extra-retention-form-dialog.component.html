<eqp-nebular-dialog dialogTitle="Inserir Retenção Extra" [rightSecondButtonVisible]="true"
  [rightSecondButtonIconVisible]="true" [rightSecondButtonIcon]="'fas fa-eraser'" [rightSecondButtonText]="'Limpar'"
  (rightSecondButtonEmitter)="reset()" [bottomLeftButtonText]="'Voltar'" [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()" [rightFirstButtonVisible]="true" [rightFirstButtonIconVisible]="true"
  [rightFirstButtonText]="'Confirmar'" [rightFirstButtonIcon]="'fas fa-check'"
  [rightFirstButtonId]="'submit-extra-retention'" (rightFirstButtonEmitter)="confirm()"
  [rightFirstButtonDisabled]="model.pristine || model.invalid">
  <ng-container [formGroup]="model">
    <eqp-loading *ngIf="loading"></eqp-loading>
    <div class="row">
      <div class="col col-12 col-md-6">
        <eqp-search-field
          label="Contábil *"
          formControlName="planoContabilExtra"
          codeKey="planoContabil.codigoReduzido"
          nameKey="planoContabil.nome"
          [multipleNames]="['planoContabil.codigo', 'planoContabil.nome']"
          dialogTitle="Plano contábil"
          [uri]="baseUrl + '/plano_contabil_extra'"
          [searchPanelVisible]="false"
          [disabledCodeInput]="true"
          searchColumnsType="accountPlanExtraColumns"
          [returnAllData]="true"
        ></eqp-search-field>
      </div>
      <div class="col col-12 col-md-6">
        <eqp-search-field
          label="Fonte de recurso *"
          formControlName="fonteRecurso"
          dialogTitle="Fonte de Recurso"
          [uri]="baseUrl + '/fonte_recurso'"
          [filter]="['flagRetencao', '=', 'S']"
          searchColumnsType="resourceSourceColumns"
          [returnAllData]="true"
        ></eqp-search-field>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col col-12 col-md-6">
        <eqp-search-field
          label="Credor *"
          formControlName="fornecedor"
          dialogTitle="Fornecedor"
          [uri]="baseUrl + '/retencao_extra/fornecedor'"
          searchColumnsType="programColumns"
          [returnAllData]="true"
        ></eqp-search-field>
      </div>
      <div class="col col-12 col-md-3">
        <eqp-nebular-input [style]="'currency'" [size]="'small'" [shape]="'rectangle'"
          formControlName="percentualRetencao"
          [options]="{ precision: 2, prefix: '', suffix: '%', thousands: '.', decimal: ',' }" name="percentualRetencao"
          label="Retenção %" placeholder="0,00%"></eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-3">
        <eqp-nebular-input [style]="'currency'" [size]="'small'" [shape]="'rectangle'"
          formControlName="percentualBaseCalculo" placeholder="0,00%"
          [options]="{ precision: 2, prefix: '', suffix: '%', thousands: '.', decimal: ',' }"
          name="percentualBaseCalculo" label="Base de cálculo %"></eqp-nebular-input>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col col-12">
        <eqp-nebular-checkbox
          label="Calcular pela tabela de faixas"
          formControlName="calcularUsandoTabelaFaixa"
          [disabled]="!tableFlagEnable"
        ></eqp-nebular-checkbox>
      </div>
    </div>
  </ng-container>
</eqp-nebular-dialog>
