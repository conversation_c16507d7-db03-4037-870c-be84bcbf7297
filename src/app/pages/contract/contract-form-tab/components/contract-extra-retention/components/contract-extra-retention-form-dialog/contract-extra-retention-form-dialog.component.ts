import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { ExtraRetentionInterface } from '@pages/contract/interfaces/contract-extra-retention'
import { ContractExtraRetentionService } from '@pages/contract/services/contract-extra-retention.service'
import { MenuService } from '@pages/menu.service'
import { Subject, combineLatest } from 'rxjs'
import { finalize, take, takeUntil } from 'rxjs/operators'

@Component({
  selector: 'eqp-contract-extra-retention-form-dialog',
  templateUrl: './contract-extra-retention-form-dialog.component.html',
  styleUrls: ['./contract-extra-retention-form-dialog.component.scss'],
})
export class ContractExtraRetentionFormDialogComponent
  extends BaseTelasComponent implements OnInit, OnDestroy
{
  @Input() parentUuid: string
  @Input() retention: ExtraRetentionInterface

  loading: boolean = false
  baseUrl = 'licitacao/contrato'
  tableFlagEnable = false

  model: FormGroup

  private unsub$ = new Subject<void>()

  constructor(
    public menuService: MenuService,
    public router: Router,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ContractExtraRetentionFormDialogComponent>,
    private service: ContractExtraRetentionService,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('/contrato')
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadHandler()
    if (this.retention?.uuid) {
      this.loadForm()
    }
  }

  ngOnDestroy(): void {
    this.unsub$.next()
    this.unsub$.complete()
  }

  private loadHandler() {
    combineLatest([
      this.model.get('planoContabilExtra').valueChanges,
      this.model.get('fornecedor').valueChanges,
    ])
      .pipe(takeUntil(this.unsub$))
      .subscribe(([accountPlanExtra, person]) => {
        this.model.get('calcularUsandoTabelaFaixa').patchValue(false)
        this.tableFlagEnable = false
        if (!accountPlanExtra || !person) {
          return
        }
        this.tableFlagEnable = (
          [2,3].includes(accountPlanExtra?.tipoRetencao?.codigo) &&
          person?.tipoPessoaId == 1
        )
      })
  }

  loadForm() {
    this.model.patchValue({
      ...this.retention,
      calcularUsandoTabelaFaixa: this.retention.calcularUsandoTabelaFaixa == 'S'
    })
    this.model.get('planoContabilExtra').patchValue({
      ...this.retention.planoContabilExtra,
      planoContabil: {
        ...this.retention.planoContabilExtra.planoContabil,
        nome: `${
          this.retention.planoContabilExtra.planoContabil.codigo
        } - ${
          this.retention.planoContabilExtra.planoContabil.nome
        }`
      }
    })
  }

  private getNewModel() {
    return this.builder.group({
      uuid: [],
      fornecedor: ['', Validators.required],
      fonteRecurso: ['', Validators.required],
      planoContabilExtra: ['', Validators.required],
      calcularUsandoTabelaFaixa: [false],
      percentualRetencao: [
        0,
        (control: AbstractControl) => {
          if (control.value > 100 || control.value < 0) {
            return {
              customError:
                'O percentual de retenção deve estar compreendido entre 0% e 100%',
            }
          }
        },
      ],
      percentualBaseCalculo: [
        0,
        (control: AbstractControl) => {
          if (control.value > 100 || control.value < 0) {
            return {
              customError:
                'O percentual da base de cálculo deve estar compreendido entre 0% e 100%',
            }
          }
        },
      ],
      dataInclusao: [],
    })
  }

  prepare(formData: any) {
    return {
      ...formData,
      planoContabilExtra: {
        uuid: formData.planoContabilExtra?.uuid
      },
      fonteRecurso: {
        uuid: formData.fonteRecurso?.uuid
      },
      fornecedor: {
        uuid: formData.fornecedor?.uuid
      },
      calcularUsandoTabelaFaixa: formData.calcularUsandoTabelaFaixa ? "S" : "N"
    }
  }

  confirm() {
    const dto = this.prepare(this.model.getRawValue())
    const request$ = this.retention?.uuid
      ? this.service.put(this.parentUuid, dto, this.retention.uuid)
      : this.service.post(this.parentUuid, dto)

    this.loading = true
    request$
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          message: `Retenção extra ${
            !this.retention?.uuid ? 'cadastrada' : 'atualizada'
          } com sucesso!`,
          success: true,
        })
        this.dialogRef.close(res)
      })
  }

  cancel() {
    this.dialogRef.close(null)
  }

  reset() {
    this.model.reset({
      percentualRetencao: 0,
      percentualBaseCalculo: 0
    })
  }
}
