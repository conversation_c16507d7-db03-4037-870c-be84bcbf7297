import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { ContractAdditionService } from '@pages/contract/services/contract-addition.service'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import DataSource from 'devextreme/data/data_source'
import { Subject } from 'rxjs'
import { filter, takeUntil, finalize, take } from 'rxjs/operators'
import { ContractAdditionDialogComponent } from '../../../contract-act/components/contract-addition-dialog/contract-addition-dialog.component'
import { ContractExtraRetentionFormDialogComponent } from '../contract-extra-retention-form-dialog/contract-extra-retention-form-dialog.component'
import { ExtraRetentionInterface } from '@pages/contract/interfaces/contract-extra-retention'
import { ContractExtraRetentionService } from '@pages/contract/services/contract-extra-retention.service'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { MenuService } from '@pages/menu.service'
import { SelectDto } from '@pages/contract/interfaces/select-dto'

@Component({
  selector: 'eqp-contract-extra-retention-list',
  templateUrl: './contract-extra-retention-list.component.html',
  styleUrls: ['./contract-extra-retention-list.component.scss'],
})
export class ContractExtraRetentionListComponent
  extends BaseTelasComponent implements OnInit {
  pageTitle: string = 'Retenção Extra'
  loading: boolean = false

  dataSource: DataSource
  providerDataSource: any
  resourceSourceDataSource: any
  parentUuid: string
  currencyFormat = currencyFormat
  baseUri: string = 'retencao_extra'

  private destroy$ = new Subject<null>()

  constructor(
    public menuService: MenuService,
    public router: Router,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private crudService: CrudService,
    private dialogService: NbDialogService,
    private service: ContractExtraRetentionService,
  ) {
    super(menuService, router)
    this.permissao('/contrato')
  }

  ngOnInit(): void {
    const { uuid } = this.route.snapshot.params
    this.parentUuid = uuid

    this.fetchGrid()
  }

  fetchGrid() {
    this.providerDataSource = {
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/contrato/retencao_extra/fornecedor',
        10
      ),
      paginate: true,
      pageSize: 10
    }
    this.resourceSourceDataSource = {
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/contrato/fonte_recurso',
        10
      ),
      paginate: true,
      pageSize: 10
    }
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `licitacao/contrato/${this.parentUuid}/${this.baseUri}`,
        10,
      ),
      map: (data: any) => {
        return {
          ...data,
          gridFornecedor: `${data.fornecedor.codigo} - ${data.fornecedor.nome}`
        }
      },
      paginate: true,
      pageSize: 10,
    })
  }

  public provider(data): any {
    if (data.data && data.data.fornecedor.nome)
      return data.data.fornecedor.nome
  }

  public resourceSource(data): any {
    if (data.data && data.data.fonteRecurso.nome)
      return data.data.fonteRecurso.nome
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Retenção extra'
      event.toolbarOptions.items[0].options.hint = 'Nova retenção extra'
      event.toolbarOptions.items[0].options.onClick = () => this.edit()
    }
  }

  edit(data?: any) {
    this.dialogService.open(
      ContractExtraRetentionFormDialogComponent,
      {
        context: {
          parentUuid: this.parentUuid,
          retention: data
        },
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    ).onClose.pipe(filter(res => res)).subscribe(res => {
      this.fetchGrid()
    })
  }

  remove(uuid: string) {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(res => {
      if (res === 'S') {
        this.loading = true
        this.service
          .delete(this.parentUuid, uuid)
          .pipe(
            takeUntil(this.destroy$),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: `Retenção extra removida com sucesso.`,
              })
              this.dataSource.reload()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
