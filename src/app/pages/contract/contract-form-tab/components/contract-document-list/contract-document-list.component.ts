import { Component, OnInit } from '@angular/core';
import { ContractDocumentFormComponent } from '../contract-document-form/contract-document-form.component';
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component';
import { finalize, take } from 'rxjs/operators';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CrudService } from '@common/services/crud.service';
import { NbDialogService } from '@nebular/theme';
import DataSource from 'devextreme/data/data_source';
import { ContractDocumentService } from '@pages/contract/services/contract-document.service';

@Component({
  selector: 'eqp-contract-document-list',
  templateUrl: './contract-document-list.component.html',
  styleUrls: ['./contract-document-list.component.scss']
})
export class ContractDocumentListComponent implements OnInit {

  loading: boolean = false
  dataSource: DataSource

  parentUuid: string

  constructor(
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private crudService: CrudService,
    private service: ContractDocumentService,
    private dialogService: NbDialogService
  ) { }

  ngOnInit(): void {
    this.parentUuid = this.route.snapshot.params?.uuid

    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `licitacao/contrato/${this.parentUuid}/documento`,
        10
      ),
      paginate: true,
      pageSize: 10,
      map: (data) => {
        data.flagPublicarPortal = data?.processoDocumentoModelo?.flagPublicar == 'S' ? "Sim" : "Não"
        return data
      }
    })
  }


  openVersions(uuid: string, data) {
    this.router.navigate([
      'contrato', this.parentUuid, 'documento', uuid, 'versoes'
    ])
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Documento'
      event.toolbarOptions.items[0].options.hint = 'Nova documento'
      event.toolbarOptions.items[0].options.onClick = () => this.edit()
    }
  }

  public edit(uuid?: string) {
    this.dialogService.open(ContractDocumentFormComponent, {
      context: {
        parentUuid: this.parentUuid,
        uuid,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose.pipe().subscribe(res => {
      this.dataSource.reload()
    })
  }

  public remove(uuid: string){
    this.dialogService.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          body: 'Confirma a exclusão do documento?'
        }
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose.pipe(take(1)).subscribe(res => {
      if (res === true) {
        this.loading = true
        this.service
          .delete(this.parentUuid, uuid)
          .pipe(take(1), finalize(() => (this.loading = false)))
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: `Documento removido com sucesso.`,
              })
              this.dataSource.reload()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

}
