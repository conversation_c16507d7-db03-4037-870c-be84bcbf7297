import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ContractItemsDotationRoutingModule } from './contract-dotation-routing.module';
import { ContractItemsDotationComponent } from './contract-dotation.component';
import { CommonToolsModule } from '@common/common-tools.module';
import { SharedModule } from '@pages/shared/shared.module';
import { DotationCurrentExerciseComponent } from './components/dotation-current-exercise/dotation-current-exercise.component';
import { DotationOtherExercisesComponent } from './components/dotation-other-exercises/dotation-other-exercises.component';


@NgModule({
  declarations: [
    ContractItemsDotationComponent,
    DotationCurrentExerciseComponent,
    DotationOtherExercisesComponent
  ],
  imports: [
    CommonModule,
    ContractItemsDotationRoutingModule,
    CommonToolsModule,
    SharedModule,
  ]
})
export class ContractDotationModule { }
