import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DotationCurrentExerciseComponent } from './dotation-current-exercise.component';

describe('DotationCurrentExerciseComponent', () => {
  let component: DotationCurrentExerciseComponent;
  let fixture: ComponentFixture<DotationCurrentExerciseComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DotationCurrentExerciseComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DotationCurrentExerciseComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
