import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DotationCurrentExerciseComponent } from './components/dotation-current-exercise/dotation-current-exercise.component';
import { DotationOtherExercisesComponent } from './components/dotation-other-exercises/dotation-other-exercises.component';
import { ContractItemsDotationComponent } from './contract-dotation.component';

const routes: Routes = [
  {
    path: '',
    component: ContractItemsDotationComponent,
    children: [
      {
        path: '',
        redirectTo: 'exercicio-atual'
      },
      {
          path: 'exercicio-atual',
          component: DotationCurrentExerciseComponent,
      },
      {
       path: 'outros-exercicios',
       component: DotationOtherExercisesComponent,
   },
    ]
   }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ContractItemsDotationRoutingModule { }
