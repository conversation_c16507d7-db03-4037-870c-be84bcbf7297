import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'eqp-contract-items-dotation',
  template: `<nb-route-tabset [activeLinkOptions]="{exact:false}" [tabs]="tabs"></nb-route-tabset>`,
  styles: [
  ]
})
export class ContractItemsDotationComponent implements OnInit {
  tabs = [];

  constructor() {}

  ngOnInit(): void {
    this.tabs =[
      {
        title: 'Exercício atual',
        route: `./exercicio-atual`,
      },
      {
        title: 'Outros exercícios',
        route: `./outros-exercicios`,
      },
    ];
  }

}
