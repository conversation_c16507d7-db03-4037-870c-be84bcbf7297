<eqp-nebular-dialog
  dialogTitle="Transferências governamentais"
  dialogSize="medium"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="rightFirstButtonDisabled"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonTitle]="'Voltar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="cancel()"
>
<eqp-loading *ngIf="loading"></eqp-loading>
  <ng-container [formGroup]="model">
    <div
      class="d-flex justify-content-center"
      style="gap: 1rem"
      *ngIf="type === 'Federal' || type === undefined"
    >
      <eqp-nebular-toggle
        *ngIf="!type"
        style="padding-top: 1.7rem"
        formControlName="permitirFederal"
      ></eqp-nebular-toggle>
      <eqp-search-field
        style="width: 100%"
        label="Convênio federal"
        dialogTitle="Convênio federal"
        [uri]="federalUrl"
        searchColumnsType="contractFederalAgreementColumns"
        formControlName="convenioFederal"
        codeLabel="Convênio"
        nameLabel="Exercício"
        codeKey="numero"
        nameKey="exercicio"
        [disabled]="!model.get('permitirFederal')?.value"
        [required]="model.get('permitirFederal')?.value"
        [dataSourceLookup]="exerciseData"
        errorMenssage="Convênio informado não encontrado."
      ></eqp-search-field>
    </div>
    <div
      class="d-flex justify-content-center"
      style="gap: 1rem"
      *ngIf="type === 'Municipal' || type === undefined"
    >
      <eqp-nebular-toggle
        *ngIf="!type"
        style="padding-top: 1.7rem"
        formControlName="permitirMunicipal"
      ></eqp-nebular-toggle>
      <eqp-search-field
        style="width: 100%"
        label="Transferência municipal"
        dialogTitle="Transferência municipal"
        [uri]="municipalUrl"
        searchColumnsType="contractGovernmentTransferColumns"
        formControlName="transferenciaMunicipal"
        codeLabel="Convênio"
        nameLabel="Exercício"
        codeKey="numero"
        nameKey="exercicio"
        [disabled]="!model.get('permitirMunicipal')?.value"
        [required]="model.get('permitirMunicipal')?.value"
        [dataSourceLookup]="exerciseData"
        errorMenssage="Convênio informado não encontrado."
      ></eqp-search-field>
    </div>
    <div
      class="d-flex justify-content-center"
      style="gap: 1rem"
      *ngIf="type === 'Estadual' || type === undefined"
    >
      <eqp-nebular-toggle
        style="padding-top: 1.7rem"
        formControlName="permitirEstadual"
        *ngIf="!type"
      ></eqp-nebular-toggle>
      <eqp-search-field
        style="width: 100%"
        label="Convênio estadual"
        dialogTitle="Convênio estadual"
        [uri]="estadualUrl"
        searchColumnsType="contractGovernmentTransferColumns"
        formControlName="convenioEstadual"
        codeLabel="Convênio"
        nameLabel="Exercício"
        codeKey="numero"
        nameKey="exercicio"
        [disabled]="!model.get('permitirEstadual')?.value"
        [required]="model.get('permitirEstadual')?.value"
        [dataSourceLookup]="exerciseData"
        errorMenssage="Convênio informado não encontrado."
      ></eqp-search-field>
    </div>
    <div
      class="d-flex mt-3"
      [ngStyle]="{ 'padding-left': !type ? '1rem' : '0px' }"
    >
      <eqp-field-date
        label="Data Inclusão TCE"
        formControlName="dataInclusao"
        placeholder="dd/mm/aaaa"
        [required]="true"
      ></eqp-field-date>
    </div>
  </ng-container>
</eqp-nebular-dialog>
