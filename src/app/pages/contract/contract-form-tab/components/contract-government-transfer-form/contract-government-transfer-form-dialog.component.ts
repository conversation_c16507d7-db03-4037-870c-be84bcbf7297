import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import { GovernmentTransferService } from '@pages/contract/services/government-transfer.service';
import { Observable, Subject } from 'rxjs';
import { finalize, take, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'eqp-contract-government-transfer-form-dialog',
  templateUrl: './contract-government-transfer-form-dialog.component.html',
  styleUrls: ['./contract-government-transfer-form-dialog.component.scss']
})
export class ContractGovernmentTransferFormDialogComponent implements OnInit, OnDestroy {
  constructor(
    private builder: FormBuilder,
    private crudService: CrudService,
    private dialogRef: NbDialogRef<ContractGovernmentTransferFormDialogComponent>,
    private service: GovernmentTransferService,
    private toastr: ToastrService,
  ) { }

  @Input() uuid: string
  @Input() parentUuid: string = ''
  @Input() type: string;

  transferType: string
  exerciseData: any[] = []


  get federalUrl(){
    return `licitacao/contrato/convenio_federal`
  }
  get municipalUrl(){
    return `licitacao/contrato/transferencia_municipal`
  }
  get estadualUrl(){
    return `licitacao/contrato/convenio_estadual`
  }

  public loading: boolean = false;
  public model: FormGroup;
  private unsub$ = new Subject<null>();

  get rightFirstButtonDisabled(){
    return this.model.invalid || (this.model.get('permitirMunicipal').value == 'N' && this.model.get('permitirEstadual').value == 'N');
  }

  ngOnInit(): void {
    this.model = this.getNewModel();    
    this.getGridSelectData()
    this.loadHandlers();
    if(this.uuid) {
      this.loadForm();
    }
  }

  ngOnDestroy(): void {
    this.unsub$.next()
    this.unsub$.complete()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      codigo: [],
      dataInclusao: [],
      transferenciaMunicipal: [],
      convenioEstadual: [],
      convenioFederal: [],
      permitirFederal: [false],
      permitirMunicipal: [false],
      permitirEstadual: [false]
    })
  }

  private getGridSelectData() {
    this.crudService.getSingleData<any>('auth/exercicio')
    .pipe(take(1))
    .subscribe(res => {
      this.exerciseData = res.data.sort((a, b) => b.exercicio - a.exercicio)
    })
  }

  private loadForm() {
    this.loading = true;
    const transferTypeRule = {
      'Municipal': {
        field: 'transferenciaMunicipal',
        flag: 'permitirMunicipal',
        filter: 'MUNICIPAL'
      },
      'Federal': {
        field: 'convenioFederal',
        flag: 'permitirFederal',
        filter: 'FEDERAL'
      },
      'Estadual':  {
        field: 'convenioEstadual',
        flag: 'permitirEstadual',
        filter: 'ESTADUAL'
      },
    }
    this.transferType = transferTypeRule[this.type]?.filter;
    this.service.getIndividual(this.parentUuid, this.uuid, this.transferType)
    .pipe(take(1), finalize(() => this.loading = false))
    .subscribe(res => {
      let dto: any = {
        [transferTypeRule[this.type]?.field]: res.dados?.transferenciaConvenio,
        dataInclusao: res.dados?.dataInclusao,
        [transferTypeRule[this.type]?.flag]: true,
      };
      this.model.patchValue(dto)
    })
  }

  private loadHandlers() {
    const arr = [
      {
        toggle: 'permitirFederal',
        search: 'convenioFederal',
        enable: false,
        transferType: 'FEDERAL'
      },
      {
        toggle: 'permitirMunicipal',
        search: 'transferenciaMunicipal',
        enable: false,
        transferType: 'MUNICIPAL'
      },
      {
        toggle: 'permitirEstadual',
        search: 'convenioEstadual',
        enable: false,
        transferType: 'ESTADUAL'
      }
    ]

    arr.forEach(control => {
      this.model.get(control.toggle)
      .valueChanges
      .pipe(takeUntil(this.unsub$))
      .subscribe(value => {
        if(value){
          const fieldsDisabled = arr.filter(value => value.toggle != control.toggle)
          fieldsDisabled.forEach(field => {
            this.model.get(field.search).reset(null, {emitEvent: false});
            this.model.get(field.toggle).patchValue(false, {emitEvent: false});
          })
        }
      })
    })
  }

  private prepare(formData): any {
    const dto: any = {
      uuid: formData?.uuid,
      dataInclusaoTce: formData?.dataInclusao,
    }
    if(this.model.get('transferenciaMunicipal')?.value){
      dto.transferencia = {uuid: this.model.get('transferenciaMunicipal')?.value?.uuid || this.model.get('transferenciaMunicipal')?.value};
      this.transferType = 'MUNICIPAL';
    }else if(this.model.get('convenioEstadual')?.value){
      dto.transferencia = {uuid: this.model.get('convenioEstadual')?.value?.uuid || this.model.get('convenioEstadual')?.value};
      this.transferType = 'ESTADUAL';
    }else{
      dto.transferencia = {uuid: this.model.get('convenioFederal')?.value?.uuid || this.model.get('convenioFederal')?.value};
      this.transferType = 'FEDERAL';

    }
    return dto
  }

  public confirm() {
    this.loading = true
    const dto = this.prepare(this.model.getRawValue())
    let req: Observable<any>
    if (this.uuid) {
      req = this.service.put(this.parentUuid, dto, this.uuid, this.transferType)
    } else {
      req = this.service.post(this.parentUuid, dto, this.transferType)
    }
    req
      .pipe(take(1), finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Transferência governamental ${
            this.uuid ? 'atualizada' : 'cadastrada'
          } com sucesso`,
          success: true,
        })
        this.dialogRef.close(res.body.dados)
      })
  }

  public reset() {
    this.model.get('dataInclusao').reset()
    this.model.get('permitirMunicipal').patchValue('N')
    this.model.get('permitirEstadual').patchValue('N')
  }

  public cancel() {
    this.dialogRef.close(null)
  }
}
