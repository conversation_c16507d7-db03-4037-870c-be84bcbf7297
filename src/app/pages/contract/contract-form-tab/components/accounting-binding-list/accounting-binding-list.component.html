<div class="mt-3">
   <dx-data-grid
     id="contract-act-list"
     [allowColumnResizing]="true"
     [columnAutoWidth]="true"
     [dataSource]="dataSource"
     [showColumnLines]="false"
     [showRowLines]="false"
     [showBorders]="false"
     [rowAlternationEnabled]="true"
     [wordWrapEnabled]="true"
     [loadPanel]="false"
     [columnHidingEnabled]="true"
     [remoteOperations]="true"
     keyExpr="uuid"
     (onToolbarPreparing)="onToolbarPreparing($event)"
   >
     <dxo-export
       [enabled]="true"
       [excelWrapTextEnabled]="true"
       [excelFilterEnabled]="true"
       [fileName]="'Vinculação contábil'"
     ></dxo-export>
 
     <dxo-paging [pageSize]="10"></dxo-paging>
 
     <dxo-pager
       [showInfo]="true"
       [showNavigationButtons]="true"
       [showPageSizeSelector]="false"
     >
     </dxo-pager>
 
     <dxo-header-filter [visible]="false"> </dxo-header-filter>
     <dxo-filter-row [visible]="true"></dxo-filter-row>
 
     <dxo-sorting mode="multiple"></dxo-sorting>
 
     <dxo-column-chooser [enabled]="false"></dxo-column-chooser>
 
     <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>
 
     <dxo-search-panel
       [visible]="false"
       placeholder="Buscar"
     ></dxo-search-panel>
 
     <dxo-editing
       mode="form"
       [allowUpdating]="false"
       [allowDeleting]="false"
       [allowAdding]="true"
       [useIcons]="true"
     >
     </dxo-editing>

    <dxi-column
     alignment="left"
      dataField="contaContabil.uuid"
      caption="Conta contábil"
      cellTemplate="accountPlanReduceCodeTemplate"
      [allowSorting]="false"
      [allowSearch]="false"
    >
      <dxo-lookup
        [dataSource]="accountPlanReducedCodeDataSource"
        displayExpr="codigoReduzido"
        valueExpr="uuid"
      >
      </dxo-lookup>
    </dxi-column>

    <dxi-column 
      dataField="contaContabil.uuid"
      caption=""
      cellTemplate="accountPlanCodeTemplate"
      [allowSorting]="false"
      [allowSearch]="false"
    >
      <dxo-lookup
        [dataSource]="accountPlanCodeDataSource"
        displayExpr="codigo"
        valueExpr="uuid"
      >
      </dxo-lookup>
    </dxi-column>

    <dxi-column 
      dataField="contaContabil.uuid"
      caption=""
      cellTemplate="accountPlanNameTemplate"
      [allowSorting]="false"
      [allowSearch]="false"
    >
      <dxo-lookup
        [dataSource]="accountPlanNameDataSource"
        displayExpr="nome"
        valueExpr="uuid"
      >
      </dxo-lookup>
    </dxi-column>
    <dxi-column
      dataField="tipoVinculacaoContabil.nome"
      caption="Tipo"
    ></dxi-column>
    <dxi-column
      alignment="left"
      dataField="saldo"
      caption="Saldo"
    ></dxi-column>
    <dxi-column 
      alignment="left"
      dataField="eventoContabilConfig.eventoContabil.uuid"
      caption="Evento"
      cellTemplate="accountEventTemplate"
      [allowSorting]="false"
      [allowSearch]="false"
    >
      <dxo-lookup
        [dataSource]="accountEventDataSource"
        displayExpr="numero"
        valueExpr="uuid"
      >
      </dxo-lookup>
    </dxi-column>
    <!-- <dxi-column
      alignment="left"
      dataField="eventoContabilConfig.eventoContabil.numero"
      caption="Evento"
      [allowSorting]="false"
      [allowFiltering]="false"
    ></dxi-column> -->

    <div *dxTemplate="let data of 'accountPlanReduceCodeTemplate'">
      {{ accountPlan(data, 'codigoReduzido') }}
    </div>

    <div *dxTemplate="let data of 'accountPlanCodeTemplate'">
      {{ accountPlan(data, 'codigo') }}
    </div>

    <div *dxTemplate="let data of 'accountPlanNameTemplate'">
      {{ accountPlan(data, 'nome') }}
    </div>

    <div *dxTemplate="let data of 'accountEventTemplate'">
      {{ accountEvent(data) }}
    </div>

     <dxi-column
       dataField="uuid"
       caption=""
       [width]="80"
       [allowFiltering]="false"
       [allowEditing]="false"
       [allowSorting]="false"
       cellTemplate="acaoColumn"
     ></dxi-column>
 
     <div *dxTemplate="let data of 'acaoColumn'">
       <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
         <a
           title="Editar"
           (click)="edit(data.data)"
           class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
         >
         </a>
         <a
            *ngIf="nivelPermissao === 'FULL'"
           title="Remover"
           (click)="remove(data.value)"
           class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
         >
         </a>
       </div>
     </div>
   </dx-data-grid>
 </div>
