import { Component, Input, OnInit } from '@angular/core';
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { AccountingBindingService } from '@pages/contract/services/accounting-binding.service';
import DataSource from 'devextreme/data/data_source';
import { Subject } from 'rxjs';
import { filter, finalize, take, takeUntil } from 'rxjs/operators';
import { ContractAccountingBindingComponent } from '../accounting-binding-form/contract-accounting-binding.component';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { MenuService } from '@pages/menu.service';
import { ActivatedRoute, Router } from '@angular/router';
import { SelectDto } from '@pages/contract/interfaces/select-dto';

@Component({
  selector: 'eqp-accounting-binding-list',
  templateUrl: './accounting-binding-list.component.html',
  styleUrls: ['./accounting-binding-list.component.scss']
})
export class AccountingBindingListComponent
  extends BaseTelasComponent implements OnInit
{
  @Input() parentUuid: string
  
  loading: boolean = false
  dataSource: DataSource
  retentionTypeData: SelectDto[]
  accountPlanReducedCodeDataSource: any
  accountPlanCodeDataSource: any
  accountPlanNameDataSource: any
  accountEventDataSource: any
  
  private destroy$ = new Subject<null>()


  constructor(
    public menuService: MenuService,
    public router: Router,
    private route: ActivatedRoute,
    private crudService: CrudService,
    private dialogService: NbDialogService,
    private service: AccountingBindingService,
    private toastr: ToastrService
  ) {
    super(menuService, router)
    this.permissao('/contrato')
  }

  ngOnInit(): void {
    this.parentUuid = this.route.snapshot.params?.uuid
    this.dataSource = this.fetchGrid()
  }

  private getAccountPlanDataSource(equalFilter = false) {
    if (equalFilter) {
      return {
        store: this.crudService.getDataSource(
          'uuid',
          `licitacao/contrato/plano_contabil`,
          10
        ),
        paginate: true,
        pageSize: 10,
      }
    }
    return {
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `licitacao/contrato/plano_contabil`,
        10
      ),
      paginate: true,
      pageSize: 10,
    }
  }

  fetchGrid() {
    this.accountPlanReducedCodeDataSource = this.getAccountPlanDataSource(true)
    this.accountPlanCodeDataSource = this.getAccountPlanDataSource()
    this.accountPlanNameDataSource = this.getAccountPlanDataSource()
    this.accountEventDataSource = {
      store: this.crudService.getDataSource(
        'uuid',
        'licitacao/licitacao/evento_contabil',
        10,
      ),
      paginate: true,
      pageSize: 10
    }
    return new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `licitacao/contrato/${this.parentUuid}/vinculacao_contabil`,
        10
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  accountPlan(data, key: string): any {
    if (data.data && data.data.contaContabil[key])
      return data.data.contaContabil[key]
  }

  accountEvent(data): any {
    if (data.data && data.data?.eventoContabilConfig?.eventoContabil?.numero)
      return data?.data?.eventoContabilConfig?.eventoContabil?.numero
  }

  onToolbarPreparing(event: any){
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = `Vinculação contábil`
      event.toolbarOptions.items[0].options.hint = `Nova vinculação contábil`
      event.toolbarOptions.items[0].options.onClick = () => this.edit()
    }
  }

  edit(data?: any){
    this.dialogService.open(ContractAccountingBindingComponent, {
      context: {
        parentUuid: this.parentUuid,
        biddind: data
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose.pipe(filter(res => res)).subscribe(res => {
      this.dataSource.reload()
    })
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(res => {
      if (res === 'S') {
        this.loading = true
        this.service
          .delete(this.parentUuid, uuid)
          .pipe(
            takeUntil(this.destroy$),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: `Vinculação contábil removida com sucesso!`,
              })
              this.dataSource.reload()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
