<div class="container" [formGroup]="model">
  <ng-container>
    <nb-tabset>
      <nb-tab tabTitle="Tela I">
        <div style="position: relative;">
          <div class="row">
            <eqp-loading *ngIf="loading"></eqp-loading>
            <div class="col col-sm-3 col-md-2">
              <eqp-nebular-input
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="codigo"
                label="Código"
                placeholder
                [readonly]="uuid">
              </eqp-nebular-input>
            </div>
            <div class="col col-sm-3 col-md-3">
              <eqp-nebular-input
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="exercicio"
                name="exercicio"
                label="Exercício"
                placeholder
                [readonly]="true">
              </eqp-nebular-input>
            </div>
            <div class="col col-sm-3 col-md-3" formGroupName="entidadeOrigem">
              <eqp-nebular-select
                formControlName="uuid"
                label="Entidade *"
                [dataSource]="entityData"
                [displayExpr]="entityDisplay"
                [valueExpr]="'uuid'"></eqp-nebular-select>
            </div>
            <div class="col col-sm-3 col-md-4">
              <eqp-nebular-select
                formControlName="tipoAtoContratual"
                label="Tipo ato contratual"
                [dataSource]="contractActTypeData"
                displayExpr="nome"
                [valueExpr]="'uuid'"
                [required]="true"></eqp-nebular-select>
            </div>
          </div>
          <div class="row mt-3 mb-3">
            <div class="col">
              <eqp-nebular-input
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="numero"
                label="Número"
                placeholder
                [required]="true"
                [readonly]="disableNumbers || !tipoAtoContratual">
              </eqp-nebular-input>
            </div>
            <div class="col">
              <eqp-nebular-input
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="numeroTce"
                name="numeroTce"
                label="Número TCE"
                placeholder
                [readonly]="disableNumbers || !tipoAtoContratual">
              </eqp-nebular-input>
            </div>
            <div class="col">
              <eqp-field-date
                formControlName="inclusaoTce"
                name="inclusaoTce"
                label="Inclusão TCE"
                [required]="true"
                placeholder="dd/mm/aaaa"></eqp-field-date>
            </div>
            <div class="col">
              <eqp-field-date
                formControlName="inclusaoTceParteContrato"
                name="inclusaoTceParteContrato"
                label="Inclusão TCE (Parte Contrato)"
                [required]="true"
                placeholder="dd/mm/aaaa"></eqp-field-date>
            </div>
          </div>
          <eqp-fieldset
            *ngIf="entityExerciseData.length > 0 && (!uuid || !semLicitacao)"
            label="Licitação">
            <div class="row">
              <div *ngIf="!uuid" class="col col-12 col-xxl-auto mt-1">
                <eqp-nebular-toggle
                  label="Sem licitação"
                  formControlName="semLicitacao"
                  title="Sem licitação"></eqp-nebular-toggle>
              </div>
              <div class="col col-12 col-md-3 col-xxl-2"formGroupName="licitacao">
                <eqp-nebular-search-field
                  [label]="'Licitação *'"
                  (onButtonClick)="onLicitationSearchClick()"
                  [nameKey]="'objeto'"
                  [codeKey]="'numero'"
                  codeLabel="Número"
                  formControlName="numero"
                  [disabledCodeInput]="true"
                  [hideName]="true"
                ></eqp-nebular-search-field>
              </div>
              <div class="col" formGroupName="licitacao">
                <eqp-nebular-input
                  [size]="'small'"
                  formControlName="entidade"
                  label="Entidade"
                  placeholder
                  [readonly]="true"></eqp-nebular-input>
              </div>
              <div class="col col-12 col-md-3 col-xxl-2"
                formGroupName="licitacao">
                <eqp-nebular-input
                  [type]="'number'"
                  [size]="'small'"
                  formControlName="exercicio"
                  label="Exercício"
                  placeholder
                  [readonly]="true"></eqp-nebular-input>
              </div>
              <div class="col" formGroupName="licitacao">
                <eqp-nebular-input
                  [size]="'small'"
                  formControlName="modalidade"
                  label="Modalidade"
                  placeholder
                  [readonly]="true"></eqp-nebular-input>
              </div>
            </div>
            <div formGroupName="licitacao">
              <eqp-nebular-input
                [style]="'textArea'"
                [size]="'small'"
                formControlName="objeto"
                label="Objeto"
                placeholder
                [readonly]="true"
                [rows]="1"></eqp-nebular-input>
            </div>
          </eqp-fieldset>
          <div class="row mt-3">
            <div class="col">
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Valor original"
                formControlName="valorOriginal"
                placeholder="00,00"
                [required]="true">
              </eqp-nebular-input>
            </div>
            <div class="col">
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Acréscimo"
                formControlName="valorAcrescimo"
                placeholder="00,00"
                [readonly]="true">
              </eqp-nebular-input>
            </div>
            <div class="col">
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Anulação"
                formControlName="valorAnulaccao"
                placeholder="00,00"
                [readonly]="true">
              </eqp-nebular-input>
            </div>
            <div class="col">
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Total aditivos"
                formControlName="totalAditivos"
                placeholder="00,00"
                [readonly]="true">
              </eqp-nebular-input>
            </div>
            <div class="col">
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Valor atualizado"
                formControlName="valorAtualizado"
                placeholder="00,00"
                [readonly]="true">
              </eqp-nebular-input>
            </div>
          </div>
          <div class="row justify-content-between mt-3">
            <div class="col-12">
              <eqp-fieldset label="Vigência do contrato original *">
                <eqp-period
                  formGroupName="vigenciaOriginal"
                  [deadLineTypeData]="deadLineTypeData"></eqp-period>
              </eqp-fieldset>
            </div>
            <div class="col-12">
              <eqp-fieldset [label]="'Período de execução'+(obrigarPeriodoExecucao ? ' *': '')">
                <eqp-period
                  formGroupName="periodoExecucao"
                  [obrigatorio]="obrigarPeriodoExecucao"
                  [deadLineTypeData]="deadLineTypeData"></eqp-period>
              </eqp-fieldset>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col">
              <eqp-nebular-select
                formControlName="tipoContrato"
                label="Tipo de contrato"
                [size]="'small'"
                [dataSource]="contractTypeData"
                displayExpr="nome"
                [valueExpr]="'uuid'"
                [required]="true"></eqp-nebular-select>
            </div>
            <div class="col">
              <eqp-nebular-input
                [style]="'basic'"
                [type]="'text'"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="fundamentoLegal"
                name="fundamentoLegal"
                label="Fundamento legal"
                placeholder
                maxlength="128">
              </eqp-nebular-input>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col" formGroupName="tipoFormaPagamento">
              <eqp-nebular-select
                formControlName="uuid"
                label="Forma de pagamento (TCE)"
                [size]="'small'"
                [dataSource]="formPaymentData"
                displayExpr="nome"
                [valueExpr]="'uuid'"
                [required]="true"></eqp-nebular-select>
            </div>
            <div class="col">
              <eqp-nebular-input
                [style]="'basic'"
                [type]="'text'"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="formaPagamento"
                name="formaPagamento"
                label="Forma de pagamento"
                placeholder
                [required]="true">
              </eqp-nebular-input>
            </div>
            <div class="col">
              <eqp-field-date
                formControlName="dataEntrega"
                label="Data de entrega"
                placeholder="dd/mm/aaaa"></eqp-field-date>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col col-md-4">
              <eqp-search-field
                label="Local"
                formControlName="local"
                [uri]="baseUrl + '/local'"
                dialogTitle="Local"
                searchColumnsType="unityPlanColumns"
                [returnAllData]="true"
                [required]="true"></eqp-search-field>
            </div>
            <div class="col">
              <eqp-nebular-select
                formControlName="pais"
                label="País"
                [size]="'small'"
                [dataSource]="countryData"
                displayExpr="nome"
                [valueExpr]="'uuid'"></eqp-nebular-select>
            </div>
            <div class="col">
              <eqp-nebular-select
                formControlName="tipoRegimeExecucao"
                label="Regime de execução"
                [size]="'small'"
                [dataSource]="executionRegimeTypeData"
                displayExpr="nome"
                [valueExpr]="'uuid'"
                [required]="true"></eqp-nebular-select>
            </div>
            <div class="col col-md-4" formGroupName="tipoGarantia">
              <eqp-nebular-select
                formControlName="uuid"
                label="Tipo garantia"
                [size]="'small'"
                [dataSource]="guaranteeTypeData"
                displayExpr="nome"
                [valueExpr]="'uuid'"></eqp-nebular-select>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col">
              <eqp-search-field
                label="Fornecedor"
                formControlName="fornecedor"
                [uri]="uriGridProvider()"
                dialogTitle="Fornecedor"
                searchColumnsType="providerContractedColumns"
                codeKey="pessoaCodigo"
                nameKey="pessoaNome"
                [returnAllData]="true"
                [required]="true"></eqp-search-field>
            </div>
            <div class="col">
              <eqp-search-field
                label="Representante Legal"
                formControlName="representante"
                [uri]="baseUrl + '/pessoa'"
                dialogTitle="Representante"
                searchColumnsType="unityPlanColumns"
                [returnAllData]="true"
                [required]="true"></eqp-search-field>
            </div>
            <div class="col">
              <eqp-nebular-input
                [style]="'basic'"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="descricaoGarantia"
                name="descricaoGarantia"
                [label]="'Descrição da garantia'"
                placeholder="Descrição da garantia"
                [required]="warrantyTypeCode == 99"
                [readonly]="!tipoGarantia">
              </eqp-nebular-input>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <nb-card class="footer">
                <nb-card-header>Previsão de subcontratação *</nb-card-header>
                <nb-card-body>
                  <nb-radio-group
                    formControlName="previsaoSubcontratacao"
                    [name]="'previsaoSubcontratacao'">
                    <nb-radio
                      *ngFor="let option of tipoPrevisaoSubcontratacaoTemplate"
                      [value]="option.valor">
                      {{ option.texto }}
                    </nb-radio>
                  </nb-radio-group>
                </nb-card-body>
              </nb-card>
            </div>
            <div class="col">
              <nb-card class="footer">
                <nb-card-header>Fornecimento *</nb-card-header>
                <nb-card-body>
                  <nb-radio-group
                    formControlName="fornecimentoMedio"
                    [name]="'fornecimentoMedio'">
                    <nb-radio
                      *ngFor="let option of tipoFornecimentoMedioTemplate"
                      [value]="option.valor">
                      {{ option.texto }}
                    </nb-radio>
                  </nb-radio-group>
                </nb-card-body>
              </nb-card>
            </div>
          </div>
          <div formGroupName="recursos">
            <eqp-fieldset label="Recursos *">
              <div class="row align-items-end">
                <div class="col">
                  <eqp-nebular-input
                    [style]="'currency'"
                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                    [size]="'small'"
                    [shape]="'rectangle'"
                    label="Recursos próprios"
                    formControlName="proprios"
                    placeholder="00,00">
                  </eqp-nebular-input>
                </div>
                <strong class="mb-2">+</strong>
                <div class="col">
                  <eqp-nebular-input
                    [style]="'currency'"
                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                    [size]="'small'"
                    [shape]="'rectangle'"
                    label="Recursos estaduais"
                    formControlName="estaduais"
                    placeholder="00,00">
                  </eqp-nebular-input>
                </div>
                <strong class="mb-2">+</strong>
                <div class="col">
                  <eqp-nebular-input
                    [style]="'currency'"
                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                    [size]="'small'"
                    [shape]="'rectangle'"
                    label="Recursos federais"
                    formControlName="federais"
                    placeholder="00,00">
                  </eqp-nebular-input>
                </div>
                <strong class="mb-2">+</strong>
                <div class="col">
                  <eqp-nebular-input
                    [style]="'currency'"
                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                    [size]="'small'"
                    [shape]="'rectangle'"
                    label="Operações de crédito"
                    formControlName="operacoesCredito"
                    placeholder="00,00">
                  </eqp-nebular-input>
                </div>
                <strong class="mb-2">=</strong>
                <div class="col">
                  <eqp-nebular-input
                    [type]="'total'"
                    [style]="'currency'"
                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                    [size]="'small'"
                    [shape]="'rectangle'"
                    label="Total"
                    [value]="getTotal()"
                    formControlName="total"
                    placeholder="00,00"
                    [readonly]="true">
                  </eqp-nebular-input>
                </div>
              </div>
            </eqp-fieldset>
          </div>
        </div>
      </nb-tab>
      <nb-tab tabTitle="Tela II">
        <ng-container formGroupName="assinatura">
          <eqp-fieldset label="Assinaturas">
            <div class="row justify-content-start">
              <div class="col col-12 col-md-3 col-xxl-2">
                <eqp-field-date
                  formControlName="data"
                  name="data"
                  label="Data"
                  [required]="true"
                  placeholder="dd/mm/aaaa"></eqp-field-date>
              </div>
              <div class="col col-12 col-md-auto">
                <eqp-nebular-toggle
                  label="Publicar no portal"
                  formControlName="publicarInternet"
                  title="Publicar no portal"></eqp-nebular-toggle>
              </div>
              <div class="col col-12 col-md-auto">
                <eqp-nebular-toggle
                  label="Atendimento COVID-19"
                  formControlName="atendimentoCovid"
                  title="Atendimento COVID-19"></eqp-nebular-toggle>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col">
                <eqp-search-field
                  label="Contratante"
                  formControlName="contratante"
                  [uri]="baseUrl + '/pessoa'"
                  dialogTitle="Contratante"
                  searchColumnsType="unityPlanColumns"
                  [returnAllData]="true"></eqp-search-field>
              </div>
              <div class="col">
                <eqp-search-field
                  label="Contratada"
                  formControlName="contratada"
                  [uri]="baseUrl + '/pessoa'"
                  dialogTitle="Contratada"
                  searchColumnsType="unityPlanColumns"
                  [returnAllData]="true"></eqp-search-field>
              </div>
            </div>
            <div class="row mt-4">
              <div class="col">
                <eqp-search-field
                  label="Testemunha"
                  formControlName="testemunhaContratante"
                  [uri]="baseUrl + '/pessoa'"
                  dialogTitle="Contratante"
                  searchColumnsType="unityPlanColumns"
                  [returnAllData]="true"></eqp-search-field>
              </div>
              <div class="col">
                <eqp-search-field
                  label="Testemunha"
                  formControlName="testemunhaContratada"
                  [uri]="baseUrl + '/pessoa'"
                  dialogTitle="Contratante"
                  searchColumnsType="unityPlanColumns"
                  [returnAllData]="true"></eqp-search-field>
              </div>
            </div>
          </eqp-fieldset>
        </ng-container>
        <div>
          <div class="row align-items-end justify-content-between mt-3">
            <div class="col col-12 col-sm-6 col-md-4 mb-3" formGroupName="multa">
              <eqp-nebular-select
                label="Tipo de multa"
                formControlName="tipo"
                [size]="'small'"
                [dataSource]="fineTypeData"
                displayExpr="nome"
                [valueExpr]="'uuid'"
              ></eqp-nebular-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
              <eqp-fieldset label="SIAFIC">
                <eqp-field-date
                  label="Inclusão TCE"
                  formControlName="dataInclusaoTceSiafic"
                  placeholder="dd/mm/aaaa"
                ></eqp-field-date>
              </eqp-fieldset>
            </div>
          </div>
          <div class="mb-3" formGroupName="multa">
            <eqp-nebular-input
              [style]="'textArea'"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="descricao"
              name="descricao"
              [label]="'Descrição multa' + (tipoMulta ? ' *' : '')"
              placeholder="Descrição multa"
              [readonly]="!tipoMulta"
              [required]="tipoMulta"
              [rows]="3"></eqp-nebular-input>
          </div>
        </div>
        <div class="mt-3">
          <div class="mb-3">
            <eqp-nebular-input
              [style]="'textArea'"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="descricaoObjeto"
              name="descricaoObjeto"
              [label]="'Objeto'"
              placeholder="Objeto"
              [rows]="3"></eqp-nebular-input>
          </div>
        </div>
        <div class="row mt-2">
          <div class="col">
            <label class="label">Previsão de cessão contratual</label>
            <eqp-nebular-toggle
              formControlName="previsaoCessaoContratual"
              title="Previsão de cessão contratual"></eqp-nebular-toggle>
          </div>
          <div class="col">
            <label for class="label">Controla saldo por contrato/ato</label>
            <eqp-nebular-toggle
              formControlName="controlarSaldoSeparadamente"
              title="Controla saldo por contrato/ato"></eqp-nebular-toggle>
          </div>
          <div class="col">
            <label for class="label">Bloquear requisição de compra</label>
            <eqp-nebular-toggle
              formControlName="bloquearRequisicaoCompra"
              title="Bloquear requisição de compra"></eqp-nebular-toggle>
          </div>
          <div class="col">
            <eqp-field-date
              formControlName="dataProrrogacaoCompras"
              name="dataProrrogacaoCompras"
              label="Data de prorrogação de compras"
              placeholder="dd/mm/aaaa"></eqp-field-date>
          </div>
        </div>
      </nb-tab>
    </nb-tabset>
  </ng-container>
  <ng-container *ngIf="uuid">
    <app-log-info [formGroup]="log"></app-log-info>
  </ng-container>
  <div class="d-flex mt-4 justify-content-end" style="gap: 0.5rem">
    <eqp-nebular-button
      [buttonShape]="'rectangle'"
      [buttonText]="'Voltar'"
      (buttonEmitter)="cancel()"
      [buttonType]="'primary'"
      [buttonAppearance]="'outline'"
      [buttonIcon]="'fas fa-undo-alt'"
      [buttonIconVisible]="true"
      [buttonVisible]="true"></eqp-nebular-button>
    <eqp-nebular-button
      *ngIf="
        (uuid && nivelPermissao === 'EDITOR') ||
        nivelPermissao === 'FULL'
      "
      [buttonShape]="'rectangle'"
      [buttonText]="'Salvar'"
      (buttonEmitter)="submit()"
      [buttonType]="'success'"
      [buttonIcon]="'fas fa-save'"
      [buttonIconVisible]="true"
      [buttonVisible]="true"
      [buttonDisabled]="model.invalid"
    ></eqp-nebular-button>
  </div>
</div>
