import { Component, HostL<PERSON>ener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core'
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { ComponentCanDeactivate } from '@guards/pending-changes-guard/pending-changes.guard'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { ContractService } from '@pages/contract/services/contract.service'
import { MenuService } from '@pages/menu.service'
import { ContractProviderSearchDialogComponent } from '@pages/shared/search/contract-provider-search-dialog/contract-provider-search-dialog.component'
import DataSource from 'devextreme/data/data_source'
import { Observable, Subject } from 'rxjs'
import { debounceTime, filter, finalize, take, takeUntil } from 'rxjs/operators'
import { ContractValidateDto } from '../../interfaces/contract-validate.dto'
import { ContractProviderDialogComponent } from '../contract-provider-dialog/contract-provider-dialog.component'
import { PurchaseRequisitionLicitationSearchComponent } from '@pages/purshase-requisition/components/purchase-req-form-tab/components/purchase-requisition-licitation-search/purchase-requisition-licitation-search.component'
import DateParser from '@common/misc/date-parser'

@Component({
  selector: 'eqp-contract-form',
  templateUrl: './contract-form.component.html',
  styleUrls: ['./contract-form.component.scss'],
})
export class ContractFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy, ComponentCanDeactivate {
  uuid: string
  licitacaoUuid: string
  uuidLicitation: string;
  loading = false
  model: FormGroup
  baseUrl = 'licitacao/contrato'
  private destroy$ = new Subject<void>()

  @HostListener('window:beforeunload')
  canDeactivate(): Observable<boolean> | boolean {
    return this.model.pristine
  }

  entityData: DataSource
  contractActTypeData: any[] = []
  contractTypeData: DataSource
  executionRegimeTypeData: DataSource
  guaranteeTypeData: DataSource
  fineTypeData: DataSource
  formPaymentData: DataSource = new DataSource({ store: [] })
  countryData: DataSource
  entityExerciseData: any[] = []
  deadLineTypeData: any[]
  filterProviderData: any
  obrigarPeriodoExecucao:boolean
  currentNumbers: number[] = []
  disableNumbers = true

  tipoPrevisaoSubcontratacaoTemplate = [
    {
      texto: 'Existe previsão',
      valor: 'S',
    },
    {
      texto: 'Não existe previsão',
      valor: 'N',
    },
  ]

  tipoFornecimentoMedioTemplate = [
    {
      texto: 'Imediato',
      valor: 'S',
    },
    {
      texto: 'Não imediato',
      valor: 'N',
    },
  ]

  constructor(
    public menuService: MenuService,
    public router: Router,
    private builder: FormBuilder,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    private crudService: CrudService,
    private service: ContractService,
    private userService: UserDataService,
  ) {
    super(menuService, router)
    this.permissao('/contrato')
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.verificarObrigarPeriodoExecucao();
    this.uuid = this.route.snapshot.params?.uuid
    this.initializeSelectData()
    if (this.uuid) {
      this.loadPageData(this.uuid)
    } else {
      this.getContractCode()
      this.loadHandler()
    }
  }

  ngOnDestroy() {
    this.destroy$.next()
    this.destroy$.complete()
  }

  codeNameDisplay(item: any) {
    return item && `${item.codigo} - ${item.nome}`
  }

  deadLineTypeDisplay(item) {
    return item && `${item.texto}`
  }

  private getContractCode() {
    this.getCustomData(
      'buscar_codigo_sugestao',
      res => this.model.get('codigo').patchValue(res.dados),
    )
  }

  private validateContractNumber(typeUuid: string) {
    this.loading = true
    this.service.autoFillNumberContract(typeUuid)
      .pipe(take(1), finalize(() => this.loading = false))
      .subscribe({next: res => {
        this.disableNumbers = this.uuid ? false : res.dados.numerarContratosAtasAutomaticamente
        if (!this.uuid) {
          this.model.patchValue({
            numero: res.dados.numero,
            numeroTce: res.dados.numeroTce
          }, { emitEvent: false })
        }
      }})
  }

  private getDataSource(url: string, paginate = true, filter?: any[]) {
    return new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `${this.baseUrl}/${url}`,
        paginate ? 10 : 0,
      ),
      filter,
      paginate,
      pageSize: paginate ? 10 : undefined,
    })
  }

  private getCustomData(url: string, onNext: (res) => void, hasPrefix = true) {
    let prefix = ''
    if (hasPrefix) {
      prefix = this.baseUrl + '/'
    }
    this.crudService
      .getSingleData<any>(`${prefix}${url}`)
      .pipe(take(1))
      .subscribe(onNext)
  }

  initializeSelectData() {
    this.entityData = this.getDataSource('entidade_por_municipio', false)
    this.countryData = this.getDataSource('pais', false)
    this.getCustomData(
      'tipo_ato_contratual',
      res => (this.contractActTypeData = res?.dados),
    )
    this.getCustomData(
      'tipo_contrato',
      res => (this.contractTypeData = new DataSource(res?.dados)),
    )
    this.getCustomData(
      'forma_pagamento',
      res => (this.formPaymentData = new DataSource(res?.dados)),
    )
    this.getCustomData(
      'tipo_garantia',
      res => (this.guaranteeTypeData = new DataSource(res?.dados)),
    )
    this.getCustomData(
      'regime_execucao',
      res => (this.executionRegimeTypeData = new DataSource(res?.dados)),
    )
    this.getCustomData(
      'tipo_multa',
      res => (this.fineTypeData = new DataSource(res?.dados)),
    )
    this.getCustomData(
      'unidade_prazo_medida',
      res => (this.deadLineTypeData = res?.dados),
    )
    this.getCustomData('', res => (this.filterProviderData = res.dados))
    this.getCustomData(
      'licitacao/contrato/entidade_exercicio',
      res => (this.entityExerciseData = res?.dados),
      false,
    )

    if (!this.uuid) {
      this.entityData.load().then(() => {
        this.model
          .get('entidadeOrigem.uuid')
          .patchValue(this.userService.userData.entidadeUuid)
      })
      this.countryData.load().then((res: any[]) => {
        this.model
          .get('pais')
          .patchValue(res.find(val => val.nome == 'Brasil')?.uuid)
      })
    } else {
      this.entityData.load()
      this.countryData.load()
    }
  }

  loadHandler() {
    this.model
      .get('semLicitacao')
      .valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe(val => {
        if (val) {
          this.model.get('licitacao').reset()
        }
      })
    this.model
      .get('licitacao.numero')
      .valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe(val => {
        this.model.get('licitacao.entidade').reset()
        this.model.get('licitacao.exercicio').reset()
        this.model.get('licitacao.modalidade').reset()
        this.model.get('fornecedor').reset()
        if (val) {
          this.uuidLicitation = val?.uuid;
          this.model
            .get('licitacao.entidade')
            .patchValue(val?.exercicio?.entidade?.nome)
          this.model
            .get('licitacao.exercicio')
            .patchValue(val?.exercicio?.exercicio)
          this.model
            .get('licitacao.modalidade')
            .patchValue(val?.tipoModalidadeLicitacaoTce?.nome)
          this.model.get('licitacao.objeto').patchValue(val?.resumoObjeto)
        }
      })
    this.model
      .get('multa.tipo')
      .valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe(val => {
        if (!val) {
          this.model.get('multa.descricao').reset()
        }
      })
    this.model
      .get('tipoGarantia.uuid')
      .valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe(val => {
        if (!val) {
          this.model.get('descricaoGarantia').reset()
        }
      })
    this.model
      .get('entidadeOrigem.uuid')
      .valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe(val => {
        const loggEntity = this.userService.userData.entidadeUuid
        if (val != loggEntity) {
          this.dialogService
            .open(ConfirmationComponent, {
              context: {
                confirmationContent: {
                  body:
                    'Entidade de origem diferente da entidade atual deve ser informado' +
                    ' para os casos que a entidade atual está assumindo contratos de outra' +
                    ' entidade (da entidade de origem), nos casos de fusão, cisão, etc. Confirma?',
                },
              },
              closeOnBackdropClick: false,
              closeOnEsc: false,
            })
            .onClose.pipe(take(1))
            .subscribe(res => {
              if (!res) {
                this.model
                  .get('entidadeOrigem.uuid')
                  .patchValue(loggEntity, { emitEvent: false })
              }
            })
        }
      })
    this.otherHandlers()
  }

  private getSingleData(url: string, params: object, onNext: (res: any) => void) {
    this.loading = true
    this.crudService.getSingleData<any>(url, params)
      .pipe(take(1), finalize(() => this.loading = false))
      .subscribe(onNext)
  }

  private contractActTypeHandler = (typeUuid: string) => {
    this.model.get('numero').reset(undefined, { emitEvent: false })
    this.model.get('numeroTce').reset(undefined, { emitEvent: false })
    if (typeUuid) {
      this.validateContractNumber(typeUuid)
    }
  }

  private contractNumberHandler = (contractNumber: number) => {
    const typeUuid = this.tipoAtoContratual
    const errorMessage =
      'Número de contrato já utilizado para este tipo de Contrato, por favor informe outro.'
    if (!contractNumber) {
      return
    }
    if (this.uuid && contractNumber == this.currentNumbers[0]) {
      this.model.get('numero').setErrors(null)
      return
    }
    this.getSingleData(
      'licitacao/contrato/existe_contrato_por_numero',
      {
        numero: contractNumber,
        typeActContractUuid: typeUuid,
      },
      res => {
        if (res?.dados === 'S') {
          this.model.get('numero').setErrors({ customError: errorMessage })
        } else {
          this.model.get('numero').setErrors(null)
        }
      }
    )
  }

  private contractNumberTceHandler = (tceNumber: number) => {
    const typeUuid = this.tipoAtoContratual
    const errorMessage =
      'Número de contrato já utilizado para este tipo de Contrato, por favor informe outro.'
    if (!tceNumber) {
      return
    }
    if (this.uuid && this.currentNumbers[1] && tceNumber == this.currentNumbers[1]) {
      this.model.get('numeroTce').setErrors(null)
      return
    }
    this.getSingleData(
      'licitacao/contrato/existe_contrato_por_numero_tce',
      {
        numero: tceNumber,
        typeActContractUuid: typeUuid,
      },
      res => {
        if (res?.dados === 'S') {
          this.model.get('numeroTce').setErrors({ customError: errorMessage })
        } else {
          this.model.get('numeroTce').setErrors(null)
        }
      },
    )
  }

  private otherHandlers() {
    this.model.get('tipoAtoContratual').valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.contractActTypeHandler)
    
    this.model.get('numero').valueChanges
      .pipe(debounceTime(500), takeUntil(this.destroy$))
      .subscribe(this.contractNumberHandler)
    
    this.model.get('numeroTce').valueChanges
      .pipe(debounceTime(500), takeUntil(this.destroy$))
      .subscribe(this.contractNumberTceHandler)
  }

  verificarObrigarPeriodoExecucao(){
    this.crudService.getData(`licitacao/contrato/obrigar_periodo_execucao`).subscribe(res => {
      
      this.obrigarPeriodoExecucao = res.dados[0] === 'S';  
      
      const periodoExecucaoGroup = this.model.get('periodoExecucao');

      const inicioControl = periodoExecucaoGroup.get('inicio');
      const fimControl = periodoExecucaoGroup.get('fim');

      if(this.obrigarPeriodoExecucao){
        inicioControl.setValidators([Validators.required]);
        fimControl.setValidators([Validators.required]);
      }else{
        inicioControl.clearValidators();
        fimControl.clearValidators();
      }
      inicioControl.updateValueAndValidity();
      fimControl.updateValueAndValidity();
    });
    
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      codigo: [],
      numero: [],
      exercicio: [+this.userService.userData.exercicio],
      descricaoObjeto: [],
      entidadeOrigem: this.builder.group({
        uuid: [undefined, Validators.required],
      }),
      tipoAtoContratual: [undefined, Validators.required],
      numeroTce: [],
      inclusaoTce: [undefined, Validators.required],
      inclusaoTceParteContrato: [undefined, Validators.required],
      semLicitacao: [false],
      licitacao: this.builder.group({
        uuid: [],
        numero: [],
        entidade: [],
        exercicio: [],
        modalidade: [],
        objeto: [],
      }),
      valorOriginal: [undefined, [Validators.required, Validators.min(0)]],
      valorAcrescimo: [],
      valorAnulaccao: [],
      totalAditivos: [],
      valorAtualizado: [],
      vigenciaOriginal: this.builder.group({
        uuid: [],
        inicio: [undefined, Validators.required],
        prazo: [],
        tipoPrazo: [],
        fim: [undefined, Validators.required],
      }),
      periodoExecucao: this.builder.group({
        uuid: [],
        inicio: [undefined],
        prazo: [],
        tipoPrazo: [],
        fim: [undefined],
      }),
      tipoContrato: [undefined, Validators.required],
      fundamentoLegal: [],
      formaPagamento: [undefined, [Validators.required, Validators.maxLength(128)]],
      tipoFormaPagamento: this.builder.group({
        uuid: [undefined, Validators.required],
      }),
      dataEntrega: [],
      local: [undefined, Validators.required],
      pais: [],
      descricaoGarantia: [],
      tipoGarantia: this.builder.group({
        uuid: [],
      }),
      fornecedor: [undefined, Validators.required],
      representante: [undefined, Validators.required],
      tipoRegimeExecucao: [undefined, Validators.required],
      previsaoSubcontratacao: [undefined, Validators.required],
      fornecimentoMedio: [undefined, Validators.required],
      recursos: this.builder.group({
        uuid: [],
        proprios: [undefined, Validators.min(0)],
        estaduais: [undefined, Validators.min(0)],
        federais: [undefined, Validators.min(0)],
        operacoesCredito: [undefined, Validators.min(0)],
        total: [undefined, Validators.required],
      }),
      assinatura: this.builder.group({
        data: [],
        contratante: [],
        contratada: [],
        testemunhaContratante: [],
        testemunhaContratada: [],
        publicarInternet: [true],
        atendimentoCovid: [false],
      }),
      multa: this.builder.group({
        tipo: [],
        descricao: [],
      }),
      dataInclusaoTceSiafic: [],
      previsaoCessaoContratual: [false],
      controlarSaldoSeparadamente: [false],
      dataProrrogacaoCompras: [],
      bloquearRequisicaoCompra: [false],
      log: this.builder.group({
        logCreate: [],
        logUpdate: [],
      }),
    })
  }

  entityDisplay(item) {
    return item && `${item?.codigo} - ${item?.nome}`
  }

  loadPageData(uuid: any) {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        finalize(() => (this.loading = false)),
        take(1),
      )
      .subscribe(res => {
        this.loadForm(res.dados)
        this.currentNumbers = [
          res.dados.numero, res.dados.numeroTce
        ]
        this.licitacaoUuid = res.dados.licitacao?.uuid
        this.loadHandler()
        const type: any = res.dados.tipoAtoContratual
        this.validateContractNumber(type.uuid)
      })
  }

  loadForm(data) {
    const formData = {
      ...data,
      pais: data?.pais?.uuid,
      vigenciaOriginal: {
        ...data.vigenciaOriginal,
        tipoPrazo: data.vigenciaOriginal?.tipoPrazo?.uuid,
      },
      periodoExecucao: {
        ...data.periodoExecucao,
        tipoPrazo: data.periodoExecucao?.tipoPrazo?.uuid,
      },
      licitacao: {
        numero: data?.licitacao,
        entidade: data?.licitacao?.exercicio?.entidade?.nome,
        exercicio: data?.licitacao?.exercicio?.exercicio,
        modalidade: data?.licitacao?.tipoModalidadeLicitacaoTce?.nome,
        objeto: data?.licitacao?.resumoObjeto,
      },
      multa: data.multa
        ? { tipo: data?.multa?.tipo?.uuid, descricao: data?.multa?.descricao }
        : null,
      semLicitacao: !data.licitacao?.uuid,
      assinatura: {
        ...data.assinatura,
        publicarInternet: data.assinatura.publicarInternet == 'S',
        atendimentoCovid: data.assinatura.atendimentoCovid == 'S',
      },
      previsaoCessaoContratual: data.previsaoCessaoContratual == 'S',
      controlarSaldoSeparadamente: data.controlarSaldoSeparadamente == 'S',
      bloquearRequisicaoCompra: data.bloquearRequisicaoCompra == 'S',
    }
    this.uuidLicitation = data?.licitacao?.uuid;
    this.model.patchValue(formData, { emitEvent: false })
    if (!formData?.licitacao?.uuid) {
      this.model.get('licitacao').disable({ emitEvent: false })
    }
    this.loadSelectFields([
      'tipoAtoContratual',
      'tipoContrato',
      'tipoRegimeExecucao',
      'tipoGarantia',
    ])
  }

  loadSelectFields(fields: any[]) {
    fields.forEach(element => {
      this.model
        .get(`${element}`)
        .patchValue(this.model.get(`${element}`).value?.uuid)
    })
  }

  prepare(formData: any) {
    delete formData?.semLicitacao
    delete formData?.exercicio
    const dto = {
      ...formData,
      assinatura: {
        ...formData.assinatura,
        publicarInternet: formData.assinatura.publicarInternet ? 'S' : 'N',
        atendimentoCovid: formData.assinatura.atendimentoCovid ? 'S' : 'N',
        contratante: formData.assinatura.contratante
          ? { uuid: formData.assinatura.contratante?.uuid }
          : null,
        contratada: formData.assinatura.contratada
          ? { uuid: formData.assinatura.contratada?.uuid }
          : null,
        testemunhaContratante: formData.assinatura.testemunhaContratante
          ? { uuid: formData.assinatura.testemunhaContratante?.uuid }
          : null,
        testemunhaContratada: formData.assinatura.testemunhaContratada
          ? { uuid: formData.assinatura.testemunhaContratada?.uuid }
          : null,
      },
      vigenciaOriginal: {
        ...formData.vigenciaOriginal,
        tipoPrazo: formData.vigenciaOriginal.tipoPrazo
          ? { uuid: formData.vigenciaOriginal.tipoPrazo }
          : null,
      },
      periodoExecucao: {
        ...formData.periodoExecucao,
        tipoPrazo: formData.periodoExecucao.tipoPrazo
          ? { uuid: formData.periodoExecucao.tipoPrazo }
          : null,
      },
      numero: +formData?.numero,
      previsaoCessaoContratual: formData.previsaoCessaoContratual ? 'S' : 'N',
      controlarSaldoSeparadamente: formData.controlarSaldoSeparadamente
        ? 'S'
        : 'N',
      bloquearRequisicaoCompra: formData.bloquearRequisicaoCompra ? 'S' : 'N',
      multa: formData?.multa?.tipo
        ? {
          tipo: { uuid: formData?.multa.tipo },
          descricao: formData?.multa?.descricao,
        }
        : null,
      tipoAtoContratual: formData.tipoAtoContratual
        ? { uuid: formData.tipoAtoContratual }
        : null,
      tipoContrato: formData.tipoContrato
        ? { uuid: formData.tipoContrato }
        : null,
      tipoRegimeExecucao: formData.tipoRegimeExecucao
        ? { uuid: formData.tipoRegimeExecucao }
        : null,
      pais: formData?.pais ? { uuid: formData?.pais } : null,
      licitacao: formData.licitacao.numero
        ? { uuid: formData.licitacao.numero?.uuid }
        : null,
      representante: formData.representante
        ? { uuid: formData.representante?.uuid }
        : null,
      fornecedor: formData.fornecedor
        ? { uuid: formData.fornecedor?.uuid }
        : null,
      local: formData.local ? { uuid: formData.local?.uuid } : null,
      numeroTce: Number(formData.numeroTce),
    }
    return dto
  }

  getTotal() {
    const { proprios, estaduais, federais, operacoesCredito } =
      this.model.get('recursos').value
    const total =
      Number(proprios) +
      Number(estaduais) +
      Number(federais) +
      Number(operacoesCredito)

    if (total > 0) this.model.get('recursos').get('total').patchValue(total)
    else this.model.get('recursos').get('total').patchValue(null)

    return total
  }

  private _validateSiaficDate(date: Date) {
    const dateValidator = DateParser.getDateObjectFromDateString('2024-06-01')
    if (date < dateValidator) {
      this.dialogService.open(ConfirmationComponent, {
        context: {
          dialogTitle: 'Aviso',
          bottomLeftButtonVisible: false,
          confirmationContent: {
            body: 'A data de inclusão do SIAFIC deve ser maior ou igual a 01 de junho de 2024.',
            confirmType: 'success',
            confirmText: 'OK'
          }
        }
      })
    }
    return date >= dateValidator
  }

  submit() {
    const siaficDate = this.model.get('dataInclusaoTceSiafic').value
    if (siaficDate) {
      if (!this._validateSiaficDate(DateParser.getDateObjectFromDateString(siaficDate))) {
        return
      }
    }
    this.loading = true
    this.crudService.getSingleObject<ContractValidateDto>(
      `licitacao/contrato/verificar_certificado_fornecedor`,
      {
        fornecedorUuid: this.model.get('fornecedor').value?.uuid,
        data: this.model.get('inclusaoTce').value
      }
    )
      .pipe(take(1), finalize(() => this.loading = false))
      .subscribe({next: res => {
        if (res.dados.valorParametro == 3 || res.dados.certidoesValidas) {
          this._confirm()
        } else {
          this.dialogService.open(ContractProviderDialogComponent, {
            context: {
              dialogTitle: 'Aviso',
              confirmPostButton: res.dados.valorParametro == 1,
              certificates: res.dados.invalidas.map(c => c.nome),
              provider: res.dados.fornecedor
            },
            closeOnEsc: false,
            closeOnBackdropClick: false
          })
            .onClose
            .pipe(take(1), filter(res => res))
            .subscribe(() => this._confirm())
        }
      }})
  }

  private _confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue())
      let req: Observable<any>
      if (!this.uuid) {
        req = this.service.post(dto)
      } else {
        req = this.service.put(dto, this.uuid)
      }
      this.loading = true
      req
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(res => {
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: `Contrato ${!this.uuid ? 'cadastrado' : 'atualizado'
              }(a) com sucesso!`,
          })
          this.model.markAsPristine()
          if (!this.uuid) {
            const uri = `contrato/edit/${res.body.dados.uuid}`
            this.router
              .navigateByUrl('/', { skipLocationChange: true })
              .then(() => this.router.navigate([uri]))
          }
        })
    }
  }

  public uriGridProvider(): string {
    let uriBySearchField = 'licitacao/contrato/fornecedor'
    if (this.uuidLicitation) {
      uriBySearchField += `?licitacaoUuid=${this.uuidLicitation}`
    }

    return uriBySearchField
  }

  public onProviderSearchDialog() {
    this.dialogService
      .open(ContractProviderSearchDialogComponent, {
        context: {},
      })
      .onClose.pipe(take(1))
      .subscribe(res => {
        this.model.get('fornecedor').patchValue(res)
      })
  }

  onLicitationSearchClick(){
    this.dialogService.open(PurchaseRequisitionLicitationSearchComponent, {
      context: {
        filter: ['tipoSituacaoLicitacaoTce.nome', '=', 'Homologada'],
        url: 'licitacao/contrato/licitacao',
        entityExerciseUrl: 'licitacao/contrato/entidade_exercicio'
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose
      .pipe(take(1), filter(res => res))
      .subscribe(res => this.model.get('licitacao.numero').patchValue(res[0]))
  }

  public get semLicitacao(): boolean {
    return this.model?.get('semLicitacao')?.value
  }

  public get tipoAtoContratual() {
    return this.model?.get('tipoAtoContratual')?.value
  }

  public get tipoMulta() {
    return this.model?.get('multa.tipo')?.value
  }

  public get tipoGarantia() {
    return this.model?.get('tipoGarantia.uuid')?.value
  }

  public get warrantyTypeCode() {
    if(this.tipoGarantia){
      return this.guaranteeTypeData.items().filter(item => item.uuid == this.tipoGarantia)[0]?.codigo;
    } else {
      return null;
    }
  }

  public get entidadeOrigem() {
    return this.model?.get('entidadeOrigem.uuid')?.value
  }

  public get licitacao() {
    return this.model?.get('licitacao.numero')?.value?.uuid
  }

  get log() {
    return this.model?.get('log') as FormGroup
  }

  cancel() {
    this.router.navigate(['contrato'])
  }
}
