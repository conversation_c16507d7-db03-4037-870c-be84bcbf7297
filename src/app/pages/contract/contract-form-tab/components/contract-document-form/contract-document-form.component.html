<eqp-nebular-dialog
  dialogTitle="Documento"
  dialogSize="small"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="model.invalid || model.pristine"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonTitle]="'Voltar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="cancel()"
>
<eqp-loadin *ngIf="loading"></eqp-loadin>
  <ng-container [formGroup]="model">
    <div class="row mb-3">
      <div class="col-12">
        <eqp-nebular-select
          label="Documento"
          [size]="'small'"
          [required]="true"
          formControlName="documento"
          valueExpr="uuid"
          displayExpr="nome"
          [dataSource]="documentOptions"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <eqp-nebular-select
          label="Modelo"
          [size]="'small'"
          [required]="true"
          formControlName="modelo"
          valueExpr="uuid"
          displayExpr="nome"
          [dataSource]="modelOptions"
        ></eqp-nebular-select>
      </div>
    </div>
  </ng-container>
</eqp-nebular-dialog>
