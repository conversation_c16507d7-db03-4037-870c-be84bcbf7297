import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import { ContractManageService } from '@pages/contract/services/contract-manage.service';
import DataSource from 'devextreme/data/data_source';
import { Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, switchMap, take } from 'rxjs/operators';
import { CrudService } from './../../../../../@common/services/crud.service';
import { ContractDocumentService } from '@pages/contract/services/contract-document.service';

@Component({
  selector: 'eqp-contract-document-form',
  templateUrl: './contract-document-form.component.html',
  styleUrls: ['./contract-document-form.component.scss']
})
export class ContractDocumentFormComponent implements OnInit {

  constructor(
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ContractDocumentFormComponent>,
    private service: ContractDocumentService,
    private toastr: ToastrService,
    private crudService: CrudService
  ) { }

  @Input() uuid: string
  @Input() parentUuid: string

  public loading: boolean = false;
  public model: FormGroup;

  documentOptions: DataSource;
  modelOptions: DataSource;

  ngOnInit(): void {
    this.loadSelect();
    this.startForm(); 
  }

  startForm(){
    this.model = this.getNewModel();
    
    this.loadDocumentHandler();
    if(this.uuid) {
      this.loadForm();
    } 
  }

  loadDocumentHandler(){
    this.model.get('documento').valueChanges
    .pipe(
      distinctUntilChanged()
    ).subscribe(res => this.fetchModelsByDocument(res))
  }
  
  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      modelo: [],
      documento: [],
    })
  }

  fetchModelsByDocument(documentUuid: string){
    this.modelOptions = new DataSource({
      store: this.crudService.getDataSourceFiltro('uuid', 'licitacao/contrato/processo_documento_modelo', 10, 'processoDocumento.uuid', documentUuid),
      pageSize: 10,
      paginate: true,
    })
  }

  loadSelect(filter?: any[]){
    this.documentOptions = new DataSource({
      store: this.crudService.getDataSourceFiltro('uuid', 'licitacao/contrato/processo_documento', 10),
      pageSize: 10,
      paginate: true,
      filter
    })
  }

  private loadForm() {
    this.loading = true
    this.service.getIndividual( this.parentUuid, this.uuid)
    .pipe(take(1))
    .subscribe({
      next: (res) => {
        let dto: any = {
          modelo: res.dados.processoDocumentoModelo?.uuid,
          documento: res.dados.processoDocumentoModelo?.processoDocumento.uuid
        }
        this.model.patchValue(dto)
        this.loading = false
      },
      error: (error) => this.loading = false
    })
  }

  private prepare(formData): any {
    const documentModelUuid = formData?.modelo
    if(documentModelUuid){
      return {
        processoDocumentoModelo: {
          uuid: formData?.modelo
        }
      }
    }else{
      return null
    }
  }

  public confirm() {
    this.loading = true
    const dto = this.prepare(this.model.getRawValue())
    let req: Observable<any>
    if (this.uuid) {
      req = this.service.put(this.parentUuid, dto, this.uuid)
    } else {
      req = this.service.post(this.parentUuid, dto)
    }
    req
      .pipe(take(1), finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Documento ${
            this.uuid ? 'atualizado' : 'cadastrado'
          } com sucesso`,
          success: true,
        })
        this.dialogRef.close(res.body.dados)
      })
  }

  public cancel() {
    this.dialogRef.close(null)
  }

}
