<dx-data-grid
  id="contract-attachment-grid"
  keyExpr="uuid"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [dataSource]="dataSource"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="true"
  [remoteOperations]="true"
  (onToolbarPreparing)="onToolbarPreparing($event)"
>
  <dxo-export
    [enabled]="true"
    [excelWrapTextEnabled]="true"
    [excelFilterEnabled]="true"
    [fileName]="'Anexos'"
  ></dxo-export>

  <dxo-paging [pageSize]="10"></dxo-paging>

  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <dxo-filter-row [visible]="true"></dxo-filter-row>

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar anexo"
  ></dxo-search-panel>

  <dxo-editing
    mode="form"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="true"
    [useIcons]="true"
  ></dxo-editing>

  <dxi-column caption="Nome" dataField="nomeInformado"></dxi-column>
  <dxi-column [allowSorting]="false" [width]="120" caption="Publicar no portal" dataField="publicaInternet">
    <dxo-lookup
      [dataSource]="[
        { text: 'Sim', value: 'YES' },
        { text: 'Não', value: 'NO' }
      ]"
      valueExpr="value"
      displayExpr="text"
    ></dxo-lookup
  ></dxi-column>
  <dxi-column
    dataField="uuid"
    caption=""
    [width]="120"
    [allowFiltering]="false"
    [allowEditing]="false"
    [allowSorting]="false"
    cellTemplate="acaoColumn"
  ></dxi-column>

  <div *dxTemplate="let data of 'acaoColumn'">
    <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
      <a
        title="Baixar arquivo"
        (click)="downloadFile(data)"
        class="dx-link dx-link-delete fas fa-arrow-alt-circle-down dx-link-icon btn-icon-grid"
      >
      </a>
      <a
        title="Editar"
        (click)="edit(data.value)"
        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
      >
      </a>
      <a
        title="Remover"
        (click)="remove(data.value)"
        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
      >
      </a>
    </div>
  </div>
</dx-data-grid>
