import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { GovernmentTransferService } from '@pages/contract/services/government-transfer.service';
import DataSource from 'devextreme/data/data_source';
import { finalize, take } from 'rxjs/operators';
import { ContractGovernmentTransferFormDialogComponent } from '../contract-government-transfer-form/contract-government-transfer-form-dialog.component';

@Component({
  selector: 'eqp-contract-government-transfer',
  templateUrl: './contract-government-transfer.component.html',
  styleUrls: ['./contract-government-transfer.component.scss']
})
export class ContractGovernmentTransferComponent implements OnInit {
  pageTitle: 'Transferências Governamentais'
  loading: boolean = false
  dataSource: DataSource

  parentUuid: string

  constructor(
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private crudService: CrudService,
    private service: GovernmentTransferService,
    private dialogService: NbDialogService
  ) { }

  ngOnInit(): void {
    this.parentUuid = this.route.snapshot.params?.uuid

    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `licitacao/contrato/${this.parentUuid}/transferencia_governamental`,
        10
      ),
      paginate: true,
      pageSize: 10
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Transferência Governamental'
      event.toolbarOptions.items[0].options.hint = 'Nova Transferência Governamental'
      event.toolbarOptions.items[0].options.onClick = () => this.edit()
    }
  }

  public edit(data?: any) {
    this.dialogService.open(ContractGovernmentTransferFormDialogComponent, {
      context: {
        parentUuid: this.parentUuid,
        uuid: data?.value,
        type: data?.data?.tipo
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose.pipe().subscribe(res => {
      this.dataSource.reload()
    })
  }

  public remove(data: any){
    const transferTypeRule = {
      'Municipal': {
        filter: 'MUNICIPAL'
      },
      'Federal': {
        filter: 'FEDERAL'
      },
      'Estadual':  {
        filter: 'ESTADUAL'
      },
    }
    this.dialogService.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          body: 'Confirma a exclusão da transferência governamental?'
        }
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose.pipe(take(1)).subscribe(res => {
      if (res === true) {
        this.loading = true
        this.service
          .delete(this.parentUuid, data.value, transferTypeRule[data?.data?.tipo]?.filter)
          .pipe(take(1), finalize(() => (this.loading = false)))
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: `Transferência Governamental removida com sucesso.`,
              })
              this.dataSource.reload()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

}
