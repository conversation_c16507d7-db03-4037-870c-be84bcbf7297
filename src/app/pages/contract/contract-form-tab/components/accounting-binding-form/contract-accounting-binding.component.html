<eqp-nebular-dialog
[formGroup]="model" 
dialogTitle="Vinculação contábil"
[dialogSize]="'extra-large'"
[spinnerActive]="loading"
[spinnerStatus]="'info'"
[bottomLeftButtonText]="'Voltar'"
[bottomLeftButtonVisible]="true"
[bottomLeftButtonIconVisible]="true"
[bottomLeftButtonIcon]="'fas fa-undo-alt'"
[bottomLeftButtonId]="'dispose-info-dialog'"
[bottomLeftButtonTitle]="'Voltar'"
[bottomLeftButtonDisabled]="false"
(bottomLeftButtonEmitter)="dispose()"

[rightFirstButtonText]="'Confirmar'"
[rightFirstButtonVisible]="true"
[rightFirstButtonIconVisible]="true"
[rightFirstButtonDisabled]="
   model.invalid ||
   model.pristine ||
   !((biddind?.uuid && nivelPermissao === 'EDITOR') || nivelPermissao === 'FULL')
"
[rightFirstButtonIcon]="'fas fa-check-circle'"
[rightFirstButtonId]="'confirm-accounting-binding'"
[rightFirstButtonTitle]="'Confirmar'"
(rightFirstButtonEmitter)="confirm()"
>
<div class="container">
   <div class="row mb-3">
      <div class="col col-12 col-md-3 col-xxl-2">
         <eqp-nebular-select
            formControlName="tipoVinculacaoContabil"
            label="Tipo"
            [dataSource]="accountingBindingData"
            [displayExpr]="'nome'"
            [valueExpr]="'uuid'"
            [required]="true"
         ></eqp-nebular-select>
      </div>
      <div class="col col-12 col-md">
         <eqp-search-field
            label="Conta contábil"
            [uri]="baseUrl + '/plano_contabil'"
            [filter]="accountFilter"
            formControlName="contaContabil"
            dialogTitle="Conta contábil"
            searchColumnsType="accountPlanColumns"
            codeKey="codigoReduzido"
            [multipleNames]="['codigo', 'nome']"
            [returnAllData]="true"
            [required]="true"
         ></eqp-search-field>
      </div>
   </div>
   <div class="row">
      <div class="col col-12 col-md-3 col-xxl-2">
         <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            formControlName="saldo"
            [label]="'Saldo'"
            placeholder=""
            [required]="!(accountBindingType != 'POTENCIAL_PASSIVO')"
            [readonly]="accountBindingType != 'POTENCIAL_PASSIVO'"
            ></eqp-nebular-input>
      </div>
      <div class="col col-12 col-md">
         <eqp-nebular-select
            formControlName="eventoContabilConfig"
            [label]="'Evento'"
            [dataSource]="accountingEventConfigData"
            displayExpr="eventoContabil.nomeNumero"
            [valueExpr]="'uuid'"
            [required]="requiredEvent"
            [disabled]="!requiredEvent"
         ></eqp-nebular-select>
      </div>
      <div class="col col-12 col-md-3 col-xxl-2">
         <eqp-nebular-input
            [style]="'basic'"
            [type]="'number'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="configuracao"
            name="Configuração"
            label="Configuração"
            placeholder=""
            [readonly]="true"
         ></eqp-nebular-input>
      </div>
   </div>
</div>
</eqp-nebular-dialog>