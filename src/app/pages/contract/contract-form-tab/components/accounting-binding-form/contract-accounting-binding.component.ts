import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import { AccountingBindingInterface } from '@pages/contract/interfaces/accounting-binding';
import { AccountingBindingService } from '@pages/contract/services/accounting-binding.service';
import { MenuService } from '@pages/menu.service';
import { Observable, Subject, from } from 'rxjs';
import { take, finalize, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'eqp-contract-accounting-binding',
  templateUrl: './contract-accounting-binding.component.html',
  styleUrls: ['./contract-accounting-binding.component.scss']
})
export class ContractAccountingBindingComponent
  extends BaseTelasComponent implements OnInit, OnDestroy
{

  @Input() parentUuid: string
  @Input() biddind: AccountingBindingInterface
  @Input() baseUrl = 'licitacao/contrato'

  model: FormGroup
  loading: boolean = false
  requiredEvent = false

  accountingBindingData: any[] = []
  accountFilter: any[]
  accountingEventConfigData: any[] = []

  private unsub$ = new Subject<void>()

  constructor(
    public menuService: MenuService,
    public router: Router,
    private builder: FormBuilder,
    private service: AccountingBindingService,
    protected dialogRef: NbDialogRef<ContractAccountingBindingComponent>,
    private crudService: CrudService,
    private toastr: ToastrService
  ) {
    super(menuService, router)
    this.permissao('/contrato')
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadSelect()
    this.loadHandler()
    if (this.biddind?.uuid) {
      this.loadForm()
    }
  }

  ngOnDestroy(): void {
    this.unsub$.next()
    this.unsub$.complete()
  }

  private getNewModel(): FormGroup {
    const form = this.builder.group({
      uuid: [],
      tipoVinculacaoContabil: [undefined, Validators.required],
      contaContabil: [undefined, Validators.required],
      eventoContabilConfig: [undefined, Validators.required],
      saldo: [undefined, Validators.required],
      configuracao: []
    })
    form.get('eventoContabilConfig').disable({emitEvent: false})
    form.get('saldo').disable({emitEvent: false})
    return form
  }

  private loadForm() {
    this.accountingEventConfigData = [this.biddind.eventoContabilConfig]
    this.model.patchValue({
      ...this.biddind,
      tipoVinculacaoContabil: this.biddind?.tipoVinculacaoContabil?.uuid,
      eventoContabilConfig: this.biddind?.eventoContabilConfig?.uuid
    }, { emitEvent: false })
    const code = this.biddind.tipoVinculacaoContabil.codigo
    this.setAccountPlanFilter(code)
    if (code != 2) {
      this.accountBindTypeHandler(this.biddind.tipoVinculacaoContabil.uuid, true, code)
    }
  }

  private loadSelect(){
    this.crudService.getSingleData<any>(`${this.baseUrl}/tipo_vinculacao_contabil`)
      .pipe(take(1))
      .subscribe(res => {
        this.accountingBindingData = res?.dados
      })
  }

  private setAccountPlanFilter(code: number) {
    if (code == 1) {
      this.accountFilter = [
        ['codigo', 'startswith', '712'],
        'and',
        ['analitica', '=', 'S']
      ]
    } else {
      this.accountFilter = [
        ['codigo', 'startswith', '812'],
        'and',
        ['analitica', '=', 'S']
      ]
    }
  }

  private loadHandler() {
    this.model.get('tipoVinculacaoContabil').valueChanges
      .pipe(takeUntil(this.unsub$))
      .subscribe(this.accountBindTypeHandler)
    this.model.get('eventoContabilConfig').valueChanges
      .pipe(takeUntil(this.unsub$))
      .subscribe(this.accountEventHandler)
    this.model.get('contaContabil').valueChanges
      .pipe(takeUntil(this.unsub$))
      .subscribe(this.accountPlanHandler)
  }

  private accountEventHandler = (eventUuid: string) => {
    this.model.get('configuracao').reset()
    if (!eventUuid) {
      return
    }
    this.model.get('configuracao').patchValue(
      this.accountingEventConfigData
        .find(value => value?.uuid == eventUuid)?.numero
    )
  }

  private accountPlanHandler = plan => {
    this.model.get('eventoContabilConfig').disable({emitEvent: false})
    this.model.get('eventoContabilConfig').reset(undefined, {emitEvent: false})
    this.accountingEventConfigData = []
    const typeUuid = this.model.get('tipoVinculacaoContabil').value
    const code = this.accountingBindingData.find(type => type?.uuid == typeUuid)?.codigo
    this.requiredEvent = [1,3].includes(code) && !!plan?.uuid
    if (plan?.uuid && code != 2) {
      this.getAccountEvents(typeUuid, plan.uuid)
    }
  }

  private accountBindTypeHandler = (typeUuid: string, fromLoadForm = false, codeType?: number) => {
    if (!fromLoadForm) {
      this.model.get('eventoContabilConfig').disable({emitEvent: false})
      this.model.get('eventoContabilConfig').reset(undefined, {emitEvent: false})
      this.requiredEvent = false
      this.model.get('saldo').disable({emitEvent: false})
      this.model.get('contaContabil').reset(undefined, {emitEvent: false})
      this.model.get('saldo').reset(undefined, {emitEvent: false})
      this.accountingEventConfigData = []
    }
    if (!typeUuid) {
      return
    }
    let code: number
    if (codeType) {
      code = codeType
    } else {
      code = this.accountingBindingData.find(type => type?.uuid == typeUuid)?.codigo
    }
    const planUuid = this.model.get('contaContabil').value?.uuid
    if ([1,3].includes(code) && planUuid) {
      this.model.get('eventoContabilConfig').enable({emitEvent: false})
      this.getAccountEvents(typeUuid, planUuid, fromLoadForm)
    }
    this.requiredEvent = [1,3].includes(code) && !!planUuid
    console.log([[1,3].includes(code), this.model.get('contaContabil').value?.uuid])
    this.setAccountPlanFilter(code)
    if (code == 1) {
      this.model.get('saldo').enable({emitEvent: false})
    }
  }

  private getAccountEvents(typeUuid, planUuid, fromLoadForm = false) {
    this.loading = true
      from(
        this.crudService.getDataSourceFiltro(
          'uuid',
          `${this.baseUrl}/evento_contabil?tipoVinculacaoContabilUuid=${typeUuid}&contaContabilUuid=${planUuid}`,
          10
        ).load()
      )
        .pipe(take(1), finalize(() => this.loading = false))
        .subscribe(res => {
          this.accountingEventConfigData = res
          if (fromLoadForm) {
            this.model.get('eventoContabilConfig').patchValue(
              this.biddind.eventoContabilConfig?.uuid
            )
          }
        })
  }
  
  private prepare(formData: any){
    delete formData?.configuracao
    return {
      ...formData,
      tipoVinculacaoContabil: {
        uuid: formData.tipoVinculacaoContabil
      },
      contaContabil: {
        uuid: formData.contaContabil?.uuid
      },
      eventoContabilConfig: formData.eventoContabilConfig ? {
        uuid: formData.eventoContabilConfig
      } : null
    }
  }

  public confirm() {
    this.loading = true
    let obs$: Observable<any>
    if (this.biddind?.uuid) {
      obs$ = this.service.put(this.parentUuid, this.prepare(this.model.getRawValue()), this.biddind.uuid)
    } else {
      obs$ = this.service.post(this.parentUuid, this.prepare(this.model.getRawValue()))
    }
    obs$
      .pipe(take(1), finalize(() => this.loading = false))
      .subscribe({
        next: res => {
          this.toastr.send({
            success: true,
            message: `Vinculação contábil ${this.biddind?.uuid ? 'atualizada' : 'criada'} com sucesso.`
          })
          this.dialogRef.close(res)
        }
      })
  }

  get accountBindingType() {
    const typeArr = [
      'POTENCIAL_PASSIVO',
      'EXECUCAO',
      'EXECUTADAS'
    ]
    const typeUuid = this.model?.get('tipoVinculacaoContabil')?.value
    if (typeUuid) {
      const idx = this.accountingBindingData.find(val => val?.uuid == typeUuid)?.codigo - 1
      return typeArr[idx]
    }
    return null
  }

  dispose(){
    this.dialogRef.close(false)
  }
}
