import { Component, OnInit } from '@angular/core'

@Component({
  selector: 'eqp-contract-act',
  template: `<nb-route-tabset [tabs]="tabs"></nb-route-tabset>`,
  styles: [],
})
export class ContractActComponent implements OnInit {
  isEditing = false

  tabs = []
  currentSection: number = 0

  constructor() {}

  ngOnInit(): void {
    this.tabs = [
      {
        title: 'Atos',
        route: `./ato`,
        icon: 'home',
      },
      {
        title: 'Efeito de inexecução',
        route: `./efeitos-da-inexecucao`,
        icon: 'list',
      },
      {
        title: 'Responsabilidade administrativa',
        route: `./responsabilidade-administrativa`,
        icon: 'list',
      },
      {
        title: 'Certificados de regularidade',
        route: `./certificado-de-regularidade`,
        icon: 'list',
      },
      // {
      //   title: 'Publicação órgão oficial',
      //   route: `./publicacao-orgao-oficial`,
      //   icon: 'list',
      // },
    ]
  }
}
