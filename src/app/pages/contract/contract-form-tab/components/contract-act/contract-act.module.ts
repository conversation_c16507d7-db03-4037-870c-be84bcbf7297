import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'

import { CommonToolsModule } from '@common/common-tools.module'
import { SharedModule } from '@pages/shared/shared.module'
import { DxButtonModule, DxToolbarModule } from 'devextreme-angular'
import { ContractActListComponent } from './components/contract-act-list/contract-act-list.component'
import { ContractAdditionAttachmentTabComponent } from './components/contract-addition-dialog/components/contract-addition-attachment-tab/contract-addition-attachment-tab.component'
import { ContractAdditionDocumentTabComponent } from './components/contract-addition-dialog/components/contract-addition-document-tab/contract-addition-document-tab.component'
import { ContractAdditionItemDialogComponent } from './components/contract-addition-dialog/components/contract-addition-item-tab/contract-addition-item-dialog/contract-addition-item-dialog.component'
import { ContractAdditionItemIncreaseComponent } from './components/contract-addition-dialog/components/contract-addition-item-tab/contract-addition-item-increase/contract-addition-item-increase.component'
import { ContractAdditionItemListComponent } from './components/contract-addition-dialog/components/contract-addition-item-tab/contract-addition-item-list/contract-addition-item-list.component'
import { ContractAdditionItemLoadComponent } from './components/contract-addition-dialog/components/contract-addition-item-tab/contract-addition-item-load/contract-addition-item-load.component'
import { ContractAdditionDialogComponent } from './components/contract-addition-dialog/contract-addition-dialog.component'
import { ContractActRoutingModule } from './contract-act-routing.module'
import { ContractActComponent } from './contract-act.component'
import { DialogEditItemComponent } from './components/contract-addition-dialog/components/contract-addition-item-tab/dialog-edit-item/dialog-edit-item.component'

import { OfficialOrganPublicationListComponent } from './components/official-organ-publication-list/official-organ-publication-list.component'
import { OfficialOrganPublicationFormComponent } from './components/official-organ-publication-form/official-organ-publication-form.component'
import { AdministrativeResponsibilityListComponent } from './components/administrative-responsibility-list/administrative-responsibility-list.component'
import { AdministrativeResponsibilityDialogComponent } from './components/administrative-responsibility-dialog/administrative-responsibility-dialog.component'
import { NonExecutionEffectsListComponent } from './components/non-execution-effects-list/non-execution-effects-list.component'
import { NonExecutionEffectsDialogComponent } from './components/non-execution-effects-dialog/non-execution-effects-dialog.component'
import { RegularityCertificatesComponent } from './components/regularity-certificates/regularity-certificates.component'
import { ContractAdditionAttachmentFormComponent } from './components/contract-addition-dialog/components/contract-addition-attachment-form/contract-addition-attachment-form.component'
import { ContractAdditionPncpComponent } from './components/contract-addition-dialog/components/contract-addition-pncp/contract-addition-pncp.component'
import { StdDocumentEditorModule } from '@pages/shared/std-document-editor/std-document-editor.module'

@NgModule({
  declarations: [
    ContractActComponent,
    ContractActListComponent,
    ContractAdditionDialogComponent,
    ContractAdditionItemListComponent,
    ContractAdditionItemDialogComponent,
    ContractAdditionItemIncreaseComponent,
    ContractAdditionDocumentTabComponent,
    ContractAdditionAttachmentTabComponent,
    ContractAdditionItemLoadComponent,
    OfficialOrganPublicationListComponent,
    OfficialOrganPublicationFormComponent,
    AdministrativeResponsibilityListComponent,
    AdministrativeResponsibilityDialogComponent,
    DialogEditItemComponent,
    NonExecutionEffectsListComponent,
    NonExecutionEffectsDialogComponent,
    RegularityCertificatesComponent,
    ContractAdditionPncpComponent,
    ContractAdditionAttachmentFormComponent,
  ],
  imports: [
    CommonModule,
    ContractActRoutingModule,
    CommonToolsModule,
    DxToolbarModule,
    DxButtonModule,
    SharedModule,
    StdDocumentEditorModule,
  ],
})
export class ContractActModule {}
