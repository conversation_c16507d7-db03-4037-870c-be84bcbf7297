<eqp-nebular-dialog
  id="contract-addition-form-dialog"
  [dialogTitle]="'Ato contratual'"
  [dialogSize]="'extra-large'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="initialTab"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-addition-form-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="model.invalid || model.pristine"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-addition-form-dialog-button'"
  [bottomLeftButtonTitle]="'Voltar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>
  <ng-container [formGroup]="model">
    <eqp-loading *ngIf="loading"></eqp-loading>
    <nb-tabset (changeTab)="changeTabEvent($event)">
      <nb-tab tabTitle="Ato contratual" class="p-0">
        <nb-tabset>
          <nb-tab tabTitle="Tela I" style="position: relative">
            <eqp-loading *ngIf="contractActLoading"></eqp-loading>
            <div class="row mt-3">
              <div class="col-sm-4 col-lg-2">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  label="Código"
                  placeholder=""
                  formControlName="codigo"
                  [disabled]="true"
                >
                </eqp-nebular-input>
              </div>
              <div class="col-sm-8 col-lg-2">
                <eqp-nebular-select
                  label="Tipo de ato"
                  [size]="'small'"
                  formControlName="tipoAditivoAto"
                  [dataSource]="tipoAto"
                  (ngModelChange)="actTypeChange($event)"
                  displayExpr="nome"
                  valueExpr="uuid"
                  [disabled]="uuid"
                  [required]="true"
                ></eqp-nebular-select>
              </div>
              <div class="col-sm-6 col-lg-2">
                <eqp-nebular-select
                  label="Tipo do aditivo"
                  displayExpr="nome"
                  valueExpr="uuid"
                  formControlName="tipoAditivoContrato"
                  [size]="'small'"
                  [dataSource]="tipoAditivo"
                  (ngModelChange)="onChangeAddtiveType($event)"
                  [required]="additionTypeIsRequired"
                  [disabled]="uuid || !additionTypeIsRequired"
                ></eqp-nebular-select>
              </div>
              <div class="col-sm-6 col-lg-2">
                <eqp-field-date
                  formControlName="dataAssinatura"
                  name="dataAssinatura"
                  label="Data da assinatura"
                ></eqp-field-date>
              </div>
              <div class="col-sm-8 col-lg-2">
                <eqp-field-date
                  formControlName="dataInclusaoTce"
                  name="dataInclusaoTce"
                  label="Data Inclusão TCE"
                  [required]="true"
                ></eqp-field-date>
              </div>
              <div class="col-sm-4 col-lg-1 mt-4 p-1 d-flex">
                <eqp-nebular-toggle
                  [required]="true"
                  formControlName="flagApostilamento"
                  title="Apostilamento"
                  [disabled]="apostilleFlagDisabled"
                ></eqp-nebular-toggle>
                <label class="label ml-2 mt-2">Apostilamento</label>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col col-sm-12 col-lg-6">
                <nb-card class="footer">
                  <nb-card-header class="label text-uppercase">{{
                    'Novos Prazos'
                  }}</nb-card-header>
                  <nb-card-body>
                    <div class="row mb-3">
                      <div class="col-6">
                        <eqp-field-date
                          formControlName="dataTerminoVigencia"
                          name="terminoVigencia"
                          label="Término vigência"
                          [required]="requiredExpirationDate"
                        ></eqp-field-date>
                      </div>
                      <div class="col-6">
                        <eqp-field-date
                          formControlName="dataTerminoExecucao"
                          name="terminoExecucao"
                          label="Término execução"
                          [required]="requiredExecutionEndDate"
                        ></eqp-field-date>
                      </div>
                    </div>
                  </nb-card-body>
                </nb-card>
              </div>
              <div class="col col-sm-12 col-lg-6">
                <nb-card class="footer">
                  <nb-card-header class="label text-uppercase"
                    >Classificação TCE</nb-card-header
                  >
                  <nb-card-body>
                    <div class="row mb-3">
                      <div class="col-6">
                        <eqp-nebular-select
                          label="Tipo do aditivo"
                          [size]="'small'"
                          formControlName="tipoAditivoContratoUm"
                          [dataSource]="tipoAditivoContrato"
                          displayExpr="nome"
                          valueExpr="uuid"
                          [required]="
                            additiveTypeCode == 3 || !(actTypeCode == 2)
                          "
                        ></eqp-nebular-select>
                      </div>
                      <div class="col-6">
                        <eqp-nebular-select
                          label="Tipo de operação do aditivo"
                          size="small"
                          formControlName="tipoOperacaoAditivoContratoTceUm"
                          displayExpr="nome"
                          valueExpr="uuid"
                          (ngModelChange)="
                            onChangeOperationContractTceOne($event)
                          "
                          [dataSource]="operationAdditiveTypeOne"
                          [required]="
                            additiveTypeCode == 3 ||
                            !(actTypeCode == 2) ||
                            !!model.get('tipoAditivoContratoUm')?.value
                          "
                        ></eqp-nebular-select>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-6">
                        <eqp-nebular-select
                          [label]="
                            aditiveTypeContractTowIsRequired
                              ? 'Tipo do aditivo *'
                              : 'Tipo do aditivo'
                          "
                          size="small"
                          formControlName="tipoAditivoContratoDois"
                          displayExpr="nome"
                          valueExpr="uuid"
                          [dataSource]="tipoAditivoContrato"
                          [disabled]="!(additiveTypeCode == 3)"
                        ></eqp-nebular-select>
                      </div>
                      <div class="col-6">
                        <eqp-nebular-select
                          label="Tipo de operação do aditivo"
                          [size]="'small'"
                          formControlName="tipoOperacaoAditivoContratoTceDois"
                          [dataSource]="operationAdditiveTypeTwo"
                          displayExpr="nome"
                          valueExpr="uuid"
                          [disabled]="!(additiveTypeCode == 3)"
                          [required]="additiveTypeCode == 3"
                        ></eqp-nebular-select>
                      </div>
                    </div>
                  </nb-card-body>
                </nb-card>
              </div>
            </div>

            <div
              *ngIf="additiveTypeCode == 5 || additiveTypeCode == 6"
              class="row mt-3"
            >
              <div class="col-6">
                <eqp-search-field
                  label="Fornecedor"
                  formControlName="fornecedor"
                  [uri]="providerUrl()"
                  dialogTitle="Fornecedor"
                  searchColumnsType="providerColumns"
                  codeKey="pessoaCodigo"
                  nameKey="pessoaNome"
                  [returnAllData]="true"
                  [required]="true"
                ></eqp-search-field>
              </div>
              <div class="col-6">
                <eqp-search-field
                  label="Representante"
                  formControlName="pessoaRepresentante"
                  [uri]="'licitacao/contrato/pessoa'"
                  dialogTitle="Representante"
                  searchColumnsType="unityPlanColumns"
                  [returnAllData]="true"
                  [required]="true"
                ></eqp-search-field>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col col-sm-6 col-lg-2">
                <label class="label mb-1">{{
                  requiredContractualForecast
                    ? 'Previsão contratual *'
                    : 'Previsão contratual'
                }}</label>
                <nb-radio-group
                  class="d-flex"
                  formControlName="flagPrevisaoContratual"
                  name="previsaoContratual"
                  [required]="requiredContractualForecast"
                >
                  <nb-radio
                    *ngFor="let option of tipoPrevisaoContratualTemplate"
                    [value]="option.valor"
                  >
                    {{ option.texto }}
                  </nb-radio>
                </nb-radio-group>
              </div>
              <div class="col col-sm-6 col-lg-3">
                <eqp-nebular-input
                  [size]="'small'"
                  [required]="additiveTypeCode == 1 || additiveTypeCode == 3"
                  [shape]="'rectangle'"
                  formControlName="valor"
                  name="valor"
                  label="Valor"
                  placeholder="00,00"
                  [style]="'currency'"
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                >
                </eqp-nebular-input>
              </div>
              <div class="col col-sm-12 col-lg-7">
                <eqp-nebular-input
                  [style]="'basic'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="motivo"
                  name="motivo"
                  label="Motivo"
                  [required]="true"
                  placeholder=""
                >
                </eqp-nebular-input>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Tela II">
            <div class="row mt-3">
              <div class="col col-sm-12 col-lg-4">
                <eqp-nebular-select
                  label="Tipo redimensionamento de objeto do contrato"
                  formControlName="tipoRedimensionamentoContratoTce"
                  [dataSource]="tipoRedimensionamento"
                  displayExpr="nome"
                  valueExpr="uuid"
                  [size]="'small'"
                ></eqp-nebular-select>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-select
                  label="Índice de correção"
                  formControlName="indiceCorrecao"
                  [dataSource]="tipoIndiceCorrecao"
                  displayExpr="nome"
                  valueExpr="uuid"
                  [size]="'small'"
                ></eqp-nebular-select>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  label="Periodicidade (meses)"
                  placeholder=""
                  formControlName="periodicidadeReajuste"
                >
                </eqp-nebular-input>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-select
                  label="Mês do reajuste"
                  formControlName="mesReajuste"
                  [dataSource]="mesReajuste"
                  displayExpr="valor"
                  valueExpr="chave"
                  [size]="'small'"
                ></eqp-nebular-select>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-field-date
                  formControlName="dataProrrogacaoCompra"
                  name="dataProrrogacaoCompra"
                  label="Data de prorrogação de compras"
                ></eqp-field-date>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col col-lg-12">
                <eqp-nebular-select
                  label="Motivo da rescisão do contrato"
                  formControlName="tipoMotivoRescisaoContratoTce"
                  [dataSource]="tipoMotivoRescisao"
                  displayExpr="nome"
                  valueExpr="uuid"
                  [size]="'small'"
                  [required]="false"
                ></eqp-nebular-select>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-lg-3 p-1 d-flex">
                <eqp-nebular-toggle
                  formControlName="flagCovid19"
                  title="Atendimento - Covid 2019"
                ></eqp-nebular-toggle>
                <label class="label ml-2 mt-2">Atendimento - Covid 2019</label>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Tela III">
            <div class="row mt-3">
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-input
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorProrrogacao"
                  label="Valor prorrogação"
                  placeholder="00,00"
                  type="decimal"
                  [style]="'currency'"
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                >
                </eqp-nebular-input>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-input
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorAcrescimo"
                  label="Valor acréscimo"
                  placeholder="00,00"
                  [style]="'currency'"
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                >
                </eqp-nebular-input>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-input
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorReajuste"
                  label="Valor reajuste"
                  placeholder="00,00"
                  [style]="'currency'"
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                >
                </eqp-nebular-input>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-input
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorRecomposicao"
                  label="Valor recomposição"
                  placeholder="00,00"
                  [style]="'currency'"
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                >
                </eqp-nebular-input>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-input
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorSupressao"
                  label="Valor supressão"
                  placeholder="00,00"
                  [style]="'currency'"
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                >
                </eqp-nebular-input>
              </div>
              <div class="col col-sm-6 col-lg-2">
                <eqp-nebular-input
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="total"
                  label="Total"
                  [value]="getTotal()"
                  placeholder="00,00"
                  [style]="'currency'"
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  [readonly]="true"
                >
                </eqp-nebular-input>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-12">
                <eqp-nebular-input
                  [style]="'textArea'"
                  [rows]="6"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="descricao"
                  name="descricao"
                  label="Descrição"
                  placeholder="Descrição"
                >
                </eqp-nebular-input>
              </div>
            </div>
          </nb-tab>
        </nb-tabset>
      </nb-tab>
      <nb-tab [disabled]="!uuid" tabTitle="Itens" class="p-0">
        <eqp-contract-addition-item-list
          [parentUuid]="parentUuid"
          [uuid]="uuid"
          (loadedItems)="onLoadItems($event)"
        ></eqp-contract-addition-item-list>
      </nb-tab>
      <nb-tab [disabled]="!uuid" tabTitle="Documento" class="p-0">
        <eqp-contract-addition-document-tab
          [contractUuid]="parentUuid"
          [adictiveUuid]="uuid"
        ></eqp-contract-addition-document-tab>
      </nb-tab>
      <nb-tab [disabled]="!uuid" tabTitle="Anexos" class="p-0">
        <eqp-contract-addition-attachment-tab
          id="contractAdditionAttachmentId"
          [adictiveUuid]="uuid"
        ></eqp-contract-addition-attachment-tab>
      </nb-tab>
      <nb-tab
        [disabled]="!uuid"
        tabTitle="Publicação Órgão Oficial"
        class="p-0"
      >
        <eqp-official-organ-publication-list
          [parentUuid]="uuid"
        ></eqp-official-organ-publication-list>
      </nb-tab>
      <nb-tab
        [disabled]="!uuid"
        tabTitle="PNCP - Termo de contrato"
      >
        <app-contract-addition-pncp
          [additiveUuid]="uuid"
        ></app-contract-addition-pncp>
      </nb-tab>
    </nb-tabset>
  </ng-container>
</eqp-nebular-dialog>
