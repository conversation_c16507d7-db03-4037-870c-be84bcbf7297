<eqp-nebular-dialog
  dialogTitle="Item"
  [spinnerActive]="loading"
  spinnerSize="large"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonIcon]="'fas fa-check'"
  [rightFirstButtonId]="'submit-item-update'"
  (rightFirstButtonEmitter)="confirm()"
  [rightFirstButtonDisabled]="!hasChangesAfterLoad()"
  dialogSize="medium"
>
  <ng-container [formGroup]="model">
    <eqp-fieldset label="De">
      <div class="row">
        <div class="col-4">
          <eqp-nebular-input
            [disabled]="true"
            [style]="'basic'"
            [type]="'number'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="deQuantidade"
            name="deQuantidade"
            label="Quantidade "
            placeholder=""
          ></eqp-nebular-input>
        </div>
        <div class="col-4">
          <eqp-nebular-input
            [disabled]="true"
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="deValor"
            name="valor"
            label="Valor"
            placeholder=""
          ></eqp-nebular-input>
        </div>
        <div class="col-4">
          <eqp-nebular-input
            [disabled]="true"
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="deTotal"
            name="total"
            label="Total"
            placeholder=""
          ></eqp-nebular-input>
        </div>
      </div>
    </eqp-fieldset>
    <eqp-fieldset label="Para">
      <div class="row">
        <div class="col-4">
          <eqp-nebular-input
            [disabled]="balanceControlTypeCode != 0"
            [style]="'basic'"
            [type]="'number'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="paraQuantidade"
            name="quantidade"
            label="Quantidade"
            placeholder=""
          ></eqp-nebular-input>
        </div>
        <div class="col-4">
          <eqp-nebular-input
            [disabled]="balanceControlTypeCode != 1"
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="paraValor"
            name="valor"
            label="Valor"
            placeholder=""
          ></eqp-nebular-input>
        </div>
        <div class="col-4">
          <eqp-nebular-input
            [disabled]="true"
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="paraTotal"
            name="total"
            label="Total"
            placeholder=""
          ></eqp-nebular-input>
        </div>
      </div>
    </eqp-fieldset>
  </ng-container>
</eqp-nebular-dialog>
