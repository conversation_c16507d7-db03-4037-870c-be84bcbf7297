import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ContractAdditionItemDialogComponent } from './contract-addition-item-dialog.component';

describe('ContractAdditionItemDialogComponent', () => {
  let component: ContractAdditionItemDialogComponent;
  let fixture: ComponentFixture<ContractAdditionItemDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ContractAdditionItemDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractAdditionItemDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
