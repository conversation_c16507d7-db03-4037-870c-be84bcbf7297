import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { finalize, take } from 'rxjs/operators'
import { FormBuilder, FormGroup } from '@angular/forms'
import { Component, Input, OnInit } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { Subject } from 'rxjs'
import { ContractAdditionService } from '@pages/contract/services/contract-addition.service'
import { ContractAdditionItemInterface } from '@pages/contract/interfaces/contract-addition-item'

@Component({
  selector: 'eqp-contract-addition-item-increase',
  templateUrl: './contract-addition-item-increase.component.html',
  styleUrls: ['./contract-addition-item-increase.component.scss'],
})
export class ContractAdditionItemIncreaseComponent implements OnInit {
  @Input() parentUuid: string
  @Input() uuid: string

  dialogTitle: string = 'Gerar adicionais de itens'
  invalidUpdate: boolean = true
  loading: boolean = false

  model: FormGroup
  itens: ContractAdditionItemInterface[] = []
  currencyFormat = currencyFormat
  min: number = 0

  decimalFormat = {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  get porcentagem() {
    return this.model.get('porcentagem')?.value
  }

  constructor(
    private builder: FormBuilder,
    private toastr: ToastrService,
    private crudService: CrudService,
    private service: ContractAdditionService,
    private dialogRef: NbDialogRef<ContractAdditionItemIncreaseComponent>,
  ) {}

  ngOnInit(): void {
    if (this.uuid) this.fetchGrid()
    this.model = this.getNewModel()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      porcentagem: [],
    })
  }

  fetchGrid() {
    this.loading = true
    const url = `contrato/contrato/${this.parentUuid}/ato/${this.uuid}/item`
    this.crudService
      .getSingleData<any>(url, { take: 0 })
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        const itens = res.data.filter(
          item => item.quantidadeRequisitada > 0 || item.valorRequisitado > 0,
        )
        this.itens = itens
      })
  }

  prepare(data: any[]) {
    const dto = {
      itens: data,
      acrescimo: true,
    }
    return dto
  }

  confirm() {
    const url = `licitacao/contrato_aditivo/UUID/item_aditivo/lote`
    this.loading = true
    const dto = this.prepare(this.itens)
    this.service
      .updateItems(this.parentUuid, this.uuid, dto)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Acréscimo realizado com sucesso!`,
          success: true,
        })
        this.dialogRef.close(res)
      })
  }

  addPercentage(porcentagem) {
    this.loading = true
    const dto = {
      itens: this.itens,
      porcentagem: porcentagem,
    }
    this.service
      .addPercentage(this.parentUuid, this.uuid, dto)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.itens = res.dados
        this.invalidUpdate = false
      })
  }

  dispose() {
    this.dialogRef.close()
  }

  onRowUpdated() {
    this.invalidUpdate = false
  }

  validateNumber(e) {
    if (
      e.data.quantidadeRequisitadaAto &&
      e.value < e.data.quantidadeRequisitadaAto
    )
      return false
    else return true
  }

  // calculations

  calculateTotalPrice(rowData) {
    return (rowData.valorTotal || 0) + (rowData.valorAdicionar || 0)
  }

  calculateTotalAmount(rowData) {
    return rowData.quantidadeRequisitada + (rowData.quantidadeAdicionar || 0)
  }

  calculateTotal(rowData) {
    return rowData.precoMaximo * rowData.quantidade
  }

  calculateAddition(rowData) {
    return (rowData.valorAdicionar =
      rowData.precoMaximo * (rowData.quantidadeAdicionar || 0))
  }
}
