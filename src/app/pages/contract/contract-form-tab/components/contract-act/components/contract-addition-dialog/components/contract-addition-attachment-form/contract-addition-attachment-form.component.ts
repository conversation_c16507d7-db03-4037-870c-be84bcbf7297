import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { SimpleRequestService } from '@common/services/simple-request.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { ContractAttachmentService } from '@pages/contract/services/contract-attachment.service'
import { Observable, ReplaySubject } from 'rxjs'
import { finalize, take } from 'rxjs/operators'

@Component({
  selector: 'eqp-contract-addition-attachment-form',
  templateUrl: './contract-addition-attachment-form.component.html',
  styleUrls: ['./contract-addition-attachment-form.component.scss'],
})
export class ContractAdditionAttachmentFormComponent implements OnInit {
  constructor(
    private toastr: ToastrService,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ContractAdditionAttachmentFormComponent>,
    private service: ContractAttachmentService,
    private simpleRequestService: SimpleRequestService,
  ) {}

  @Input() contractingUuid: string
  @Input() uuid: string
  @Input() attachmentData: any

  public model: FormGroup
  files64Upload: any[] = []
  loading = false
  fileToUpload: File = null
  arquivosSelecionados: FileList | null = null

  get rightFirstButtonDisabled() {
    if (!this.uuid) {
      return (
        !(this.files64Upload.length > 0 && this.model.pristine) &&
        !(this.files64Upload.length > 0 && this.model.valid)
      )
    } else {
      return this.model.invalid || this.model.pristine
    }
  }

  get hideFileField() {
    if (this.uuid) {
      if (!!this.model.get('nomeArquivo')?.value) {
        return false
      } else {
        return true
      }
    }
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    if (this.attachmentData) this.loadForm()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      nomeArquivo: [''],
      nome: [''],
      descricao: [''],
      conteudo: [],
      publicaInternet: ['N'],
    })
  }

  loadForm() {
    const {
      nomeArquivoOriginal,
      nomeInformado,
      descricao,
      publicaInternet,
      uuid,
    } = this.attachmentData
    this.model.patchValue({
      nomeArquivo: nomeArquivoOriginal,
      descricao,
      nome: nomeInformado,
      publicaInternet: publicaInternet == 'YES' ? 'S' : 'N',
      uuid,
    })
    console.log(this.attachmentData)
  }

  private prepare(formData: any) {
    const dto = {
      ...formData,
      ...this.files64Upload[0],
      nomeInformado: formData.nome,
      nome: formData.nomeInformado,
      publicaInternet: formData.publicaInternet == 'S' ? 'YES' : 'NO',
    }
    return dto
  }

  handleFileInput(files: FileList) {
    this.fileToUpload = files.item(0)
  }

  selecionarArquivo() {
    const inputElement = document.createElement('input')
    inputElement.type = 'file'

    inputElement.addEventListener('change', event => {
      this.arquivosSelecionados = (event.target as HTMLInputElement).files
      this.convertFile(this.arquivosSelecionados[0]).subscribe(res =>
        this.setNewAttachment(res),
      )
    })

    inputElement.click()
  }

  setNewAttachment(data: any) {
    if (this.files64Upload.length > 0) this.files64Upload.shift()
    this.files64Upload.push({
      conteudo: data,
    })
    this.model.get('nomeArquivo').patchValue(this.arquivosSelecionados[0].name)
    this.model.markAsDirty()
  }

  convertFile(file: File): Observable<string> {
    const result = new ReplaySubject<string>(1)
    const reader = new FileReader()
    reader.readAsBinaryString(file)
    reader.onload = event => {
      const binaryString = btoa(event.target.result.toString())
      const data = `data:${file.type};base64,${binaryString}`
      result.next(data)
    }
    return result
  }

  dispose() {
    this.dialogRef.close(null)
  }

  confirm() {
    const uuid = this.model.get('uuid')?.value
    if (this.model.valid) {
      const url = `licitacao/contrato_aditivo/${this.contractingUuid}/anexo`
      const dto = this.prepare(this.model.getRawValue())
      let req: Observable<any>
      if (!uuid) {
        req = this.simpleRequestService.post(url, dto)
      } else {
        req = this.simpleRequestService.put(url + '/' + uuid, dto)
      }
      req
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(res => this.confirmRegister(res))
    }
  }

  confirmRegister(response: any) {
    const uuid = this.model.get('uuid')?.value

    this.toastr.send({
      success: true,
      title: 'Sucesso',
      message: `Anexo ${!uuid ? 'cadastrado' : 'atualizado'} com sucesso!`,
    })
    this.dialogRef.close(true)
  }
}
