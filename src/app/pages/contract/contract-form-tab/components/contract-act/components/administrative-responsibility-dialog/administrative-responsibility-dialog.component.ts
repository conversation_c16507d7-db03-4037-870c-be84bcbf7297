import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { SimpleRequestService } from '@common/services/simple-request.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { Observable } from 'rxjs'
import { finalize, take } from 'rxjs/operators'

@Component({
  selector: 'eqp-administrative-responsibility-dialog',
  templateUrl: './administrative-responsibility-dialog.component.html',
  styleUrls: ['./administrative-responsibility-dialog.component.scss'],
})
export class AdministrativeResponsibilityDialogComponent implements OnInit {
  @Input() uuid: string
  @Input() parentUuid: string

  dialogTitle: string = 'Responsabilidade Administrativa'
  loading: boolean = false
  model: FormGroup

  types: DataSource

  constructor(
    private builder: FormBuilder,
    private toastr: ToastrService,
    private dialogRef: NbDialogRef<AdministrativeResponsibilityDialogComponent>,
    private simpleRequestService: SimpleRequestService,
  ) {}

  ngOnInit(): void {
    this.loadTypes()
    this.model = this.getNewModel()
    if (this.uuid) {
      this.loadResponsible()
    }
  }

  getNewModel() {
    return this.builder.group({
      codigo: [],
      atoDesignacao: ['', Validators.required],
      data: [''],
      dataFinal: ['', Validators.required],
      dataInicial: ['', Validators.required],
      pessoaResponsavel: ['', Validators.required],
      tipoResponsabilidadeAdministrativa: ['', Validators.required],
    })
  }

  loadTypes() {
    const url = 'licitacao/tipo_responsabilidade_administrativa'
    this.simpleRequestService.get(url).subscribe(
      res =>
        (this.types = new DataSource({
          store: res.data,
        })),
    )
  }

  loadResponsible() {
    const url = `licitacao/contrato/${this.parentUuid}/responsabilidade_administrativa/${this.uuid}`
    this.simpleRequestService.get(url).subscribe((res: any) => {
      const {
        pessoaResponsavel: { codigo },
        tipoResponsabilidadeAdministrativa: { uuid },
        dataInicial,
        dataFinal,
        atoDesignacao,
        pessoaResponsavel,
      } = res.dados
      const dto = {
        codigo,
        dataInicial,
        dataFinal,
        atoDesignacao,
        pessoaResponsavel,
        tipoResponsabilidadeAdministrativa: uuid,
      }
      this.model.patchValue(dto)
    })
  }

  confirm() {
    if (this.model.valid) {
      this.loading = true

      let req: Observable<any>

      if (this.uuid) {
        const url = `licitacao/contrato/${this.parentUuid}/responsabilidade_administrativa/${this.uuid}`

        req = this.simpleRequestService.put(url, this.dto)
      } else {
        const url = `licitacao/contrato/${this.parentUuid}/responsabilidade_administrativa`

        req = this.simpleRequestService.post(url, this.dto)
      }
      req
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(() => {
          this.toastr.send({
            title: 'Sucesso',
            message: `Responsável ${
              this.uuid ? 'atualizado' : 'cadastrado'
            }(a) com sucesso`,
            success: true,
          })
          this.dialogRef.close(true)
        })
    }
  }

  dispose() {
    this.dialogRef.close()
  }

  get dto() {
    const {
      atoDesignacao,
      dataFinal,
      dataInicial,
      pessoaResponsavel: { uuid: pessoaResponsavelUuid },
      tipoResponsabilidadeAdministrativa,
    } = this.model.getRawValue()

    const dto = {
      atoDesignacao,
      dataFinal,
      dataInicial,
      pessoaResponsavel: { uuid: pessoaResponsavelUuid },
      tipoResponsabilidadeAdministrativa: {
        uuid: tipoResponsabilidadeAdministrativa,
      },
    }
    return dto
  }
}
