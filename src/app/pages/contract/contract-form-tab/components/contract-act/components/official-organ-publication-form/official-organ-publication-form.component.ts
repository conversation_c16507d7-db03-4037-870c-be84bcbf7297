import { DatePipe } from '@angular/common'
import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { SimpleRequestService } from '@common/services/simple-request.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { Observable } from 'rxjs'
import { finalize, take } from 'rxjs/operators'
@Component({
  selector: 'eqp-official-organ-publication-form',
  templateUrl: './official-organ-publication-form.component.html',
  styleUrls: ['./official-organ-publication-form.component.scss'],
  providers: [DatePipe],
})
export class OfficialOrganPublicationFormComponent implements OnInit {
  @Input() uuid: string
  @Input() parentUuid: string
  @Input() inclusaoTce: string

  dialogTitle: string = 'Publicação órgão oficial'
  loading: boolean = false
  model: FormGroup

  officialOrgans: DataSource

  constructor(
    private builder: FormBuilder,
    private toastr: ToastrService,
    private dialogRef: NbDialogRef<OfficialOrganPublicationFormComponent>,
    private simpleRequestService: SimpleRequestService,
    private datePipe: DatePipe,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadOfficialOrgans()
    if (this.uuid) this.loadForm()
    this.checkError()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      dataInclusaoTce: ['', Validators.required],
      dataPublicacao: ['', Validators.required],
      orgaoOficial: this.builder.group({
        uuid: ['', Validators.required],
      }),
    })
  }

  loadOfficialOrgans() {
    const url = 'licitacao/contrato/orgao_oficial'
    this.simpleRequestService.get(url).subscribe(
      res =>
        (this.officialOrgans = new DataSource({
          store: res.data,
        })),
    )
  }

  public loadForm() {
    this.loading = true;
    const url = `licitacao/contrato_aditivo/${this.parentUuid}/orgao_oficial/${this.uuid}`

    this.simpleRequestService.get<any>(url)
    .pipe(finalize(() => this.loading = false))
    .subscribe(({ dados }) => {
      const {
        dataInclusaoTce,
        dataPublicacao,
        orgaoOficial: { uuid },
      } = dados

      const dto = {
        dataInclusaoTce,
        dataPublicacao,
        orgaoOficial: { uuid },
      }

      this.model.patchValue(dto)
    })
  }

  confirm() {
    if (this.model.valid) {
      this.loading = true
      let req: Observable<any>
      if (this.uuid) {
        const url = `licitacao/contrato_aditivo/${this.parentUuid}/orgao_oficial/${this.uuid}`
        req = this.simpleRequestService.put(url, this.model.getRawValue())
      } else {
        const url = `licitacao/contrato_aditivo/${this.parentUuid}/orgao_oficial`
        req = this.simpleRequestService.post(url, this.model.getRawValue())
      }
      req
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe({next: () => {
          this.toastr.send({
            title: 'Sucesso',
            message: `Publicação ${
              this.uuid ? 'atualizada' : 'cadastrada'
            } com sucesso`,
            success: true,
          })
          this.dialogRef.close(true)
        }})
    }
  }

  dispose() {
    this.dialogRef.close()
  }

  //selects display
  responsibleTypeDisplay(item) {
    return item && `${item.codigo} - ${item.pessoaJuridica.nome}`
  }

  checkError(): 'CONTRACT_DATE' | 'ACTUAL_DATE' | 'NO_ERROR' {
    const today = this.getAbsoluteDateValue()
    const publicationDate = this.getAbsoluteDateValue(
      this.model.get('dataPublicacao').value,
    )
    const inclusaoTce = this.getAbsoluteDateValue(this.inclusaoTce)

    if (publicationDate > today) {
      return 'ACTUAL_DATE'
    } else if (publicationDate < inclusaoTce) {
      return 'CONTRACT_DATE'
    } else {
      return 'NO_ERROR'
    }
  }

  getAbsoluteDateValue(date?: string) {
    if (date) {
      const [year, month, day] = date.split('-')
      return Number(year + month + day)
    }
    const year = new Date().getFullYear()
    const month = new Date().getMonth()
    const day = new Date().getDate()
    return Number(`${year}${month + 1}${day}`)
  }
}
