import { Component, OnInit } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { SimpleRequestService } from '@common/services/simple-request.service'
import DataSource from 'devextreme/data/data_source'

@Component({
  selector: 'eqp-regularity-certificates',
  templateUrl: './regularity-certificates.component.html',
  styleUrls: ['./regularity-certificates.component.scss'],
})
export class RegularityCertificatesComponent implements OnInit {
  pageTitle: 'Certificados de regularidade'
  parentUuid: string
  dataSource: DataSource
  readonly loading$ = this.simpleRequestService.loading$

  constructor(
    private route: ActivatedRoute,
    private simpleRequestService: SimpleRequestService,
  ) {}

  ngOnInit(): void {
    const { uuid } = this.route.parent.snapshot.params
    this.parentUuid = uuid
    this.fetchData()
  }

  fetchData() {
    const url = `licitacao/contrato/${this.parentUuid}/certidoes`
    this.simpleRequestService.get<any>(url).subscribe(({ dados }) => {
      this.dataSource = new DataSource([...dados.certidoes])
    })
  }
}
