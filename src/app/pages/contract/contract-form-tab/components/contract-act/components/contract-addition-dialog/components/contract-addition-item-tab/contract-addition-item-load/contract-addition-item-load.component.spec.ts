import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ContractAdditionItemLoadComponent } from './contract-addition-item-load.component';

describe('ContractAdditionItemLoadComponent', () => {
  let component: ContractAdditionItemLoadComponent;
  let fixture: ComponentFixture<ContractAdditionItemLoadComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ContractAdditionItemLoadComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractAdditionItemLoadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
