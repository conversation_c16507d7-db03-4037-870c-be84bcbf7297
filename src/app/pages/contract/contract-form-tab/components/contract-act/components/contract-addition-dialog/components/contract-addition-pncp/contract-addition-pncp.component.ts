import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnInit } from '@angular/core';
import { ContractAdditionPncpService } from './services/contract-addition-pncp.service';
import { finalize, take } from 'rxjs/operators';
import { ToastrService } from '@common/services/toastr/toastr.service';

@Component({
  selector: 'app-contract-addition-pncp',
  templateUrl: './contract-addition-pncp.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractAdditionPncpComponent implements OnChanges {

  @Input() additiveUuid: string
  
  pncpSeq?: number
  loading = false

  constructor(
    private _service: ContractAdditionPncpService,
    private _cd: ChangeDetectorRef,
    private _toastr: ToastrService
  ) {}

  ngOnChanges(): void {
    if (this.additiveUuid) {
      this._loadPncp()
    }
  }

  private _loadPncp() {
    this.loadChange(true)
    this._service.get(this.additiveUuid)
      .pipe(take(1), finalize(() => this.loadChange(false)))
      .subscribe({next: res => {
        this.pncpSeq = res.dados.sequencialPncp
      }})
  }

  send() {
    this.loadChange(true)
    this._service.sendPncp(this.additiveUuid)
      .pipe(take(1), finalize(() => this.loadChange(false)))
      .subscribe({next: res => {
        this.pncpSeq = res.dados.sequencialPncp
        this._toastr.send({
          success: true,
          message: 'Dados enviados com sucesso.'
        })
      }})
  }

  remove() {
    this.loadChange(true)
    this._service.removePncp(this.additiveUuid)
      .pipe(take(1), finalize(() => this.loadChange(false)))
      .subscribe({next: () => {
        this._loadPncp()
        this._toastr.send({
          success: true,
          message: 'Dados removidos com sucesso.'
        })
      }})
  }

  loadChange(loading: boolean) {
    this.loading = loading
    this._cd.markForCheck()
  }

}
