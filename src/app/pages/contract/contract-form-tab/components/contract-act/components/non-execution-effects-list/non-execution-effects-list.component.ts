import { Component, OnInit } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { CrudService } from '@common/services/crud.service'
import { SimpleRequestService } from '@common/services/simple-request.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { ResponsibleService } from '@pages/contract/services/responsible.service'
import { flagParser } from '@pages/shared/helpers/parsers.helper'
import DataSource from 'devextreme/data/data_source'
import { Subject } from 'rxjs'
import { filter } from 'rxjs/operators'
import { OfficialOrganPublicationFormComponent } from '../official-organ-publication-form/official-organ-publication-form.component'
import { NonExecutionEffectsDialogComponent } from '../non-execution-effects-dialog/non-execution-effects-dialog.component'
import { currencyFormat } from '@pages/shared/helpers/format.helper'

@Component({
  selector: 'eqp-non-execution-effects-list',
  templateUrl: './non-execution-effects-list.component.html',
  styleUrls: ['./non-execution-effects-list.component.scss'],
})
export class NonExecutionEffectsListComponent implements OnInit {
  dataSource: DataSource

  pageTitle: 'Efeito da Inexecução'
  parentUuid: string

  loading: boolean = false

  currencyFormat = currencyFormat

  private readonly destroy$ = new Subject<void>()
  readonly loading$ = this.simpleRequestService.loading$

  constructor(
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private crudService: CrudService,
    private service: ResponsibleService,
    private simpleRequestService: SimpleRequestService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    const { uuid } = this.route.parent.snapshot.params
    this.parentUuid = uuid
    this.fetchData()
  }

  ngOnDestroy() {
    this.destroy$.next()
    this.destroy$.complete()
  }

  public fetchData() {
    const url = `licitacao/contrato/${this.parentUuid}/efeito_inexecucao`
    const dataSourceConfig = {
      store: this.crudService.getDataSourceFiltro('uuid', url, 10),
      paginate: true,
      pageSize: 10,
    }
    this.dataSource = new DataSource(dataSourceConfig)
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Efeitos da Inexecução'
      event.toolbarOptions.items[0].options.hint = 'Novo efeito da inexecução'
      event.toolbarOptions.items[0].options.onClick = () => this.edit()

      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  edit(uuid?: string): void {
    const dialogRef = this.dialogService.open(
      NonExecutionEffectsDialogComponent,
      {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.componentRef.instance.parentUuid = this.parentUuid
    dialogRef.componentRef.instance.uuid = uuid
    dialogRef.onClose.pipe(filter(res => res)).subscribe(res => {
      this.dataSource.reload()
    })
  }

  remove(uuid: any) {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.subscribe(res => {
      if (flagParser(res)) {
        const url = `licitacao/contrato/${this.parentUuid}/efeito_inexecucao/${uuid}`

        this.simpleRequestService.delete(url).subscribe(
          () => {
            this.toastr.send({
              success: true,
              message: `Efeito removido com sucesso.`,
            })
            this.dataSource.reload()
          },
          (resp: any) => this.toastr.bulkSend(resp.mensagens),
        )
      }
    })
  }
}
