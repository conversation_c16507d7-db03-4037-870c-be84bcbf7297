import { NbDialogRef} from '@nebular/theme';
import { finalize, take } from 'rxjs/operators';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Component, Input, OnInit } from '@angular/core';
import { CrudService } from '@common/services/crud.service';
import { currencyFormat } from '@pages/shared/helpers/format.helper';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { ContractAdditionService } from '@pages/contract/services/contract-addition.service';

@Component({
  selector: 'eqp-contract-addition-item-dialog',
  templateUrl: './contract-addition-item-dialog.component.html',
  styleUrls: ['./contract-addition-item-dialog.component.scss']
})
export class ContractAdditionItemDialogComponent implements OnInit {
  @Input() parentUuid: string
  @Input() uuid: string

  dialogTitle: string = "Carregar itens no ato contratual"

  loading: boolean = false
  invalidUpdate: boolean = true

  model: FormGroup
  dataSource: any[] = []

  selectedOption: number
  currencyFormat = currencyFormat

  decimalFormat = {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  constructor(
    private builder: FormBuilder,
    private toastr: ToastrService,
    private crudService: CrudService,
    private service: ContractAdditionService,
    private dialogRef: NbDialogRef<ContractAdditionItemDialogComponent>
  ) { }

  ngOnInit(): void {
    if(this.uuid) this.fetchGrid()
    this.model = this.getNewModel()
  }

  getNewModel() {
    return this.builder.group({
    option: [],
    solicitacao: [],
    solicitante: [],
    local: [],
    requisicaoCompra: []
    })
  }

  fetchGrid() {
    this.loading = true
    this.crudService.getSingleData<any>(
      `contrato/contrato/${this.parentUuid}/ato/${this.uuid}/item`,
        { take: 0 },
    ).pipe(
      take(1),
      finalize(() => (this.loading = false)),
    ).subscribe(
      res => {
        const itens = res.data.filter(item => item.quantidadeAdicionar > 0)
        this.dataSource = itens
        if(this.dataSource) this.invalidUpdate = false
        })
  }

  prepare(data){
    const dto = {
      itens: this.dataSource,
      acrescimo: true
    }
    return dto
  }

  confirm() {
    this.loading = true
    const dto = this.prepare(this.dataSource)
    this.service.updateItems(this.parentUuid, this.uuid, dto)
    .pipe(
      take(1),
      finalize(() => (this.loading = false)),
    )
    .subscribe(res => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Itens atualizados com sucesso!`,
          success: true,
        })
        this.dialogRef.close(res.dados)
    })
  }

  dispose() {
    this.dialogRef.close()
  }

  validateNumber(e) {
    if(e.data.quantidadeRequisitadaAto > e.data.quantidadeAdicionar || e.data.quantidadeAdicionar == null){
      e.data.quantidadeRequisitadaAto == null
     return false
    } else if(e.data.quantidadeRequisitadaAto === e.data.quantidadeAdicionar){
     return true
    } else {
     return true
    }
  }

  calculateTotalValue(rowData) {
    return (rowData.quantidadeRequisitadaAto || 0) * rowData.precoMaximo
  }

  calculateAmount(rowData) {
    if(rowData.quantidadeRequisitadaAto) {
      return rowData.quantidadeRequisitadaAto
    } else {
      rowData.quantidadeRequisitadaAto = rowData.quantidadeAdicionar
    }
  }

  onRowUpdated(){
    this.invalidUpdate = false
  }

}
