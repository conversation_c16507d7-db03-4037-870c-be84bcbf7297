import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ContractAdditionItemIncreaseComponent } from './contract-addition-item-increase.component';

describe('ContractAdditionItemIncreaseComponent', () => {
  let component: ContractAdditionItemIncreaseComponent;
  let fixture: ComponentFixture<ContractAdditionItemIncreaseComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ContractAdditionItemIncreaseComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractAdditionItemIncreaseComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
