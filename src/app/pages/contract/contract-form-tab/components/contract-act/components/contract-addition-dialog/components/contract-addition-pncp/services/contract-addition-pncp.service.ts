import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseDto } from '@common/interfaces/dtos/response-dto';

type T = ResponseDto<{sequencialPncp: number}>

@Injectable({
  providedIn: 'root'
})
export class ContractAdditionPncpService {

  constructor(private _http: HttpClient) { }

  private readonly _baseUrl = 'licitacao/contrato_aditivo/pncp'

  get(additiveUuid: string) {
    return this._http.get<T>(
      `${this._baseUrl}/${additiveUuid}`,
      { headers: new HttpHeaders() }
    )
  }

  sendPncp(additiveUuid: string) {
    return this._http.get<T>(
      `${this._baseUrl}/${additiveUuid}/enviar`,
      { headers: new HttpHeaders() }
    )
  }

  removePncp(additiveUuid: string) {
    return this._http.delete<void>(
      `${this._baseUrl}/${additiveUuid}/remover`,
      { headers: new HttpHeaders() }
    )
  }

}
