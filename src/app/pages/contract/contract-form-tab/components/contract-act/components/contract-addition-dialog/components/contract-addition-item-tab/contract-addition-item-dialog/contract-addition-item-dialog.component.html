<eqp-nebular-dialog
  id="product-form-dialog"
  [dialogTitle]="dialogTitle"
  [dialogSize]="'extra-large'"
  [rightFirstButtonText]="'Executar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-check'"
  [rightFirstButtonId]="'confirm-contract-addition-item-form-dialog-button'"
  [rightFirstButtonTitle]="'Executar'"
  [rightFirstButtonDisabled]="invalidUpdate"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-contract-addition-item-form-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>
  <ng-container [formGroup]="model">
    <div class="p-3">
      <eqp-loading *ngIf="loading"></eqp-loading>
      <dx-data-grid
        id="contract-addition-item-grid"
        [dataSource]="dataSource"
        [allowColumnResizing]="true"
        [columnAutoWidth]="true"
        [showColumnLines]="false"
        [showRowLines]="false"
        [showBorders]="false"
        [rowAlternationEnabled]="true"
        [wordWrapEnabled]="true"
        [loadPanel]="false"
        [columnHidingEnabled]="false"
        [remoteOperations]="true"
        (onRowUpdated)="onRowUpdated()"
        keyExpr="uuid"
        class="p-0"
      >
        <dxo-paging [pageSize]="10"></dxo-paging>
        <dxo-pager
          [showInfo]="true"
          [showNavigationButtons]="true"
          [showPageSizeSelector]="false"
        >
        </dxo-pager>

        <dxo-header-filter [visible]="false"></dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>

        <dxo-sorting mode="multiple"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

        <dxo-group-panel
          [visible]="false"
          [emptyPanelText]="''"
        ></dxo-group-panel>

        <dxo-editing
          mode="cell"
          [allowUpdating]="true"
          [allowDeleting]="false"
          [allowAdding]="false"
          [useIcons]=""
        >
        </dxo-editing>

        <dxi-column
          dataField="solicitacao.codigo"
          dataType="number"
          [allowEditing]="false"
          caption="Solicitação"
        ></dxi-column>

        <dxi-column
          dataField="solicitante.nome"
          [allowEditing]="false"
          caption="Solicitante"
        ></dxi-column>

        <dxi-column
          dataField="local.nome"
          [allowEditing]="false"
          caption="Local"
        ></dxi-column>

        <!-- <dxi-column
        dataField="requisicaoCompra"
        [allowEditing]="false"
        caption="Req compra"
      ></dxi-column> -->

        <dxi-column
          dataField="codigo"
          dataType="number"
          [allowEditing]="false"
          caption="Código"
        ></dxi-column>

        <dxi-column
          dataField="produto.nome"
          [allowEditing]="false"
          caption="Produto"
        ></dxi-column>

        <dxi-column
          dataField="marca"
          [allowEditing]="false"
          caption="Marca"
        ></dxi-column>

        <dxi-column
          dataField="unidadeMedida.sigla"
          [allowEditing]="false"
          caption="Unid."
        ></dxi-column>

        <dxi-column
          dataField="quantidade"
          [dataType]="'number'"
          [allowEditing]="false"
          caption="Qtde. original"
        ></dxi-column>

        <dxi-column
          dataField="quantidadeAdicionar"
          [dataType]="'number'"
          [allowEditing]="false"
          caption="Qtde. disponível"
        ></dxi-column>

        <dxi-column
          cssClass="cell-highlighted"
          dataField="quantidadeRequisitadaAto"
          [dataType]="'number'"
          caption="Qtde. requisitada"
          [format]="decimalFormat"
          [allowEditing]="true"
          [calculateCellValue]="calculateAmount"
          [width]="150"
        >
          <dxi-validation-rule
            [ignoreEmptyValue]="true"
            min="0"
            type="custom"
            [validationCallback]="validateNumber"
            [message]="'Quantidade requisitada excede a quantidade disponível'"
          ></dxi-validation-rule>
        </dxi-column>

        <dxi-column
          dataField="valorUnitário"
          [format]="currencyFormat"
          [allowEditing]="false"
          caption="Preço unitário"
        ></dxi-column>

        <dxi-column
          dataField="valorTotal"
          [format]="currencyFormat"
          [allowEditing]="false"
          caption="Valor original"
        ></dxi-column>

        <dxi-column
          dataField="valorAdicionar"
          [format]="currencyFormat"
          [allowEditing]="false"
          caption="Valor disponível"
        ></dxi-column>

        <dxi-column
          dataField="valorAditivar"
          [format]="currencyFormat"
          [allowEditing]="false"
          caption="Valor a aditivar"
          [calculateDisplayValue]="calculateTotalValue"
        ></dxi-column>

        <dxi-column
          caption="Tipo controle"
          dataField="tipoControle.nome"
          [format]="currencyFormat"
          [allowEditing]="false"
        >
        </dxi-column>
      </dx-data-grid>
    </div>
  </ng-container>
</eqp-nebular-dialog>
