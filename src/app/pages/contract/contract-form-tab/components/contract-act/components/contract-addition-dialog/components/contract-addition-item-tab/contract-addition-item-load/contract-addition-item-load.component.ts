import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { ContractAdditionService } from '@pages/contract/services/contract-addition.service'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import { Subject } from 'rxjs'
import { finalize, take } from 'rxjs/operators'
import { ContractAdditionItemLoadService } from './contract-addition-item-load.service'
import { CellPreparedEvent, EditorPreparingEvent } from 'devextreme/ui/data_grid'

@Component({
  selector: 'eqp-contract-addition-item-load',
  templateUrl: './contract-addition-item-load.component.html',
  styleUrls: ['./contract-addition-item-load.component.scss'],
})
export class ContractAdditionItemLoadComponent implements OnInit {
  @Input() parentUuid: string
  @Input() uuid: string

  unsubs$ = new Subject<void>()
  model: FormGroup
  validateHasItem: boolean = false
  isEditingCell = false

  uri: string
  title: string
  nameKey: string
  codeKey: string
  columnsSearchName: string = ''
  gridByType: string = 'SOLICITACAO'
  data: any
  changedItems = []
  loading: boolean = false
  itens: any[] = []
  originalData: any
  controlTypeIsValue: boolean
  itemsUpdated = false

  invalidUpdate: boolean = true

  //formats
  decimalFormat = {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }
  currencyFormat = currencyFormat

  constructor(
    private toastr: ToastrService,
    private builder: FormBuilder,
    private dialogService: NbDialogService,
    private dialogRef: NbDialogRef<ContractAdditionItemLoadComponent>,
    private service: CrudService,
    private contractAdditionService: ContractAdditionService,
    private contractAdditionItemLoadService: ContractAdditionItemLoadService,
  ) { }

  ngOnInit(): void {
    this.fetchGrid()
    this.model = this.newModel()
    this.changesValuesObservable()
    this.setValuesBySearchData('SOLICITACAO')
  }

  private newModel(): FormGroup {
    return this.builder.group({
      entidade: [''],
      fornecedor: [''],
      tipoAto: [''],
      local: [''],
      tipoFiltro: ['SOLICITACAO'],
      uuidFiltro: [''],
      solicitacao: [''],
      requisicaoCompra: [''],
      solicitante: [''],
      numeroContrato: [''],
      exercicioContrato: [''],
      exercicioModalidade: [''],
      numeroModalidade: [''],
      modalidade: [''],
    })
  }

  fetchGrid() {
    this.service
      .getSingleData<any>(`licitacao/contrato/${this.parentUuid}`)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.itens = res.dados
        this.patchModel(this.itens)
      })
  }

  onRowUpdated(event) {
    this.itemsUpdated = true
  }

  private validateBalanceControlTypeEdit(typeCode: number, newData: Object) {
    if (typeCode == 1 && 'quantidadeDesejada' in newData) {
      this.toastr.send({
        info: true,
        message: 'Quantidade desejada não é editável quando o controle de saldo for por valor'
      })
      return false
    }
    if (typeCode == 0 && 'valorDesejado' in newData) {
      this.toastr.send({
        info: true,
        message: 'Valor desejado não é editável quando o controle de saldo for por quantidade'
      })
      return false
    }
    return true
  }

  onRowValidating(event) {
    this.isEditingCell = false
    const quantidadeDisponivel = event.oldData?.quantidadeDisponivel
    const valorDisponivel = event.oldData?.valorDisponivel
    const balanceControlType = event.oldData?.tipoControleSaldo?.codigo

    if (!this.validateBalanceControlTypeEdit(balanceControlType, event.newData)) {
      event.component.cancelEditData()
    }

    if (balanceControlType == 0) {
      const newRequest = event?.newData?.quantidadeDesejada

      if (newRequest == 0 || newRequest == null) {
        this.itemsUpdated = false
      }

      if (newRequest > quantidadeDisponivel) {
        event.component.cancelEditData()
        this.toastr.send({
          success: false,
          info: true,
          message:
            'A quantidade requisitada não deve exceder a quantidade disponível',
        })
      }
    } else {
      const newRequest = event?.newData?.valorDesejado

      if (newRequest == 0 || newRequest == null) {
        this.itemsUpdated = false
      }

      if (newRequest > valorDisponivel) {
        event.component.cancelEditData()
        this.toastr.send({
          success: false,
          info: true,
          message: 'O valor requisitado não deve exceder o valor disponível',
        })
      }
    }
  }

  private changesValuesObservable() {
    this.model
      .get('tipoFiltro')
      .valueChanges.pipe()
      .subscribe(res => {
        this.gridByType = res
        this.setValuesBySearchData(res)
        this.model.get('uuidFiltro').reset()
      })
  }

  setValuesBySearchData(type) {
    switch (type) {
      case 'SOLICITACAO':
        this.uri = 'solicitacao'
        this.title = 'Solicição'
        this.columnsSearchName = 'solicitation'
        this.nameKey = 'exercicio.exercicio'
        this.codeKey = 'numero'
        break
      case 'SOLICITANTE':
        this.uri = 'solicitante'
        this.title = 'Solicitante'
        this.columnsSearchName = 'requester'
        this.nameKey = 'nome'
        this.codeKey = 'codigo'
        break
      case 'LOCAL':
        this.uri = 'local'
        this.title = 'Local'
        this.columnsSearchName = 'local'
        this.nameKey = 'nome'
        this.codeKey = 'codigo'
        break
      case 'REQUISICAO_COMPRA':
        this.uri = 'requisicao_compra'
        this.title = 'Requisição de compra'
        this.columnsSearchName = 'Nomes das colunas para REQUISICAO_COMPRA'
        this.nameKey = 'name'
        this.codeKey = 'code'
        break
    }
  }

  private patchModel(data) {
    this.model.patchValue({
      entidade: data.entidadeOrigem.codigo,
      tipoAto: data.tipoAtoContratual.nome,
      fornecedor: data.fornecedor,
      local: data.local,
      numeroContrato: data.numero,
      exercicioContrato: data.exercicio,
      exercicioModalidade: data.licitacao?.exercicio.exercicio,
      modalidade: data.licitacao?.tipoModalidadeLicitacaoTce.nome,
      numeroModalidade: data.licitacao?.numero,
    })
  }

  // isEditingAllowed(data) {
  //   const rowData = data[0].items
  //   console.log(rowData)
  //   return rowData.tipoControleSaldo?.codigo === 0
  // }

  combineSolicitacao(data: any) {
    return `${data.solicitacaoNumero} / ${data.solicitacaoExercicio} - ${data.solicitacaoCodigoEntidade}`
  }

  validateDesiredQuantity(data: any): any {
    if (
      data.quantidadeDesejada <= 0 ||
      data.quantidadeDesejada > data.quantidadeDisponivel
    ) {
      return data.quantidadeDisponivel
    }
    return data.quantidadeDesejada
  }

  validateAllowedValue(data: any): any {
    if (
      data.valorDisponivel <= 0 ||
      data.valorDesejado > data.valorDisponivel
    ) {
      return data.valorDisponivel
    }
    return data.valorDesejado
  }

  onEditingStart(e) {
    this.isEditingCell = true
  }

  resetValues() {
    this.setGridValues(0)
  }

  setGridValues(value: number) {
    this.data = this.data.map(batch => ({
      ...batch,
      items: batch.items.map(item => {
        if (item.tipoControleSaldo.codigo == 0) {
          item.quantidadeDesejada = value
        } else {
          item.valorDesejado = value
        }
        return item
      })
    }))
  }

  prepare() {
    return this.data.flatMap(lote => lote.items
      .filter(item => {
        if (item.tipoControleSaldo.codigo == 0) {
          return item.quantidadeDesejada > 0
        }
        return item.valorDesejado > 0
      })
      .map(item => ({
        uuid: item.uuid,
        precoUnitario: item.precoUnitario,
        valor: item.valorDesejado || 0,
        quantidade: item.quantidadeDesejada || 1,
        solicitacaoItem: item?.processoLoteItem
      })),
    )
  }

  confirm() {
    this.loading = true
    const dto = this.prepare()
    if (dto.length == 0) {
      return
    }
    this.contractAdditionService
      .postItem(this.uuid, dto)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Itens carregados com sucesso!`,
          success: true,
        })
        this.contractAdditionItemLoadService.saveItems(res.body.dados)
        this.dialogRef.close(res)
      })
  }

  cancel() {
    this.dialogRef.close()
  }

  loadItems() {
    this.loading = true
    const dto = this.model.getRawValue()
    this.service
      .getSingleData<any>(
        `licitacao/contrato_aditivo/${this.uuid}/item_carregar`,
        {
          uuidFiltro: dto['uuidFiltro'],
          tipoFiltro: dto['tipoFiltro'],
        },
      )
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {

        res.dados.sort((a, b) => a.lote - b.lote)
        res.dados.forEach((batch: any) => {
          batch.items.sort((a, b) => a.item - b.item)
          batch.items.forEach((item: any) => {
            item.idSolicitacaoItem = `${item.item}-${item.solicitacaoNumero}`;
            if (item.tipoControleSaldo.codigo == 1) {
              item.quantidadeDesejada = undefined
              item.valorDesejado = item.valorDisponivel;
            } else {
              item.valorDesejado = undefined
            }
          })
        });

        this.data = res.dados
        this.controlTypeIsValue =
          res.dados[0].items[0].tipoControleSaldo.codigo === 1

        this.validateHasItem = true

      })
  }

  onCellPrepared(e: CellPreparedEvent) {
      if (e.rowType != 'data') {
        return
      }
      if (
        e.column.dataField === 'quantidadeDesejada' &&
        e?.data?.tipoControleSaldo.codigo === 0
      ) {
        e.cellElement.classList.add('cell-highlighted')
      }
      else if (
        e.column.dataField === 'valorDesejado' &&
        e?.data?.tipoControleSaldo.codigo === 1
      ) {
        e.cellElement.classList.add('cell-highlighted')
      }
    }
  
    onEditorPreparing(e: EditorPreparingEvent) {
      if (e.parentType == 'dataRow' && (e.dataField == 'valorDesejado' || e.dataField == 'quantidadeDesejada' )) {  
        e.editorOptions.onFocusOut = () => {
          this.isEditingCell = false
        }
      }  
    }

  clear() {
    this.gridByType === ''
    this.validateHasItem = false
  }

  ngOnDestroy(): void {
    this.unsubs$.next()
    this.unsubs$.complete()
  }
}
