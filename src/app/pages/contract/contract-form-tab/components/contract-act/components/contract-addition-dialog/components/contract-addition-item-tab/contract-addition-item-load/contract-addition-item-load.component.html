<eqp-nebular-dialog
  dialogTitle="Carregar itens"
  spinnerSize="large"
  [spinnerActive]="loading"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="false"
  [rightFirstButtonText]="'Executar'"
  [rightFirstButtonIcon]="'fas fa-check'"
  [rightFirstButtonId]="'submit-filtered-items'"
  (rightFirstButtonEmitter)="confirm()"
  [rightFirstButtonDisabled]="!validateHasItem || isEditingCell"
  [rightSecondButtonDisabled]="!validateHasItem"
  [rightSecondButtonVisible]="true"
  [rightSecondButtonText]="'Limpar'"
  (rightSecondButtonEmitter)="clear()"
  dialogSize="extra-large"
>
  <ng-container [formGroup]="model">
    <eqp-fieldset label="Dados do contrato">
      <div class="d-flex flex-column">
        <div class="d-flex flex-row">
          <eqp-fieldset label="Contrato" class="col-5">
            <div class="d-flex flex-row justify-content-between">
              <eqp-nebular-input
                [disabled]="true"
                class="pr-3"
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Exercício"
                placeholder=""
                formControlName="exercicioContrato"
              ></eqp-nebular-input
              ><eqp-nebular-input
                [disabled]="true"
                class="pr-3"
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Número"
                placeholder=""
                formControlName="numeroContrato"
              ></eqp-nebular-input
              ><eqp-nebular-input
                [disabled]="true"
                [style]="'basic'"
                [type]="'text'"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Tipo de ato"
                placeholder=""
                formControlName="tipoAto"
              ></eqp-nebular-input>
            </div>
          </eqp-fieldset>
          <eqp-fieldset label="Licitação" class="col-7">
            <div class="d-flex flex-row justify-content-between">
              <eqp-nebular-input
                [disabled]="true"
                class="pr-4"
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Entidade"
                placeholder=""
                formControlName="entidade"
              ></eqp-nebular-input
              ><eqp-nebular-input
                [disabled]="true"
                class="pr-4"
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Exercício"
                placeholder=""
                formControlName="exercicioModalidade"
              ></eqp-nebular-input
              ><eqp-nebular-input
                [disabled]="true"
                class="pr-4"
                [style]="'basic'"
                [type]="'text'"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Modalidade"
                placeholder=""
                formControlName="modalidade"
              ></eqp-nebular-input>
              <eqp-nebular-input
                [disabled]="true"
                [style]="'basic'"
                [type]="'number'"
                [size]="'small'"
                [shape]="'rectangle'"
                label="Número"
                placeholder=""
                formControlName="numeroModalidade"
              ></eqp-nebular-input>
            </div>
          </eqp-fieldset>
        </div>
        <div class="d-flex flex-row pt-3">
          <eqp-nebular-search-field
            [disabled]="true"
            class="col-6"
            label="Fornecedor"
            formControlName="fornecedor"
            nameKey="pessoaNome"
            codeKey="pessoaCodigo"
          ></eqp-nebular-search-field>
          <eqp-nebular-search-field
            [disabled]="true"
            class="col-6"
            label="Local"
            formControlName="local"
            nameKey="nome"
            codeKey="codigo"
          ></eqp-nebular-search-field>
        </div>
      </div>
    </eqp-fieldset>
    <eqp-fieldset label="Seleção dos itens">
      <div class="p-3">
        <nb-radio-group class="d-flex flex-row" formControlName="tipoFiltro">
          <nb-radio value="SOLICITACAO">Solicitação</nb-radio>
          <nb-radio value="SOLICITANTE">Solicitante</nb-radio>
          <nb-radio value="LOCAL">Local</nb-radio>
        </nb-radio-group>
        <div class="w-50">
          <eqp-search-field
            *ngIf="gridByType === 'SOLICITACAO'"
            dialogTitle="Solicitação"
            formControlName="uuidFiltro"
            [uri]="'licitacao/contrato_aditivo/' + uuid + '/' + uri"
            [searchColumnsType]="columnsSearchName"
            nameKey="exercicio.exercicio"
            codeKey="numero"
            [objIsData]="false"
            label=" "
          ></eqp-search-field>
          <eqp-search-field
            *ngIf="gridByType === 'SOLICITANTE'"
            dialogTitle="Solicitante"
            formControlName="uuidFiltro"
            [uri]="'licitacao/contrato_aditivo/' + uuid + '/' + uri"
            [searchColumnsType]="columnsSearchName"
            nameKey="nome"
            codeKey="codigo"
            [objIsData]="false"
            label=" "
          ></eqp-search-field>
          <eqp-search-field
            *ngIf="gridByType === 'LOCAL'"
            dialogTitle="Local"
            formControlName="uuidFiltro"
            [uri]="'licitacao/contrato_aditivo/' + uuid + '/' + uri"
            [searchColumnsType]="columnsSearchName"
            nameKey="nome"
            codeKey="codigo"
            [objIsData]="false"
            label=" "
          ></eqp-search-field>
        </div>
      </div>
    </eqp-fieldset>
    <div class="d-flex mt-4 justify-content-end" style="gap: 0.5rem">
      <eqp-nebular-button
        [buttonShape]="'rectangle'"
        [buttonText]="'Carregar'"
        [buttonType]="'primary'"
        [buttonAppearance]="'outline'"
        (buttonEmitter)="loadItems()"
        [buttonIconVisible]="true"
        [buttonVisible]="true"
      ></eqp-nebular-button>
    </div>

    <div *ngIf="validateHasItem">
      <dx-data-grid
        [style.width]="'100%'"
        [dataSource]="data"
        [allowColumnResizing]="true"
        [columnAutoWidth]="true"
        [showColumnLines]="false"
        [showRowLines]="false"
        [showBorders]="false"
        [rowAlternationEnabled]="true"
        [wordWrapEnabled]="true"
        [loadPanel]="false"
        [columnHidingEnabled]="false"
        [remoteOperations]="true"
        keyExpr="lote"
        [masterDetail]="{
          enabled: true,
          autoExpandAll: true,
          template: 'itemGridTemplate'
        }"
      >
        <dxo-paging [pageSize]="10"></dxo-paging>

        <dxo-pager
          [showInfo]="true"
          [showNavigationButtons]="true"
          [showPageSizeSelector]="false"
        >
        </dxo-pager>

        <dxo-sorting mode="false"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

        <dxo-group-panel
          [visible]="false"
          [emptyPanelText]="''"
        ></dxo-group-panel>

        <dxo-editing
          mode="form"
          [allowUpdating]="false"
          [allowDeleting]="false"
          [allowAdding]="false"
          [useIcons]="true"
        >
        </dxo-editing>

        <dxi-column
          dataField="lote"
          dataType="number"
          caption="Lote"
          alignment="left"
          width="200"
          format="000"
        ></dxi-column>

        <div *dxTemplate="let subItem of 'itemGridTemplate'">
          <dx-data-grid
            [dataSource]="subItem.data['items']"
            [allowColumnResizing]="true"
            [columnAutoWidth]="true"
            [showColumnLines]="false"
            [showRowLines]="false"
            [showBorders]="false"
            [rowAlternationEnabled]="true"
            [wordWrapEnabled]="true"
            [loadPanel]="false"
            [columnHidingEnabled]="false"
            [remoteOperations]="true"
            keyExpr="processoLoteItem"
            (onRowUpdated)="onRowUpdated($event)"
            (onEditingStart)="onEditingStart($event)"
            (onRowValidating)="onRowValidating($event, subItem?.data)"
            (onCellPrepared)="onCellPrepared($event)"
            (onEditorPreparing)="onEditorPreparing($event)"
          >

            <dxo-paging [pageSize]="10"></dxo-paging>
            <dxo-pager
              [showInfo]="true"
              [showNavigationButtons]="true"
              [showPageSizeSelector]="false"
            >
            </dxo-pager>

            <dxo-header-filter [visible]="false"></dxo-header-filter>
            <dxo-filter-row [visible]="false"></dxo-filter-row>

            <dxo-sorting mode="false"></dxo-sorting>

            <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

            <dxo-group-panel
              [visible]="false"
              [emptyPanelText]="''"
            ></dxo-group-panel>
            <dxo-editing
              mode="cell"
              [allowUpdating]="!effectivated"
              [allowDeleting]="false"
              [allowAdding]="false"
              [useIcons]="true"
            >
            </dxo-editing>

            <dxi-column
              alignment="right"
              dataField="item"
              [allowEditing]="false"
              caption="Item"
              [allowSorting]="false"
              [allowFiltering]="false"
              format="000"
            ></dxi-column>

            <dxi-column
              alignment="right"
              dataField="produto.codigo"
              [allowEditing]="false"
              caption="Código"
              [allowSorting]="false"
              [allowFiltering]="false"
            ></dxi-column>

            <dxi-column
              alignment="left"
              width="200"
              dataField="produto.nome"
              [allowEditing]="false"
              caption="Nome"
              [allowSorting]="false"
              [allowFiltering]="false"
            ></dxi-column>
            <dxi-column
              alignment="left"
              dataField="marca"
              [allowEditing]="false"
              caption="Marca"
              [allowSorting]="false"
              [allowFiltering]="false"
            ></dxi-column>
            <dxi-column
              alignment="right"
              dataField="unidadeMedida.sigla"
              [allowEditing]="false"
              caption="Unidade"
              [allowSorting]="false"
              [allowFiltering]="false"
            ></dxi-column>

            <dxi-column
              alignment="right"
              [dataField]="'quantidadeDisponivel'"
              [allowEditing]="false"
              caption="Quantidade disponível"
              [allowSorting]="false"
              [allowFiltering]="false"
              cellTemplate="availableQuantityTemplate"
            ></dxi-column>

            <dxi-column
              alignment="right"
              [dataField]="'quantidadeDotada'"
              [allowEditing]="false"
              caption="Quantidade dotada"
              [allowSorting]="false"
              [allowFiltering]="false"
              cellTemplate="endowedQuantityTemplate"
            ></dxi-column>

            <dxi-column
              alignment="right"
              [dataField]="'quantidadeDesejada'"
              caption="Quantidade desejada"
              cellTemplate="wishedQuantityTemplate"
              [allowSorting]="false"
            ></dxi-column>

            <dxi-column
              dataField="precoUnitario"
              [allowEditing]="false"
              [allowSorting]="false"
              caption="Preço unitário"
              cellTemplate="unitPriceTemplate"
            ></dxi-column>

            <dxi-column
              alignment="right"
              [dataField]="'valorDisponivel'"
              caption="Valor disponível"
              [allowSorting]="false"
              [allowEditing]="false"
              [allowFiltering]="false"
              cellTemplate="availableValueTemplate"
            ></dxi-column>

            <dxi-column
              alignment="right"
              [allowEditing]="false"
              [dataField]="'valorDotado'"
              caption="Valor dotado"
              [allowSorting]="false"
              [allowFiltering]="false"
              cellTemplate="endowedValueTemplate"
            ></dxi-column>

            <dxi-column
              alignment="right"
              [dataField]="'valorDesejado'"
              caption="Valor desejado"
              [allowSorting]="false"
              [allowFiltering]="false"
              [allowEditing]="true"
              cellTemplate="wishedValueTemplate"
            ></dxi-column>
            <dxi-column
              dataField="solicitacaoNumero"
              dataType="text"
              caption="Solicitação"
              alignment="left"
              [allowEditing]="false"
              [allowSorting]="false"
              [allowFiltering]="false"
              [calculateCellValue]="combineSolicitacao"
            ></dxi-column>
            <dxi-column
              alignment="left"
              dataField="solicitante.nome"
              caption="Solicitante"
              [allowEditing]="false"
              [allowSorting]="false"
              [allowFiltering]="false"
            ></dxi-column
            ><dxi-column
              alignment="left"
              dataField="local.nome"
              caption="Local"
              [allowEditing]="false"
              [allowSorting]="false"
              [allowFiltering]="false"
            ></dxi-column>

            <div *dxTemplate="let data of 'priceTemplate'">
              {{ data?.value | currency: 'BRL' }}
            </div>

            <div *dxTemplate="let data of 'unitPriceTemplate'">
              {{ data?.value | currency: 'BRL': 'symbol' : '1.4-4' }}
            </div>

            <div *dxTemplate="let data of 'availableQuantityTemplate'">
              {{
                data?.data?.tipoControleSaldo?.codigo == 0 ?
                (data?.data?.quantidadeDisponivel | number: '1.0-2') : ''
              }}
            </div>

            <div *dxTemplate="let data of 'endowedQuantityTemplate'">
              {{
                data?.data?.tipoControleSaldo?.codigo == 0 ?
                (data?.data?.quantidadeDotada | number: '1.0-2') : ''
              }}
            </div>

            <div *dxTemplate="let data of 'wishedQuantityTemplate'">
              {{
                data?.data?.tipoControleSaldo?.codigo == 0 ?
                (data?.data?.quantidadeDesejada | number: '1.0-2') : ''
              }}
            </div>

            <div *dxTemplate="let data of 'availableValueTemplate'">
              {{
                data?.data?.tipoControleSaldo?.codigo == 1 ?
                (data?.data?.valorDisponivel | currency: 'BRL') : ''
              }}
            </div>

            <div *dxTemplate="let data of 'endowedValueTemplate'">
              {{
                data?.data?.tipoControleSaldo?.codigo == 1 ?
                (data?.data?.valorDotado | currency: 'BRL') : ''
              }}
            </div>

            <div *dxTemplate="let data of 'wishedValueTemplate'">
              {{
                data?.data?.tipoControleSaldo?.codigo == 1 ?
                (data?.data?.valorDesejado | currency: 'BRL') : ''
              }}
            </div>

          </dx-data-grid>
        </div>
      </dx-data-grid>
      <div class="row mt-2 justify-content-center">
        <div class="col-auto">
          <eqp-nebular-button
            [buttonShape]="'rectangle'"
            [buttonText]="'Zerar todos'"
            [buttonType]="'primary'"
            [buttonAppearance]="'outline'"
            (buttonEmitter)="resetValues()"
            [buttonIconVisible]="true"
            [buttonVisible]="true"
          ></eqp-nebular-button>
        </div>
      </div>
    </div>
  </ng-container>
</eqp-nebular-dialog>
