import { Component, Input, OnChanges, OnInit } from '@angular/core'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { CrudService } from '@common/services/crud.service'
import { SimpleRequestService } from '@common/services/simple-request.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { Subject } from 'rxjs'
import { finalize, take, takeUntil } from 'rxjs/operators'
import { ContractAdditionAttachmentFormComponent } from '../contract-addition-attachment-form/contract-addition-attachment-form.component'

@Component({
  selector: 'eqp-contract-addition-attachment-tab',
  templateUrl: './contract-addition-attachment-tab.component.html',
  styleUrls: ['./contract-addition-attachment-tab.component.scss'],
})
export class ContractAdditionAttachmentTabComponent implements OnChanges {
  @Input() adictiveUuid: string

  constructor(
    private dialogService: NbDialogService,
    private crudService: CrudService,
    private simpleRequestService: SimpleRequestService,
    private toastr: ToastrService,
  ) {}

  loading = false
  finalized = true
  private destroy$ = new Subject<null>()

  dataSource: any[]

  ngOnChanges(): void {
    if (this.adictiveUuid) {
      this.fetchGrid()
    }
  }

  fetchGrid() {
    this.crudService
      .getSingleData(`licitacao/contrato_aditivo/${this.adictiveUuid}/anexo`)
      .pipe(take(1))
      .subscribe({
        next: res => {
          this.dataSource = res.dados
        },
      })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Anexo'
      event.toolbarOptions.items[0].options.hint = 'Nova Anexo'
      event.toolbarOptions.items[0].options.onClick = () => this.edit()

      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  edit(uuid?: string) {
    let data: any
    if (uuid) {
      data = this.dataSource.find(item => item.uuid == uuid)
    }
    const dialogRef = this.dialogService.open(
      ContractAdditionAttachmentFormComponent,
      {
        context: {
          contractingUuid: this.adictiveUuid,
          uuid: uuid,
          attachmentData: data,
        },
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.fetchGrid()
      }
    })
  }

  downloadFile({ data }) {
    window.open(data.link, '_blank')
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(res => {
      if (res === 'S') {
        this.loading = true
        const url = `licitacao/contrato_aditivo/${this.adictiveUuid}/anexo/${uuid}`
        this.simpleRequestService
          .delete(url)
          .pipe(
            takeUntil(this.destroy$),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: `Anexo removido com sucesso.`,
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
