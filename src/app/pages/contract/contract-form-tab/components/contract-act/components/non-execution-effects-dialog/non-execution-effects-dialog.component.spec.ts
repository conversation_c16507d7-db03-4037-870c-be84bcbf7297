import { ComponentFixture, TestBed } from '@angular/core/testing';

import { NonExecutionEffectsDialogComponent } from './non-execution-effects-dialog.component';

describe('NonExecutionEffectsDialogComponent', () => {
  let component: NonExecutionEffectsDialogComponent;
  let fixture: ComponentFixture<NonExecutionEffectsDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ NonExecutionEffectsDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NonExecutionEffectsDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
