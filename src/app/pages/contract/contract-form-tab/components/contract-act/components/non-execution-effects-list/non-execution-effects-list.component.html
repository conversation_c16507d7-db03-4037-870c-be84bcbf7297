<eqp-loading *ngIf="loading$ | async"></eqp-loading>
<dx-data-grid
  id="contract-responsibles-list"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [dataSource]="dataSource"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="true"
  [remoteOperations]="true"
  keyExpr="uuid"
  (onToolbarPreparing)="onToolbarPreparing($event)"
>
  <dxo-export
    [enabled]="true"
    [excelWrapTextEnabled]="true"
    [excelFilterEnabled]="true"
    [fileName]="pageTitle"
  ></dxo-export>

  <dxo-paging [pageSize]="10"></dxo-paging>

  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <!-- <dxo-filter-row [visible]="true"></dxo-filter-row> -->

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar efeito"
  ></dxo-search-panel>

  <dxo-editing
    mode="form"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="true"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column
    dataField="numero"
    dataType="number"
    caption="Código"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="tipoParteOriginariaInexecucao.nome"
    caption="Parte originária"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="tipoEfeitoInexecucao.nome"
    caption="Efeito"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="tipoPenalidadeInexecucao.nome"
    caption="Tipo"
    dataType="date"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="dataPenalidade"
    dataType="date"
    caption="Data"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="dataPublicacaoPenalidade"
    dataType="date"
    caption="Publicação"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="valorPenalidade"
    caption="Valor"
    alignment="left"
    [format]="currencyFormat"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="uuid"
    caption=""
    alignment="left"
    cellTemplate="acaoColumn"
    [width]="150"
    [allowFiltering]="false"
    [allowSorting]="false"
  ></dxi-column>

  <div *dxTemplate="let data of 'acaoColumn'">
    <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
      <a
        title="Editar"
        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        (click)="edit(data.value)"
      >
      </a>
      <a
        title="Remover"
        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        (click)="remove(data.value)"
      >
      </a>
    </div>
  </div>
</dx-data-grid>
