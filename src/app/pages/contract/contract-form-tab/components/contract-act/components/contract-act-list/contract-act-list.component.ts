import { Component, OnInit } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { NbDialogService } from '@nebular/theme'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import DataSource from 'devextreme/data/data_source'
import { filter, take } from 'rxjs/operators'

import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { CrudService } from '@common/services/crud.service'
import { SimpleRequestService } from '@common/services/simple-request.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { ContractAdditionDialogComponent } from '../contract-addition-dialog/contract-addition-dialog.component'

@Component({
  selector: 'eqp-contract-act-list',
  templateUrl: './contract-act-list.component.html',
  styleUrls: ['./contract-act-list.component.scss'],
})
export class ContractActListComponent implements OnInit {
  dataSource: DataSource

  pageTitle: string = 'Atos'
  parentUuid: string
  loading = false

  dataInclusaoContrato = ''

  currencyFormat = currencyFormat

  constructor(
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private simpleRequestService: SimpleRequestService,
    private crudService: CrudService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    const { uuid } = this.route.parent.snapshot.params
    this.parentUuid = uuid
    this.fetchData()
  }

  fetchData() {
    const url = `licitacao/contrato/${this.parentUuid}/aditivo`
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro('uuid', url, 10),
      paginate: true,
      pageSize: 10,
      onChanged: this.getDataInclusaoContrato.bind(this),
      onLoadingChanged: isLoading => this.loading = isLoading
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Ato'
      event.toolbarOptions.items[0].options.hint = 'Novo ato'
      event.toolbarOptions.items[0].options.onClick = () => this.edit()
    }
  }

  edit(uuid?: string) {
    this.dialogService.open(ContractAdditionDialogComponent, {
      context: {
        parentUuid: this.parentUuid,
        dataInclusaoContrato: this.dataInclusaoContrato,
        uuid,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose
      .pipe(take(1))
      .subscribe(() => this.dataSource.reload())
  }

  remove(uuid: string) {
    this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    }).onClose
      .pipe(take(1), filter(res => res))
      .subscribe(() => {
        const url = `licitacao/contrato/${this.parentUuid}/aditivo/${uuid}`
        this.simpleRequestService.delete(url)
          .pipe(take(1))
          .subscribe(() => {
            this.toastr.send({
              success: true,
              message: `Ato contratual removido(a) com sucesso.`,
            })
            this.dataSource.reload()
          })
      })
  }

  getDataInclusaoContrato() {
    if (this.dataSource.items().length) {
      this.dataInclusaoContrato =
        this.dataSource.items()[0]?.contrato?.inclusaoTce
    } else {
      this.simpleRequestService
        .get(`licitacao/contrato/${this.parentUuid}`)
        .subscribe((res: any) => {
          this.dataInclusaoContrato = res.dados?.inclusaoTce
        })
    }
  }
}
