import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { SimpleRequestService } from '@common/services/simple-request.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto'
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { ContractPreLoadInterface } from '@pages/pre-load'
import DataSource from 'devextreme/data/data_source'
import { Observable } from 'rxjs'
import { take } from 'rxjs/operators'

@Component({
  selector: 'eqp-non-execution-effects-dialog',
  templateUrl: './non-execution-effects-dialog.component.html',
  styleUrls: ['./non-execution-effects-dialog.component.scss'],
})
export class NonExecutionEffectsDialogComponent implements OnInit {
  loading = false
  model: FormGroup

  contractPreload: ContractPreLoadInterface
  tipoEfeitoTemplate: NebularSelectDto[] = []
  tipoPenalidadeTemplate: NebularSelectDto[] = []

  type: DataSource
  penalty: DataSource
  originalPart: any[] = []
  selectedOriginalPart

  @Input() parentUuid: string
  @Input() uuid: string

  constructor(
    private dialogRef: NbDialogRef<NonExecutionEffectsDialogComponent>,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    private builder: FormBuilder,
    private crudService: CrudService,
    private simpleRequestService: SimpleRequestService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.fetchData()
  }

  fetchData() {
    const originalPartUrl = 'licitacao/tipo_parte_originaria_inexecucao'
    this.simpleRequestService
      .get<any>(originalPartUrl)
      .subscribe(({ data }) => {
        this.originalPart = data
      })

    const typeDataSourceConfig = {
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/tipo_efeito_inexecucao',
        10,
      ),
      paginate: true,
      pageSize: 10,
    }
    this.type = new DataSource(typeDataSourceConfig)

    const penaltyDataSourceConfig = {
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/tipo_penalidade_inexecucao',
        10,
      ),
      paginate: true,
      pageSize: 10,
    }
    this.penalty = new DataSource(penaltyDataSourceConfig)

    this.type.load()
    this.penalty.load()

    if (this.uuid) {
      const url = `licitacao/contrato/${this.parentUuid}/efeito_inexecucao/${this.uuid}`

      this.simpleRequestService.get<any>(url).subscribe(({ dados }) => {
        const {
          dataPenalidade,
          dataPublicacaoPenalidade,
          motivoPenalidade,
          tipoEfeitoInexecucao,
          tipoParteOriginariaInexecucao,
          tipoPenalidadeInexecucao,
          valorPenalidade,
          numero: codigo,
        } = dados

        const dto = {
          codigo,
          dataPenalidade,
          dataPublicacaoPenalidade,
          motivoPenalidade,
          tipoEfeitoInexecucao: tipoEfeitoInexecucao.uuid,
          tipoPenalidadeInexecucao: tipoPenalidadeInexecucao.uuid,
          valorPenalidade,
        }

        this.model.patchValue(dto)

        this.setSelectedOriginalPart(tipoParteOriginariaInexecucao)
      })
    }
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      codigo: [''],
      dataPenalidade: ['', Validators.required],
      dataPublicacaoPenalidade: ['', Validators.required],
      motivoPenalidade: ['', Validators.required],
      tipoEfeitoInexecucao: ['', Validators.required],
      tipoParteOriginariaInexecucao: ['', Validators.required],
      tipoPenalidadeInexecucao: ['', Validators.required],
      valorPenalidade: [0],
    })
  }

  dispose() {
    this.dialogRef.close()
  }

  onOriginalPartInput(event?: any) {
    const originalPart = this.originalPart.find(
      originalPart => originalPart.codigo == event,
    )

    if (originalPart) {
      this.setSelectedOriginalPart(originalPart)
    } else {
      this.setSelectedOriginalPart()
    }
  }

  onOriginalPartDialog() {
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar parte originária'

    dialogRef.componentRef.instance.dataGrid = this.originalPart
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'codigo',
        dataType: 'number',
        alignment: 'left',
        width: 200,
      },
      {
        caption: 'Nome',
        dataField: 'nome',
        alignment: 'left',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) this.setSelectedOriginalPart(res)
    })
  }

  setSelectedOriginalPart(originalPart?: any) {
    if (originalPart) {
      this.selectedOriginalPart = originalPart
      this.model.patchValue({
        tipoParteOriginariaInexecucao: originalPart.uuid,
      })
    } else {
      this.selectedOriginalPart = undefined
      this.model.get('tipoParteOriginariaInexecucao').patchValue('')
    }
  }

  confirm() {
    if (this.model.valid) {
      const dataPublicacaoPenalidade = new Date(
        this.dto.dataPublicacaoPenalidade,
      )

      const dataPenalidade = new Date(this.dto.dataPenalidade)

      // if (dataPublicacaoPenalidade.getTime() >= dataPenalidade.getTime()) {
        let request: Observable<any>
        const url = `licitacao/contrato/${this.parentUuid}/efeito_inexecucao/`

        if (!this.uuid) {
          request = this.simpleRequestService.post(url, this.dto)
        } else {
          request = this.simpleRequestService.put(url + this.uuid, this.dto)
        }

        request.pipe(take(1)).subscribe(
          res => {
            this.toastr.send({
              success: true,
              title: 'Sucesso',
              message: `Efeito ${
                this.uuid ? 'atualizado' : 'cadastrado'
              } com sucesso`,
            })
            this.dialogRef.close(true)
          },
          error => {
            this.toastr.send({
              error: true,
              title: 'Error',
              message:
                error.mensagens ||
                `Falha ao ${this.uuid ? 'atualizar' : 'cadastrar'} efeito`,
            })
          },
        )
      // } else {
      //   this.toastr.send({
      //     error: true,
      //     title: 'Erro',
      //     message:
      //       'A data da publicação deve ser igual ou superior à data da penalidade! Por favor, verifique e tente novamente',
      //   })
      // }
    }
  }

  get dto() {
    const {
      dataPenalidade,
      dataPublicacaoPenalidade,
      motivoPenalidade,
      tipoEfeitoInexecucao,
      tipoParteOriginariaInexecucao,
      tipoPenalidadeInexecucao,
      valorPenalidade,
    } = this.model.getRawValue()

    const dto = {
      dataPenalidade,
      dataPublicacaoPenalidade,
      motivoPenalidade,
      tipoEfeitoInexecucao: { uuid: tipoEfeitoInexecucao },
      tipoParteOriginariaInexecucao: { uuid: tipoParteOriginariaInexecucao },
      tipoPenalidadeInexecucao: { uuid: tipoPenalidadeInexecucao },
      valorPenalidade,
    }

    return dto
  }
}
