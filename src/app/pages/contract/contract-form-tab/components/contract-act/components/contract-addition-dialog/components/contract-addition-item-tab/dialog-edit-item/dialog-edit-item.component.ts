import { Component, Input, OnInit } from '@angular/core'
import { FormGroup, FormBuilder } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { ContractItemDialogComponent } from '@pages/contract/contract-form-tab/components/contract-items/contract-item-dialog/contract-item-dialog.component'
import { ContractAdditionService } from '@pages/contract/services/contract-addition.service'
import { ContractItemService } from '@pages/contract/services/contract-item.service'
import { take, finalize } from 'rxjs/operators'

@Component({
  selector: 'eqp-dialog-edit-item',
  templateUrl: './dialog-edit-item.component.html',
  styleUrls: ['./dialog-edit-item.component.scss'],
})
export class DialogEditItemComponent implements OnInit {
  model: FormGroup
  @Input() parentUuid: string
  @Input() itemUuid: string
  @Input() uuid: string

  loading: boolean = false
  data: any
  enableButtonSend: boolean = true

  constructor(
    private toastr: ToastrService,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ContractItemDialogComponent>,
    private service: ContractItemService,
    private contractAdditionService: ContractAdditionService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.fetchData()
    this.initialObservable()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      deQuantidade: [],
      deValor: [],
      deTotal: [],
      paraQuantidade: [],
      paraValor: [],
      paraTotal: [],
    })
  }

  fetchData() {
    this.loading = true
    this.contractAdditionService
      .getItemIndividualAditive(this.parentUuid, this.uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.data = res.dados
        this.patchValuesForm(res.dados)
      })
  }

  initialObservable() {
    this.model.get('paraQuantidade').valueChanges.subscribe(res => {
      this.model.patchValue({
        paraTotal: res * this.model.get('paraValor').value,
      })
    })

    this.model.get('paraValor').valueChanges.subscribe(res => {
      this.model.patchValue({
        paraTotal: res * this.model.get('paraQuantidade').value,
      })
    })
  }

  hasChangesAfterLoad(): boolean {
    return this.model.dirty
  }

  patchValuesForm(data) {
    this.model.patchValue({
      deQuantidade: data.quantidade,
      deValor: data.valor,
      deTotal: data.quantidade * data.valor,
      paraQuantidade: data.quantidade,
      paraValor: data.valor,
      paraTotal: data.quantidade * data.valor,
    })
  }

  get balanceControlTypeCode(): number | undefined {
    return this.data?.processoLoteItem.tipoControleSaldo.codigo
  }

  cancel() {
    this.dialogRef.close()
  }

  private prepare() {
    return {
      uuid: this.uuid,
      valor: this.model.get('paraValor').value,
      quantidade: +this.model.get('paraQuantidade').value,
    }
  }

  confirm() {
    this.loading = true
    this.contractAdditionService
      .putItem(this.parentUuid, this.prepare(), this.uuid)
      .pipe(take(1), finalize(() => this.loading = false))
      .subscribe(res => {
        this.toastr.send({
          title: 'Sucesso',
          message: `Item salvo com sucesso!`,
          success: true,
        })
        this.dialogRef.close(res)
      })
  }
}
