import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ContractAdditionAttachmentFormComponent } from './contract-addition-attachment-form.component';

describe('ContractAdditionAttachmentFormComponent', () => {
  let component: ContractAdditionAttachmentFormComponent;
  let fixture: ComponentFixture<ContractAdditionAttachmentFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ContractAdditionAttachmentFormComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractAdditionAttachmentFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
