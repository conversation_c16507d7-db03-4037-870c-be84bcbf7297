<div class="mt-3">
  <dx-data-grid
    keyExpr="uuid"
    (onToolbarPreparing)="onToolbarPreparing($event)"
    [allowColumnResizing]="true"
    [columnAutoWidth]="true"
    [dataSource]="dataSource"
    [showColumnLines]="false"
    [showRowLines]="false"
    [showBorders]="false"
    [rowAlternationEnabled]="true"
    [wordWrapEnabled]="true"
    [loadPanel]="false"
    [columnHidingEnabled]="true"
    [remoteOperations]="true"
    [nbSpinner]="loading"
    nbSpinnerSize="large"
    nbSpinnerStatus="info"
  >
    <dxo-export
      [enabled]="true"
      [excelWrapTextEnabled]="true"
      [excelFilterEnabled]="true"
      [fileName]="pageTitle"
    ></dxo-export>

    <dxo-paging [pageSize]="10"></dxo-paging>

    <dxo-pager
      [showInfo]="true"
      [showNavigationButtons]="true"
      [showPageSizeSelector]="false"
    >
    </dxo-pager>

    <dxo-header-filter [visible]="false"> </dxo-header-filter>
    <dxo-filter-row [visible]="true"></dxo-filter-row>

    <dxo-sorting mode="multiple"></dxo-sorting>

    <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

    <dxo-group-panel emptyPanelText="" [visible]="false"></dxo-group-panel>

    <dxo-search-panel
      placeholder="Buscar atos"
      [visible]="true"
    ></dxo-search-panel>

    <dxo-editing
      [allowUpdating]="false"
      [allowDeleting]="false"
      [allowAdding]="true"
      [useIcons]="true"
      mode="form"
    >
    </dxo-editing>

    <dxi-column
      dataField="numero"
      dataType="number"
      caption="Código"
      alignment="left"
    ></dxi-column>

    <dxi-column
      dataField="tipoAditivoAto.nome"
      caption="Tipo de ato"
      alignment="left"
      dataType="string"
    ></dxi-column>

    <dxi-column
      dataField="tipoAditivoContrato.nome"
      alignment="left"
      caption="Tipo do aditivo"
      dataType="string"
    ></dxi-column>

    <dxi-column
      dataField="dataAssinatura"
      dataType="date"
      caption="Data da assinatura"
      alignment="left"
    ></dxi-column>

    <dxi-column
      dataField="dataTerminoVigencia"
      dataType="date"
      caption="Término vigência"
      alignment="left"
    ></dxi-column>

    <dxi-column
      dataField="dataTerminoExecucao"
      dataType="date"
      caption="Término execução"
      alignment="left"
    ></dxi-column>

    <dxi-column
      dataField="valor"
      dataType="number"
      caption="Valor"
      cellTemplate="priceTemplate"
    ></dxi-column>

    <dxi-column
      dataField="uuid"
      caption=""
      cellTemplate="acaoColumn"
      [width]="80"
      [allowFiltering]="false"
      [allowEditing]="false"
      [allowSorting]="false"
    ></dxi-column>

    <div *dxTemplate="let data of 'priceTemplate'">
      {{data.value | currency: 'BRL'}}
    </div>

    <div *dxTemplate="let data of 'acaoColumn'">
      <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
        <a
          title="Editar"
          (click)="edit(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          title="Remover"
          (click)="remove(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </div>
  </dx-data-grid>
</div>
