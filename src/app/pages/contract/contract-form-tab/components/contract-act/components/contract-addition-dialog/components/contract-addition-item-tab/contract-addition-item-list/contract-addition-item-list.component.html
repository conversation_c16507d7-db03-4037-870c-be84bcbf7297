<div class="text-left mt-4 d-flex" style="gap: 0.5rem">
  <eqp-loading *ngIf="loading"></eqp-loading>
  <eqp-nebular-button
    [buttonDisabled]="false"
    buttonText="Carregar itens"
    [buttonIconVisible]="true"
    [buttonAppearance]="'outline'"
    buttonIcon="fas fa-sync"
    buttonTitle="Carregar itens"
    [buttonDisabled]="buttonDisabled"
    [buttonVisible]="true"
    (buttonEmitter)="loadItems()"
  ></eqp-nebular-button>
</div>
<dx-data-grid
  id="contract-addition-item-grid"
  [dataSource]="dataSource"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="false"
  [remoteOperations]="true"
  keyExpr="uuid"
  class="mt-0"
>
  <dxo-paging [pageSize]="10"></dxo-paging>
  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"></dxo-header-filter>
  <dxo-filter-row [visible]="false"></dxo-filter-row>

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxo-toolbar>
    <dxi-item location="before">
      <dx-button
        text="Botão"
        width="120"
        icon="fas fa-sync"
        (onClick)="increaseItems($event)"
      >
      </dx-button>
    </dxi-item>
    <dxi-item name="columnChooserButton"></dxi-item>
  </dxo-toolbar>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar item"
  ></dxo-search-panel>

  <dxo-editing
    mode="cell"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="false"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column
    dataField="solicitacao.numero"
    caption="Solicitação"
    alignment="left"
    [allowEditing]="false"
    [calculateCellValue]="combineSolicitacao"
  ></dxi-column>

  <dxi-column
    dataField="solicitacaoItem.lote.lote"
    dataType="number"
    caption="Lote"
    alignment="left"
    [allowEditing]="false"
    [width]="100"
  ></dxi-column>

  <dxi-column
    dataField="processoLoteItem.item"
    dataType="number"
    caption="Item"
    alignment="left"
    [width]="100"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    dataField="solicitacaoItem.produto.codigo"
    dataType="number"
    caption="Código"
    alignment="left"
    [width]="100"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    dataField="solicitacaoItem.produto.nome"
    dataType="number"
    caption="Produto"
    alignment="left"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    dataField="quantidade"
    dataType="number"
    caption="Quantidade"
    alignment="left"
    [allowEditing]="false"
    [width]="100"
  ></dxi-column>

  <dxi-column
    dataField="valor"
    caption="Valor"
    alignment="left"
    cellTemplate="unitPriceTemplate"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    caption="Total"
    dataField="total"
    dataType="number"
    alignment="left"
    cellTemplate="totalPriceTemplate"
  >
  </dxi-column>

  <dxi-column
    caption="Tipo controle"
    alignment="left"
    dataField="processoLoteItem.tipoControleSaldo.nome"
  >
  </dxi-column>

  <dxi-column
    dataField="uuid"
    caption=""
    [width]="80"
    [allowFiltering]="false"
    [allowEditing]="false"
    [allowSorting]="false"
    cellTemplate="acaoColumn"
  ></dxi-column>

  <div *dxTemplate="let data of 'unitPriceTemplate'">
    {{data.value | currency: 'BRL' : 'symbol' : '1.4-4'}}
  </div>

  <div *dxTemplate="let data of 'totalPriceTemplate'">
    {{(data.data.valor * data.data.quantidade) | currency: 'BRL'}}
  </div>

  <div *dxTemplate="let data of 'acaoColumn'">
    <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
      <a
        title="Editar"
        (click)="edit(data.value)"
        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
      >
      </a
      ><a
        title="Remover"
        (click)="remove(data)"
        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
      >
      </a>
    </div>
  </div>
</dx-data-grid>
