<eqp-nebular-dialog
  dialogTitle="Inserir Anexo"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-situation-form-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="rightFirstButtonDisabled"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-situation-form-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
  [formGroup]="model"
>
  <div style="position: relative">
    <eqp-loading *ngIf="loading"></eqp-loading>
    <div class="d-flex mb-3" style="gap: 1.8rem">
      <div class="d-flex" style="gap: 0.5rem; width: 100%">
        <eqp-nebular-button
          style="padding-top: 1.8rem"
          title="Selecionar Arquivo"
          [buttonText]="
            !!model.get('nomeArquivo')?.value || !!uuid
              ? 'Alterar Arquivo'
              : 'Selecionar Arquivo'
          "
          (click)="selecionarArquivo()"
          [buttonDisabled]="!!model.get('link')?.value"
          [buttonVisible]="true"
        ></eqp-nebular-button>
        <div style="width: 100%">
          <eqp-nebular-input
            [style]="'basic'"
            [type]="'text'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="nomeArquivo"
            disabled="true"
            name="Arquivo"
            label="Arquivo"
            placeholder="Arquivo"
          ></eqp-nebular-input>
        </div>
      </div>
      <div style="width: 100%">
        <eqp-nebular-input
          [style]="'basic'"
          [type]="'text'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="nome"
          disabled="false"
          name="Nome"
          label="Nome"
          placeholder="Nome"
        ></eqp-nebular-input>
      </div>
    </div>
    <div class="row mb-3">
      <div class="col-12">
        <eqp-nebular-input
          [style]="'textArea'"
          [size]="'small'"
          [rows]="6"
          [shape]="'rectangle'"
          formControlName="descricao"
          name="Descrição"
          label="Descrição"
          placeholder="Descrição"
        ></eqp-nebular-input>
      </div>
    </div>
    <div class="d-flex justify-content-start">
      <eqp-field-checkbox
        formControlName="publicaInternet"
        nebularLabel="Publicar no portal"
        nebularLabelPosition="right"
        label="Publicar no portal"
        title="Publicar no portal"
      ></eqp-field-checkbox>
    </div>
  </div>
</eqp-nebular-dialog>
