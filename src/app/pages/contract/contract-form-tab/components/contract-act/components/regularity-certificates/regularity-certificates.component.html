<eqp-loading *ngIf="loading$ | async"></eqp-loading>
<dx-data-grid
  id="contract-regularity-certificates"
  keyExpr="uuid"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [dataSource]="dataSource"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="true"
  [remoteOperations]="true"
>
  <dxo-paging [pageSize]="10"></dxo-paging>

  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <dxo-filter-row [visible]="false"></dxo-filter-row>

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar certificados"
  ></dxo-search-panel>

  <!-- <dxo-editing
    mode="form"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="true"
    [useIcons]="true"
  >
  </dxo-editing> -->

  <dxi-column
    dataField="numeroCertidao"
    caption="Documento"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="certidao.tipoCertidaoTceCodigo"
    caption="Tipo"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="dataEmissao"
    caption="Data de emissão"
    dataType="date"
    alignment="left"
  ></dxi-column>
  <dxi-column
    dataField="dataValidade"
    caption="Data de validade"
    dataType="date"
    alignment="left"
  ></dxi-column>

  <dxi-column
    dataField="dataApresentacao"
    dataType="date"
    caption="Data de apresentação"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="certidao.codigo"
    caption="Número de certidão"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>
</dx-data-grid>
