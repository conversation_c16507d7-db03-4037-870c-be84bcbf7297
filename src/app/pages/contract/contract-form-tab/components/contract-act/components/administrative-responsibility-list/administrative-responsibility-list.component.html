<eqp-loading *ngIf="loading$ | async"></eqp-loading>
<dx-data-grid
  id="contract-responsibles-list"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [dataSource]="dataSource"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="true"
  [remoteOperations]="true"
  keyExpr="uuid"
  (onToolbarPreparing)="onToolbarPreparing($event)"
>
  <dxo-export
    [enabled]="true"
    [excelWrapTextEnabled]="true"
    [excelFilterEnabled]="true"
    [fileName]="pageTitle"
  ></dxo-export>

  <dxo-paging [pageSize]="10"></dxo-paging>

  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <dxo-filter-row [visible]="true"></dxo-filter-row>

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar Responsabilidade"
  ></dxo-search-panel>

  <dxo-editing
    mode="form"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="true"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column
    dataField="numero"
    dataType="number"
    caption="Código"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="tipoResponsabilidadeAdministrativa.nome"
    caption="Tipo"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="pessoaResponsavel.nome"
    caption="Responsável"
    alignment="left"
    [allowFiltering]="false"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="atoDesignacao"
    caption="Ato de designação"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="dataInicial"
    dataType="date"
    caption="Data inicial"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="dataFinal"
    dataType="date"
    caption="Data final"
    alignment="left"
    [allowSorting]="false"
  ></dxi-column>

  <dxi-column
    dataField="uuid"
    caption=""
    alignment="left"
    cellTemplate="acaoColumn"
    [width]="150"
    [allowFiltering]="false"
    [allowSorting]="false"
  ></dxi-column>

  <div *dxTemplate="let data of 'acaoColumn'">
    <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
      <a
        title="Editar"
        (click)="edit(data.value)"
        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
      >
      </a>
      <a
        title="Remover"
        (click)="remove(data.value)"
        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
      >
      </a>
    </div>
  </div>
</dx-data-grid>
