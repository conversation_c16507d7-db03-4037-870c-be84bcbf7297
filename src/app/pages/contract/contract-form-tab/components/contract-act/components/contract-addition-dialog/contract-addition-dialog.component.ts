import { DatePipe } from '@angular/common'
import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { SimpleRequestService } from '@common/services/simple-request.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto'
import { NbDialogRef, NbTabComponent } from '@nebular/theme'
import { PersonInterface } from '@pages/effort-request/interfaces/person'
import { TypesService } from '@pages/shared/services/types.service'
import DataSource from 'devextreme/data/data_source'
import { Observable, Subject } from 'rxjs'
import { filter, finalize, take, takeUntil } from 'rxjs/operators'
import { ContractAdditionItemLoadService } from './components/contract-addition-item-tab/contract-addition-item-load/contract-addition-item-load.service'

@Component({
  selector: 'eqp-contract-addition-dialog',
  templateUrl: './contract-addition-dialog.component.html',
  styleUrls: ['./contract-addition-dialog.component.scss'],
  providers: [DatePipe],
})
export class ContractAdditionDialogComponent implements OnInit, OnDestroy {
  @Input() uuid: string
  @Input() parentUuid: string
  @Input() dataInclusaoContrato: string
  initialTab = true

  destroy$ = new Subject<void>()
  actTypeCode: number;

  loading: boolean = false
  additiveTypeCode: number
  model: FormGroup
  additionTypes: any
  actTypes: any
  actTypeSelected: any
  additionTypeSelected: any
  valueIsRequired: boolean = false
  deadlineIsRequired: boolean = false
  additionTypeIsRequired: boolean = false
  tipoRedimensionamentoContratoTceRequired: boolean = true
  itemsLoaded: boolean = false
  secondeClassificationOptions: boolean = true
  apostilleFlagDisabled = false;

  requiredSignatureDate = false
  requiredInclusionTceDate = false
  requiredExpirationDate = false
  requiredExecutionEndDate = false
  requiredContractualForecast = false
  requiredReason = false
  contractActLoading = false;
  aditiveTypeContractTowIsRequired = false;

  tipoAto: DataSource
  tipoAditivo: DataSource
  tipoAditivoContrato: DataSource
  tipoAditivoTce: DataSource

  operationAdditiveTypeOne: any[] = [];
  operationAdditiveTypeTwo: any[] = []

  tipoRedimensionamento: DataSource
  tipoMotivoRescisao: DataSource
  mesReajuste: DataSource
  tipoIndiceCorrecao: DataSource

  licitationUrl: string

  tipoPrevisaoContratualTemplate: NebularSelectDto[] = [
    {
      texto: 'Existe',
      valor: 'S',
    },
    {
      texto: 'Não existe',
      valor: 'N',
    },
  ]

  personsData: PersonInterface[] = []

  constructor(
    private builder: FormBuilder,
    private toastr: ToastrService,
    private typeService: TypesService,
    private dialogRef: NbDialogRef<ContractAdditionDialogComponent>,
    private crudService: CrudService,
    private simpleRequestService: SimpleRequestService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadSelectData()
    this.startFieldListeners();
    if (this.uuid) {
      this.loadForm();
    }
  }

  changeTabEvent(event: NbTabComponent) {
    this.initialTab = event.tabTitle == 'Ato contratual'
  }

  onLoadItems(e): void {
    if(e) this.loadForm(true);
  };

  getNewModel(): FormGroup {
    return this.builder.group({
      dataAssinatura: [],
      dataInclusaoTce: [],
      dataProrrogacaoCompra: [],
      dataPublicacao: [] /*?*/,
      dataTerminoExecucao: [],
      dataTerminoVigencia: [],
      descricao: [],
      flagAnularItemContrato: ['N'] /*?*/,
      flagApostilamento: [false],
      flagCovid19: ['N'],
      flagPrevisaoContratual: ['N'],
      fornecedor: [],
      indiceCorrecao: [],
      mesReajuste: [''],
      motivo: [],
      periodicidadeReajuste: [],
      pessoaRepresentante: [],
      tipoAditivoAto: [[], Validators.required],
      tipoAditivoContrato: [],
      tipoAditivoContratoAnulado: [] /*?*/,
      tipoAditivoContratoDois: [],
      tipoAditivoContratoUm: [],
      tipoMotivoRescisaoContratoTce: [],
      tipoOperacaoAditivoContratoTceDois: [],
      tipoOperacaoAditivoContratoTceUm: [],
      tipoRedimensionamentoContratoTce: [],
      valor: [0],
      valorAcrescimo: [0],
      valorProrrogacao: [0],
      valorReajuste: [0],
      valorRecomposicao: [0],
      valorSupressao: [0],
      codigo: [],
      total: [0],
    })
  }

  private startFieldListeners() {
    this.model
    .get('tipoAditivoContrato')
    .valueChanges.pipe(filter(value => value), takeUntil(this.destroy$))
    .subscribe(res => this.onChangeContractAdditiveType(res));

    this.model
    .get('tipoAditivoAto')
    .valueChanges.pipe(filter(value => value), takeUntil(this.destroy$))
    .subscribe(res => this.onChangeActAdditiveType(res));

    this.model
    .get('tipoAditivoContratoUm')
    .valueChanges.pipe(filter(value => value), takeUntil(this.destroy$))
    .subscribe(res => this.onChangeOperationAditiveTypeOne(res));

    this.model
    .get('tipoAditivoContratoDois')
    .valueChanges.pipe(filter(value => value), takeUntil(this.destroy$))
    .subscribe(res => this.onChangeOperationAditiveTypeTwo(res));
  }

  private onChangeActAdditiveType(additiveTypeUuid: string) {
    const additiveTypeCode = this.tipoAto
    .items().filter(value => value.uuid == additiveTypeUuid)[0].codigo;

    this.actTypeCode = additiveTypeCode;

    if(additiveTypeCode != 2) this.apostilleFlagDisabled = false;
    
    switch (additiveTypeCode) {
      case 2: 
        this.requiredSignatureDate = false
        this.requiredInclusionTceDate = true
        this.requiredExpirationDate = false
        this.requiredExecutionEndDate = false
        this.requiredContractualForecast = true
        this.requiredReason = true
        this.apostilleFlagDisabled = true;
        break
      case 5:
        this.requiredSignatureDate = false
        this.requiredInclusionTceDate = true
        this.requiredExpirationDate = false
        this.requiredExecutionEndDate = false
        this.requiredContractualForecast = true
        this.requiredReason = true
        break
    }
  }

  private onChangeContractAdditiveType(additiveTypeUuid: string) {
    const additiveTypeCode = this.tipoAditivo
    .items().filter(value => value.uuid == additiveTypeUuid)[0]?.codigo;

    if(additiveTypeCode != 3 && !this.uuid){
      this.model.get('tipoAditivoContratoUm').reset(null, {emitEvent: false});
      this.model.get('tipoAditivoContratoDois').reset(null, {emitEvent: false});
    };
    
    switch (additiveTypeCode) {
      case 1: 
        this.requiredSignatureDate = false
        this.requiredInclusionTceDate = true
        this.requiredExpirationDate = false
        this.requiredExecutionEndDate = false
        this.requiredContractualForecast = true
        this.requiredReason = true
        break
      case 2: 
        this.requiredSignatureDate = false
        this.requiredInclusionTceDate = true
        this.requiredExpirationDate = false
        this.requiredExecutionEndDate = false
        this.requiredContractualForecast = true
        this.requiredReason = true
        break
      case 3:
        this.loadClassificationAdditives();
        this.requiredSignatureDate = false
        this.requiredInclusionTceDate = true
        this.requiredExpirationDate = false
        this.requiredExecutionEndDate = false
        this.requiredContractualForecast = true
        this.requiredReason = true
        break
      case 4:
        this.requiredSignatureDate = false
        this.requiredInclusionTceDate = true
        this.requiredExpirationDate = false
        this.requiredExecutionEndDate = false
        this.requiredContractualForecast = true
        this.requiredReason = true
        break
      case 5:
        this.requiredSignatureDate = false
        this.requiredInclusionTceDate = true
        this.requiredExpirationDate = false
        this.requiredExecutionEndDate = false
        this.requiredContractualForecast = true
        this.requiredReason = true
        break
      case 6:
        this.requiredSignatureDate = false
        this.requiredInclusionTceDate = true
        this.requiredExpirationDate = false
        this.requiredExecutionEndDate = false
        this.requiredContractualForecast = true
        this.requiredReason = true
        break
    }
  }

  private onChangeOperationAditiveTypeOne(operationAditiveTypeUuid: string): void{
    this.model.get('tipoOperacaoAditivoContratoTceUm').reset(null, {emitEvent: false});
    
    this.simpleRequestService.get(
      `licitacao/tipo_operacao_aditivo_contrato_tce/tipo_aditivo/${operationAditiveTypeUuid}`
    ).pipe(take(1)).subscribe(res => {
      this.operationAdditiveTypeOne = res?.dados as any[];
    });
  }

  private onChangeOperationAditiveTypeTwo(operationAditiveTypeUuid: string): void{
    this.model.get('tipoOperacaoAditivoContratoTceDois').reset(null, {emitEvent: false});
    
    this.simpleRequestService.get(
      `licitacao/tipo_operacao_aditivo_contrato_tce/tipo_aditivo/${operationAditiveTypeUuid}`
    ).pipe(take(1)).subscribe(res => {
      this.operationAdditiveTypeTwo = res?.dados as any[];
    });
  }

  private loadClassificationAdditives(): void{
    const aditiveTerm = this.tipoAditivoContrato.items().filter(item => item.codigo == 1)[0];
    const aditiveValue = this.tipoAditivoContrato.items().filter(item => item.codigo == 4)[0];
    this.model.get('tipoAditivoContratoUm').patchValue(aditiveTerm?.uuid, {emitEvent: false});
    this.model.get('tipoAditivoContratoDois').patchValue(aditiveValue?.uuid, {emitEvent: false});
  }

  loadForm(onlyContractActTable: boolean = false): void{
    const url = `licitacao/contrato/${this.parentUuid}/aditivo/${this.uuid}`

    if(onlyContractActTable){ 
      this.contractActLoading = true;
    } else {
      this.loading = true
    };

    this.simpleRequestService
      .get(url)
      .pipe(finalize(() => {this.loading = false, this.contractActLoading = false}))
      .subscribe((res: any) => {
        const dto = {
          ...res.dados,
          tipoAditivoAto: res.dados.tipoAditivoAto?.uuid,
          tipoAditivoContrato: res.dados.tipoAditivoContrato?.uuid,
          tipoAditivoContratoAnulado:
            res.dados.tipoAditivoContratoAnulado?.uuid,
          tipoAditivoContratoDois: res.dados.tipoAditivoContratoDois?.uuid,
          tipoAditivoContratoUm: res.dados.tipoAditivoContratoUm?.uuid,
          tipoMotivoRescisaoContratoTce:
            res.dados.tipoMotivoRescisaoContratoTce?.uuid,
          tipoOperacaoAditivoContratoTceDois:
            res.dados.tipoOperacaoAditivoContratoTceDois?.uuid,
          tipoOperacaoAditivoContratoTceUm:
            res.dados.tipoOperacaoAditivoContratoTceUm?.uuid,
          tipoRedimensionamentoContratoTce:
            res.dados.tipoRedimensionamentoContratoTce?.uuid,
          indiceCorrecao: res.dados.indiceCorrecao?.uuid,
          fornecedor: res.dados.fornecedor,
          pessoaRepresentante: res.dados.pessoaRepresentante,
          flagApostilamento: res.dados.flagApostilamento == 'S' ? true : false,
          flagCovid19: res.dados.flagCovid19 == 'S' ? true : false,
          codigo: res.dados.numero,
          mesReajuste: String(res.dados.mesReajuste || ''),
        }

        
        const tipoAditivoContratoUuid = res.dados.tipoAditivoContrato?.uuid
        if (tipoAditivoContratoUuid) {
          const tipoSelecionado = this.tipoAditivo
          .items()
          .find(tipo => tipo.uuid == tipoAditivoContratoUuid)
          
          if (tipoSelecionado) {
            this.onChangeAddtiveType(tipoSelecionado)
          }
        }
        this.model.patchValue(dto, {emitEvent: false});
      })
  }

  providerUrl() {
    let url = 'licitacao/contrato/fornecedor'
    if (this.additiveTypeCode != 5) {
      url += `?contratoUuid=${this.parentUuid}`
    }
    return url
  }

  onChangeAddtiveType(e: string) {
    this.model.get('fornecedor').reset()
    const obj = this.tipoAditivo.items().find(value => value.uuid == e)
    this.additiveTypeCode = obj?.codigo;
    if(obj?.codigo == 3){
      this.model.get('tipoAditivoContratoDois').setValidators(Validators.required);
      this.aditiveTypeContractTowIsRequired = true;
    }
    if (obj?.codigo != 3) {
      this.aditiveTypeContractTowIsRequired = false;
      this.model.get('tipoAditivoContratoDois').reset(null , {emitEvent: false})
      this.model.get('tipoOperacaoAditivoContratoTceDois').reset(null , {emitEvent: false});
    } 
  }

  onChangeOperationContractTceOne(e: string) {
    const obj = this.operationAdditiveTypeOne.find(value =>
      value.nome.includes('Redimensionamento de Objeto'),
    )

    if (obj && obj.uuid == e) {
      this.tipoRedimensionamentoContratoTceRequired = false
    } else {
      this.tipoRedimensionamentoContratoTceRequired = true
    }
  }

  loadSelectData() {
    this.simpleRequestService
    .get<any[]>('licitacao/tipo_operacao_aditivo_contrato_tce?skip=0&take=0')
    .subscribe(res => {
      this.operationAdditiveTypeOne = res.data as any[];
      this.operationAdditiveTypeTwo = res.data as any[];
    });

    this.tipoAto = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/contrato_aditivo_tipo_ato',
        20,
      ),
      paginate: true,
      pageSize: 20,
    })
    this.tipoAto.load()

    this.tipoAditivo = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/contrato_aditivo_tipo_aditivo',
        20,
      ),
      paginate: true,
      pageSize: 20,
    })
    this.tipoAditivo.load()

    this.tipoAditivoContrato = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/tipo_aditivo_contrato_tce',
        20,
      ),
      paginate: true,
      pageSize: 20,
    })
    this.tipoAditivoContrato.load()

    this.tipoAditivoTce = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/tipo_parte_contrato_tce',
        20,
      ),
      paginate: true,
      pageSize: 20,
    })
    this.tipoAditivoTce.load()

    this.tipoRedimensionamento = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/tipo_redimensionamento_objeto_contrato_tce',
        20,
      ),
      paginate: true,
      pageSize: 20,
    })
    this.tipoRedimensionamento.load()

    this.tipoMotivoRescisao = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/tipo_motivo_rescisao_contrato_tce',
        20,
      ),
      paginate: true,
      pageSize: 20,
    })
    this.tipoMotivoRescisao.load()

    this.tipoIndiceCorrecao = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'licitacao/indice_correcao',
        20,
      ),
      paginate: true,
      pageSize: 20,
    })
    this.tipoIndiceCorrecao.load()

    this.typeService
      .getCustom('licitacao/contrato_aditivo/mes_reajuste')
      .pipe(take(1))
      .subscribe(res => {
        this.mesReajuste = new DataSource({
          store: res.dados,
        })
      })
  }

  additionTypeChange(uuid: string) {
    const type = this.additionTypes.find(type => type.uuid === uuid)
    this.additionTypeSelected = type
    this.model.get('tipoAditivoClassificacao').setValue(uuid)

    if (type.codigo == 4) {
      this.valueIsRequired = true
      this.deadlineIsRequired = false
    } else if (type.codigo == 1) {
      this.deadlineIsRequired = true
      this.valueIsRequired = false
    } else {
      this.deadlineIsRequired = false
      this.valueIsRequired = false
    }
  }

  actTypeChange(uuid: string) {
    this.actTypeSelected = this.tipoAto
      .items()
      .findIndex(type => type.uuid === uuid)

    if (this.actTypeSelected == 0) {
      this.additionTypeIsRequired = true
    } else {
      this.additionTypeIsRequired = false
      this.valueIsRequired = false
      this.deadlineIsRequired = false
      this.model.get('tipoAditivoContrato')?.reset(null, {emitEvent: false});
    }
  }

  prepare(formData: any) {
    const dto = {
      ...formData,

      flagApostilamento: formData?.flagApostilamento ? 'S' : 'N',

      flagCovid19: formData?.flagCovid19 ? 'S' : 'N',

      tipoAditivoAto: { uuid: formData?.tipoAditivoAto },

      tipoAditivoContrato: { uuid: formData?.tipoAditivoContrato },

      tipoAditivoContratoAnulado: {
        uuid: formData?.tipoAditivoContratoAnulado,
      },

      tipoAditivoContratoDois: { uuid: formData?.tipoAditivoContratoDois },

      tipoAditivoContratoUm: { uuid: formData?.tipoAditivoContratoUm },

      tipoMotivoRescisaoContratoTce: {
        uuid: formData?.tipoMotivoRescisaoContratoTce,
      },

      tipoOperacaoAditivoContratoTceDois: {
        uuid: formData?.tipoOperacaoAditivoContratoTceDois,
      },

      tipoOperacaoAditivoContratoTceUm: {
        uuid: formData?.tipoOperacaoAditivoContratoTceUm,
      },

      tipoRedimensionamentoContratoTce: {
        uuid: formData?.tipoRedimensionamentoContratoTce,
      },

      indiceCorrecao: { uuid: formData?.indiceCorrecao },

      fornecedor: { uuid: formData?.fornecedor?.uuid },

      pessoaRepresentante: { uuid: formData?.pessoaRepresentante?.uuid },
    }

    return dto
  }

  confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue())
      let req: Observable<any>

      if (!this.uuid) {
        const url = `licitacao/contrato/${this.parentUuid}/aditivo`
        req = this.simpleRequestService.post(url, dto)
      } else {
        const url = `licitacao/contrato/${this.parentUuid}/aditivo/${this.uuid}`
        req = this.simpleRequestService.put(url, dto)
      }

      this.loading = true

      req
        .pipe(take(1), finalize(() => (this.loading = false)))
        .subscribe({next: res => {
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: `Ato contratual ${
              !this.uuid ? 'cadastrado' : 'atualizado'
            } com sucesso!`,
          })
          console.log(res)
          this.uuid = res.dados.uuid
          this.ngOnInit()
        },})
    }
  }

  dispose() {
    this.dialogRef.close(!!this.uuid)
  }

  getTotal() {
    const {
      valorProrrogacao,
      valorAcrescimo,
      valorReajuste,
      valorRecomposicao,
      valorSupressao,
    } = this.model.getRawValue()
    return (
      Number(valorProrrogacao) +
      Number(valorAcrescimo) +
      Number(valorReajuste) +
      Number(valorRecomposicao) -
      Number(valorSupressao)
    )
  }

  loadedItems(event: any) {
    this.itemsLoaded = true
    this.model.markAsDirty()
  }

  validDates() {
    const dataInclusaoContrato = new Date(this.dataInclusaoContrato).getTime()

    const model = this.model.getRawValue()

    let validDates = true

    Object.keys(model)
      .filter(key => key.includes('data'))
      .forEach(key => {
        if (model[key]) {
          const date = new Date(model[key]).getTime()
          if (date < dataInclusaoContrato) validDates = false
        }
      })

    return validDates
  }

  ngOnDestroy() {
    this.destroy$.next()
    this.destroy$.complete()
  }
}
