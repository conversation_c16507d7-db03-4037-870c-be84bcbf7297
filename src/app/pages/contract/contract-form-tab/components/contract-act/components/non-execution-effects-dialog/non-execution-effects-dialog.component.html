<eqp-nebular-dialog
  id="inexecution-effect-dialog"
  [dialogTitle]="'Efeito de Inexecução'"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [dialogSize]="'extra-large'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'submit-inexecution-effect-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-inexecution-effect-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
  [rightFirstButtonDisabled]="model.invalid || model.pristine"
>
  <ng-container [formGroup]="model">
    <div class="row">
      <div class="col-2">
        <eqp-nebular-input
          [style]="'basic'"
          [type]="'number'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Código"
          placeholder="Código"
          formControlName="codigo"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-5">
        <eqp-nebular-select
          label="Parte originária"
          formControlName="tipoParteOriginariaInexecucao"
          displayExpr="nome"
          valueExpr="uuid"
          size="small"
          [dataSource]="originalPart"
          [required]="true"
        ></eqp-nebular-select>
        <!-- <div class="d-flex align-items-end">
          <eqp-nebular-search-input
            label="Parte originária*"
            formControlName="tipoParteOriginariaInexecucao"
            (onSearch)="onOriginalPartInput($event)"
            (onButtonClick)="onOriginalPartDialog()"
            [placeholder]="
              selectedOriginalPart?.codigo || 'Código da parte originária'
            "
            [required]="true"
          ></eqp-nebular-search-input>

          <p class="label ml-2 my-2">
            {{
              selectedOriginalPart?.nome || 'Parte originária não selecionada'
            }}
          </p>
        </div> -->
      </div>

      <div class="col-5">
        <eqp-nebular-select
          label="Efeito"
          formControlName="tipoEfeitoInexecucao"
          displayExpr="nome"
          valueExpr="uuid"
          [required]="true"
          [dataSource]="type"
          [size]="'small'"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="mt-3">
      <div class="row">
        <div class="col">
          <eqp-nebular-select
            formControlName="tipoPenalidadeInexecucao"
            label="Tipo"
            size="small"
            displayExpr="nome"
            valueExpr="uuid"
            [required]="true"
            [dataSource]="penalty"
          ></eqp-nebular-select>
        </div>
        <div class="col">
          <eqp-field-date
            formControlName="dataPenalidade"
            name="data"
            label="Data"
            [required]="true"
          ></eqp-field-date>
        </div>
        <div class="col">
          <eqp-field-date
            formControlName="dataPublicacaoPenalidade"
            name="dataPublicacao"
            label="Data publicação"
            [required]="true"
          ></eqp-field-date>
        </div>
        <div class="col">
          <eqp-nebular-input
            formControlName="valorPenalidade"
            name="valor"
            label="Valor"
            placeholder="00,00"
            [required]="true"
            [size]="'small'"
            [shape]="'rectangle'"
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          >
          </eqp-nebular-input>
        </div>
      </div>
      <div>
        <eqp-nebular-input
          [style]="'textArea'"
          [size]="'small'"
          [shape]="'rectangle'"
          [required]="true"
          formControlName="motivoPenalidade"
          name="motivacao"
          label="Motivação"
          placeholder="Motivação"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </ng-container>
</eqp-nebular-dialog>
