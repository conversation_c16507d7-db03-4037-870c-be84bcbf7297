<eqp-nebular-dialog
  id="product-form-dialog"
  [dialogTitle]="dialogTitle"
  [spinnerStatus]="'info'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-contract-responsible-form-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="!model.valid"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-contract-responsible-form-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  dialogSize="medium"
  (bottomLeftButtonEmitter)="dispose()"
>
  <ng-container [formGroup]="model">
    <eqp-loading *ngIf="loading"></eqp-loading>
    <div class="row">
      <div class="col col-12" formGroupName="orgaoOficial">
        <eqp-nebular-select
          size="small"
          shape="rectangle"
          label="Órgão Oficial"
          formControlName="uuid"
          valueExpr="uuid"
          searchMode="contains"
          [required]="true"
          [dataSource]="officialOrgans"
          [displayExpr]="responsibleTypeDisplay"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col col-6">
        <eqp-field-date
          formControlName="dataPublicacao"
          name="dataPublicacao"
          label="Data Publicação"
          [required]="true"
        ></eqp-field-date>
      </div>
      <div class="col col-6">
        <eqp-field-date
          formControlName="dataInclusaoTce"
          name="dataInclusaoTce"
          label="Data Inclusão TCE"
          [required]="true"
        ></eqp-field-date>
      </div>
    </div>
  </ng-container>
</eqp-nebular-dialog>
