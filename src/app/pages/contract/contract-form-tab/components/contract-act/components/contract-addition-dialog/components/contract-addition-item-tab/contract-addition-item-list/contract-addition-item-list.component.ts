import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { NbDialogService } from '@nebular/theme'
import { ContractItemDialogComponent } from '@pages/contract/contract-form-tab/components/contract-items/contract-item-dialog/contract-item-dialog.component'
import { ContractAdditionService } from '@pages/contract/services/contract-addition.service'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import DataSource from 'devextreme/data/data_source'
import { filter, finalize, take, takeUntil } from 'rxjs/operators'
import { ContractAdditionItemDialogComponent } from '../contract-addition-item-dialog/contract-addition-item-dialog.component'
import { ContractAdditionItemIncreaseComponent } from '../contract-addition-item-increase/contract-addition-item-increase.component'
import { ContractFilteredItemsComponent } from '@pages/contract/contract-form-tab/components/contract-items/contract-filtered-items/contract-filtered-items.component'
import { ContractAdditionItemLoadComponent } from '../contract-addition-item-load/contract-addition-item-load.component'
import { DialogEditItemComponent } from '../dialog-edit-item/dialog-edit-item.component'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { Observable } from 'rxjs'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { ActivatedRoute } from '@angular/router'
import { SimpleRequestService } from '@common/services/simple-request.service'

@Component({
  selector: 'eqp-contract-addition-item-list',
  templateUrl: './contract-addition-item-list.component.html',
  styleUrls: ['./contract-addition-item-list.component.scss'],
})
export class ContractAdditionItemListComponent implements OnChanges {
  @Input() parentUuid: string
  @Input() uuid: string
  @Output() loadedItems: EventEmitter<any> = new EventEmitter();
  disabledManualItems: boolean = true
  @Input() teste: any

  dataSource: DataSource
  pageTitle: string = 'Itens'
  loading: boolean = false
  buttonDisabled: boolean = false
  contractUuid: string

  currencyFormat = currencyFormat

  constructor(
    private crudService: CrudService,
    private dialogService: NbDialogService,
    private contractAdditionService: ContractAdditionService,
    private simpleRequestService: SimpleRequestService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
  ) {}

  ngOnChanges(): void {
    const { contractUuid } = this.route.snapshot.params
    this.contractUuid = contractUuid
    if (this.uuid) this.fetchGrid()
  }

  deleteByBatch() {}

  openLicitationItemDialog() {}

  fetchGrid() {
    const url = `licitacao/contrato_aditivo/${this.uuid}/item_aditivo`
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro('uuid', url, 10),
      onLoadingChanged: (isLoading) => this.loading = isLoading,
      paginate: true,
      pageSize: 10,
    })
  }

  loadItems() {
    const dialogRef = this.dialogService.open(
      ContractAdditionItemLoadComponent,
      {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.componentRef.instance.uuid = this.uuid
    dialogRef.componentRef.instance.parentUuid = this.parentUuid
    dialogRef.onClose.pipe(filter(res => res)).subscribe(_ => {
      this.loadedItems.emit(true);
      this.fetchGrid()
    })
  }

  increaseItems(event?: any) {
    const dialogRef = this.dialogService.open(
      ContractAdditionItemIncreaseComponent,
      {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    // dialogRef.componentRef.instance.uuid = this.uuid
    dialogRef.componentRef.instance.parentUuid = this.parentUuid
    dialogRef.onClose.pipe(filter(res => res)).subscribe(_ => {
      this.buttonDisabled = false
    })
  }

  edit(uuid?: string) {
    const dialogRef = this.dialogService.open(DialogEditItemComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.componentRef.instance.uuid = uuid
    dialogRef.componentRef.instance.parentUuid = this.uuid
    dialogRef.componentRef.instance.itemUuid = uuid
    dialogRef.onClose.pipe(filter(res => res)).subscribe(_ => {
      this.loadedItems.emit(true);
      this.fetchGrid()
    })
  }

  public remove(data: any): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.subscribe(res => {
      if (res === 'S') {
        let req: Observable<any>
        req = this.contractAdditionService.deleteItem(
          this.parentUuid,
          data.value,
        )
        this.loading = true
        req.pipe(finalize(() => (this.loading = false))).subscribe(
          () => {
            this.toastr.send({
              success: true,
              message: `Item removido com sucesso.`,
            })
            this.fetchGrid()
            this.loadedItems.emit(true);
          },
          (resp: any) => this.toastr.bulkSend(resp.mensagens),
        )
      }
    })
  }

  combineSolicitacao(data: any) {
    return `${data.solicitacao.numero} / ${data.solicitacao.exercicio.exercicio} - ${data.solicitacao.exercicio.entidade.codigo}`
  }
}
