<eqp-nebular-dialog
  id="product-form-dialog"
  [dialogTitle]="dialogTitle"
  [spinnerStatus]="'info'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-contract-responsible-form-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="model.invalid || model.pristine"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-contract-responsible-form-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
  [dialogSize]="'large'"
>
  <ng-container [formGroup]="model">
    <eqp-loading *ngIf="loading"></eqp-loading>
    <div class="row">
      <div class="col col-1">
        <eqp-nebular-input
          label="Código"
          placeholder=""
          formControlName="codigo"
          [style]="'basic'"
          [type]="'number'"
          [size]="'small'"
          [shape]="'rectangle'"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-2">
        <eqp-nebular-select
          size="small"
          shape="rectangle"
          label="Tipo"
          formControlName="tipoResponsabilidadeAdministrativa"
          valueExpr="uuid"
          displayExpr="nome"
          [required]="true"
          [dataSource]="types"
        ></eqp-nebular-select>
      </div>
      <div class="col col-3">
        <eqp-field-date
          formControlName="data"
          name="data"
          label="Data"
          [required]="true"
        ></eqp-field-date>
      </div>
      <div class="col col-3">
        <eqp-field-date
          formControlName="dataInicial"
          name="dataInicial"
          label="Data Inicial"
          [required]="true"
        ></eqp-field-date>
      </div>
      <div class="col col-3">
        <eqp-field-date
          formControlName="dataFinal"
          name="dataFinal"
          label="Data Final"
          [required]="true"
        ></eqp-field-date>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col col-7">
        <eqp-nebular-input
          label="Ato de Designação"
          placeholder=""
          formControlName="atoDesignacao"
          [required]="true"
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
        >
        </eqp-nebular-input>
      </div>

      <div class="col col-5">
        <eqp-search-field
          label="Responsável"
          formControlName="pessoaResponsavel"
          dialogTitle="Responsável"
          searchColumnsType="unityPlanColumns"
          [uri]="'licitacao/contrato/pessoa'"
          [returnAllData]="true"
          [required]="true"
        ></eqp-search-field>
      </div>
    </div>
  </ng-container>
</eqp-nebular-dialog>
