<eqp-nebular-dialog
  id="contract-addition-item-increase-form-dialog"
  [dialogTitle]="dialogTitle"
  [dialogSize]="'extra-large'"
  [rightFirstButtonText]="'Executar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-check'"
  [rightFirstButtonId]="
    'confirm-contract-addition-item-increase-form-dialog-button'
  "
  [rightFirstButtonTitle]="'Executar'"
  [rightFirstButtonDisabled]="invalidUpdate"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="
    'cancel-contract-addition-item-increase-form-dialog-button'
  "
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>
  <ng-container [formGroup]="model">
    <div class="p-3">
      <eqp-loading *ngIf="loading"></eqp-loading>
      <div class="row justify-content-end">
        <div
          class="col-sm-4 col-lg-2 justify-content-end d-flex"
          style="gap: 0.3rem"
        >
          <eqp-nebular-input
            class="text-right"
            [style]="'basic'"
            [type]="'number'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="porcentagem"
            name="porcentagem"
            placeholder="%"
          >
          </eqp-nebular-input>
          <eqp-nebular-button
            [buttonDisabled]="false"
            buttonText="Adicionar"
            [buttonIconVisible]="true"
            buttonIcon="fas fa-plus"
            buttonTitle="Gerar adicionais de itens"
            [buttonVisible]="true"
            [buttonDisabled]="!porcentagem"
            (buttonEmitter)="addPercentage(porcentagem)"
          ></eqp-nebular-button>
        </div>
      </div>
      <dx-data-grid
        id="contract-addition-item-increase-grid"
        [dataSource]="itens"
        [allowColumnResizing]="true"
        [columnAutoWidth]="true"
        [showColumnLines]="false"
        [showRowLines]="false"
        [showBorders]="false"
        [rowAlternationEnabled]="true"
        [wordWrapEnabled]="true"
        [loadPanel]="false"
        [columnHidingEnabled]="false"
        (onRowUpdated)="onRowUpdated()"
        [remoteOperations]="true"
        keyExpr="uuid"
        class="mt-2 p-0"
      >
        <dxo-paging [pageSize]="10"></dxo-paging>
        <dxo-pager
          [showInfo]="true"
          [showNavigationButtons]="true"
          [showPageSizeSelector]="false"
        >
        </dxo-pager>

        <dxo-header-filter [visible]="false"></dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>

        <dxo-sorting mode="multiple"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

        <dxo-group-panel
          [visible]="false"
          [emptyPanelText]="''"
        ></dxo-group-panel>

        <dxo-editing
          mode="cell"
          [allowUpdating]="true"
          [allowDeleting]="false"
          [allowAdding]="false"
          [useIcons]="true"
        >
        </dxo-editing>

        <dxi-column
          dataField="codigo"
          [allowEditing]="false"
          [allowSorting]="false"
          caption="Item"
        ></dxi-column>

        <dxi-column
          dataField="solicitacao.codigo"
          [allowEditing]="false"
          caption="Solicitacao"
        ></dxi-column>

        <dxi-column
          dataField="produto.codigo"
          dataType="number"
          [allowEditing]="false"
          [allowSorting]="false"
          caption="Código"
        >
        </dxi-column>

        <dxi-column
          dataField="produto.nome"
          [allowEditing]="false"
          [allowSorting]="false"
          caption="Produto"
        ></dxi-column>

        <dxi-column
          dataField="quantidade"
          [dataType]="'number'"
          [allowEditing]="false"
          [allowSorting]="false"
          caption="Qtde. original"
        ></dxi-column>

        <dxi-column
          dataField="valorUnitario"
          [format]="currencyFormat"
          [allowEditing]="false"
          [allowSorting]="false"
          caption="Preço unitário"
        ></dxi-column>

        <dxi-column
          caption="Total"
          dataField="total"
          [dataType]="'number'"
          [allowEditing]="false"
          [allowSorting]="false"
          [format]="currencyFormat"
          [calculateDisplayValue]="calculateTotal"
        >
        </dxi-column>

        <dxi-column
          dataField="quantidadeRequisitada"
          [dataType]="'number'"
          [allowEditing]="false"
          caption="Qtde. atual"
          [format]="decimalFormat"
          [allowSorting]="false"
        ></dxi-column>

        <dxi-column
          cssClass="cell-highlighted"
          dataField="quantidadeAdicionar"
          [dataType]="'number'"
          [allowEditing]="true"
          caption="Qtde. a adicionar"
          dataType="number"
          [format]="decimalFormat"
          [allowSorting]="false"
        >
          <dxi-validation-rule
            [ignoreEmptyValue]="true"
            type="custom"
            [validationCallback]="validateNumber"
            [message]="
              'Quantidade a adicionar é maior que a quantidade requisitada no ato'
            "
          ></dxi-validation-rule>
        </dxi-column>

        <dxi-column
          dataField="quantidadeTotal"
          [dataType]="'number'"
          [allowEditing]="false"
          caption="Qtde. total"
          [calculateDisplayValue]="calculateTotalAmount"
          [allowSorting]="false"
        ></dxi-column>

        <dxi-column
          caption="Valor atual"
          dataField="valorTotal"
          [allowEditing]="false"
          [format]="currencyFormat"
        >
        </dxi-column
        >>

        <dxi-column
          caption="Valor a adicionar"
          dataField="valorAdicionar"
          [allowEditing]="false"
          [format]="currencyFormat"
          [calculateDisplayValue]="calculateAddition"
        >
        </dxi-column>

        <dxi-column
          caption="Preço total"
          dataField="preco"
          [allowEditing]="false"
          [format]="currencyFormat"
          [calculateDisplayValue]="calculateTotalPrice"
        >
        </dxi-column>

        <dxi-column
          caption="Fornecedor"
          dataField="fornecedor.pessoaNome"
          [allowEditing]="false"
        >
        </dxi-column>
      </dx-data-grid>
    </div>
  </ng-container>
</eqp-nebular-dialog>
