import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import { ContractAttachmentService } from '@pages/contract/services/contract-attachment.service';
import { Observable, ReplaySubject } from 'rxjs';
import { finalize, take } from 'rxjs/operators';

@Component({
  selector: 'eqp-contract-attachment-form',
  templateUrl: './contract-attachment-form.component.html',
  styleUrls: ['./contract-attachment-form.component.scss']
})
export class ContractAttachmentFormComponent implements OnInit {

  constructor(
    private toastr: ToastrService,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ContractAttachmentFormComponent>,
    private service: ContractAttachmentService
  ) { }

  @Input() contractingUuid: string;
  @Input() uuid: string;

  public model: FormGroup;
  files64Upload: any[] = [];
  loading = false;
  fileToUpload: File = null;
  arquivosSelecionados: FileList | null = null;
  
  ngOnInit(): void {
    this.model = this.getNewModel();
    if(this.uuid) this.loadForm();
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      nome: [],
      ordem: [],
      publicaInternet: ['N'],
      conteudo: [],
      descricao: [],
      nomeArquivo: [],
      arquivo: [],
      link: [],
      nomeInformado: []
    })
  }

  loadForm() {
    this.loading = true
    this.service.getIndividual(this.contractingUuid, this.uuid)
    .pipe(take(1), finalize(() => this.loading = false))
    .subscribe(res => {
      const dto = {
        ...res.dados,
        link: res.dados?.nomeArquivoOriginal ? '' : res.dados.link,
        nome: res.dados?.nomeArquivoOriginal,
        descricao: res.dados?.descricao,
        publicaInternet: res.dados?.publicaInternet == "YES" ? 'S' : 'N',
        arquivo: res.dados?.nomeArquivoOriginal,
        nomeInformado: res.dados?.nomeInformado
      }
      this.model.patchValue(dto)
    })
  }

  handleFileInput(files: FileList) {
    this.fileToUpload = files.item(0);
  }

  selecionarArquivo() {
    const inputElement = document.createElement('input');
    inputElement.type = 'file';

    inputElement.addEventListener('change', (event) => {
      this.arquivosSelecionados = (event.target as HTMLInputElement).files;
      this.convertFile(this.arquivosSelecionados[0]).subscribe(res => this.setNewAttachment(res))
    });

    inputElement.click();
  }

  setNewAttachment(data: any){
    if(this.files64Upload.length > 0) this.files64Upload.shift();
    this.files64Upload.push({
      conteudo: data,
    })
    this.model.get('arquivo').patchValue(this.arquivosSelecionados[0].name);
    this.model.markAsDirty();
  }

  convertFile(file: File): Observable<string> {
    const result = new ReplaySubject<string>(1)
    const reader = new FileReader()
    reader.readAsBinaryString(file)
    reader.onload = event => {
      const binaryString = btoa(event.target.result.toString())
      const data = `data:${file.type};base64,${binaryString}`
      result.next(data)
    }
    return result
  }

  private prepare(formData: any) {
    const dto = {
      ...formData,
      conteudo: null,
      ...this.files64Upload[0],
      nome: this.model.get('arquivo')?.value,
      publicaInternet: formData.publicaInternet == 'S' ? 'YES' : 'NO'
    }
    delete dto.arquivo
    return dto
  }

  dispose() {
    this.dialogRef.close(null)
  }

  private _validateConfirm() {
    const { arquivo, link } = this.model.getRawValue()

    if (!arquivo && !link) {
      this.toastr.send({
        warning: true,
        title: 'Atenção',
        message:
          'É necessário preencher um dos seguintes campos: "Link" ou "Arquivo", por favor verifique e tente novamente.',
      });
      return false
    }

    if (arquivo && link) {
      this.toastr.send({
        warning: true,
        title: 'Atenção',
        message:
          'É permitido informar somente um dos seguintes campos: "Link" ou "Arquivo".',
      });
      return false
    }

    return true
  }

  confirm() {
    if (!this._validateConfirm()) {
      return
    }
    const uuid = this.model.get('uuid')?.value
    if (this.model.valid) {
      this.loading = true
      const dto = this.prepare(this.model.getRawValue())
      let req: Observable<any>
      if (!uuid) {
        req = this.service.post(this.contractingUuid, dto)
      } else {
        req = this.service.put(this.contractingUuid, dto, uuid)
      }
      req.pipe(
        take(1),
        finalize(() => (this.loading = false)),
      ).subscribe(res => this.confirmRegister(res))
    }
  }

  confirmRegister(response: any){
    const uuid = this.model.get('uuid')?.value

    this.toastr.send({
      success: true,
      title: 'Sucesso',
      message: `Anexo ${
        !uuid ? 'cadastrado' : 'atualizado'
      } com sucesso!`,
    })
    this.dialogRef.close(true)
  }
}
