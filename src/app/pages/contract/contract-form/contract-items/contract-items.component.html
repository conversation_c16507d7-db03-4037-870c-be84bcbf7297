<nb-tabset>
  <nb-tab tabTitle="Itens">
    <div class="text-right">
      <eqp-nebular-button
        [buttonDisabled]="!licitation"
        buttonText="Selecionar itens da licitação"
        buttonTitle="Selecionar itens da licitação"
        (buttonEmitter)="selectLicitationItems()"
        [buttonVisible]="true"

      ></eqp-nebular-button>
    </div>
    <eqp-grid
      gridId="contract-items-grid"
      gridName="Itens"
      gridSingularName="Item"
      [columnsTemplate]="columnsTemplate"
      [gridData]="items || []"
      [allowAdding]="!licitation"
      [allowUpdating]="!licitation"
      gridEditMode="row"
      (onRowPrepared)="onProductRowPrepared($event)"
    >
    </eqp-grid>
  </nb-tab>
  <nb-tab tabTitle="Dotações" class="p-0">
    <nb-tabset>
      <nb-tab tabTitle="Exercício atual">
        <eqp-grid
          gridId="contract-item-actual-dotations-grid"
          gridName="Dotações"
          gridSingularName="Dotação"
          [columnsTemplate]="dotationColumnsTemplate"
          [gridData]="dotations || []"
          [allowAdding]="false"
          [allowUpdating]="false"
          [allowDeleting]="false"
        >
        </eqp-grid>
      </nb-tab>
      <nb-tab tabTitle="Outros exercícios">
        <eqp-grid
          gridId="contract-item-other-dotations-grid"
          gridName="Dotações"
          gridSingularName="Dotação"
          [columnsTemplate]="otherDotationColumnsTemplate"
          [gridData]="dotations || []"
          [allowAdding]="false"
          [allowUpdating]="false"
          [allowDeleting]="false"
        >
        </eqp-grid>
      </nb-tab>
    </nb-tabset>
  </nb-tab>
</nb-tabset>
