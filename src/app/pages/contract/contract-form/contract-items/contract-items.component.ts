import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogService } from '@nebular/theme'
import { DotationInterface } from '@pages/contract/interfaces/dotation'
import { ProductItemInterface } from '@pages/shared/interfaces/product-item'
import { ProductFormDialogComponent } from '@pages/shared/product-form-dialog/product-form-dialog.component'
import { finalize, take } from 'rxjs/operators'
import { ContractingInterface } from './../../interfaces/contracting'
import { ContractingService } from './../../services/contracting.service'
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component'

@Component({
  selector: 'eqp-contract-items',
  templateUrl: './contract-items.component.html',
  styleUrls: ['./contract-items.component.scss'],
})
export class ContractItemsComponent implements OnInit {
  loading = false
  @Input() items: ProductItemInterface[] = []
  @Input() dotations: DotationInterface[] = []
  @Input() licitation: ContractingInterface

  @Output() onUpdateItems = new EventEmitter<ProductItemInterface[]>()

  licitationItems: ProductItemInterface[] = []
  otherDotations: DotationInterface[] = []

  columnsTemplate: DxColumnInterface[] = []
  dotationColumnsTemplate: DxColumnInterface[] = []
  otherDotationColumnsTemplate: DxColumnInterface[] = []

  constructor(
    private dialogService: NbDialogService,
    private toastr: ToastrService,
    private contractingService: ContractingService,
  ) {}

  ngOnInit(): void {
    this.columnsTemplate = this.getColumnsTemplate()
    this.dotationColumnsTemplate = this.getDotationColumnsTemplate()
    this.otherDotationColumnsTemplate = this.getOtherDotationColumnsTemplate()
  }

  getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'produto.codigo',
        formItem: {
          isRequired: true,
        },
      },
      {
        caption: 'Nome',
        dataField: 'produto.nome',
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Lote',
        dataField: 'lote',
        dataType: 'number',
        formItem: {
          isRequired: true,
        },
      },
      {
        caption: 'Quantidade',
        dataField: 'quantidade',
        dataType: 'number',
        formItem: {
          isRequired: true,
        },
      },
      {
        caption: 'Preço min.',
        dataField: 'precoMinimo',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Preço max.',
        dataField: 'precoMaximo',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Total',
        dataField: 'total',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
    ]
    return template
  }

  getDotationColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'contaDespesa.codigo',
      },
      {
        caption: 'Grupo fonte',
        dataField: 'grupoFonte',
      },
      {
        caption: 'Grupo fonte',
        dataField: 'contaDespesa.fonteRecurso',
      },
      {
        caption: 'Conta despesa',
        dataField: 'contaDespesa.nome',
      },
      {
        caption: 'Quantidade',
        dataField: 'quantidade',
        dataType: 'number',
      },
      {
        caption: 'Preço un.',
        dataField: 'precoUnitario',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Total',
        dataField: 'total',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
    ]
    return template
  }

  getOtherDotationColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Exercício',
        dataField: 'exercicio',
      },
      {
        caption: 'Código',
        dataField: 'contaDespesa.codigo',
      },
      {
        caption: 'Grupo fonte',
        dataField: 'grupoFonte',
      },
      {
        caption: 'Grupo fonte',
        dataField: 'contaDespesa.fonteRecurso',
      },
      {
        caption: 'Conta despesa',
        dataField: 'contaDespesa.nome',
      },
      {
        caption: 'Quantidade',
        dataField: 'quantidade',
        dataType: 'number',
      },
      {
        caption: 'Preço un.',
        dataField: 'precoUnitario',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Total',
        dataField: 'total',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
    ]
    return template
  }

  onProductRowPrepared(event?: any) {
    if (event.isEditing || event.isNewRow) {
      const dialogRef = this.dialogService.open(ProductFormDialogComponent, {
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      dialogRef.componentRef.instance.dialogTitle = 'Inserir item'
      if (!event.isNewRow)
      dialogRef.onClose.subscribe(res => {
        if (res) {
          event.data.total = Number(res.precoMaximo) * Number(res.quantidade)
          Object.keys(res).forEach(key => (event.data[key] = res[key]))
          event.component.saveEditData()
        } else {
          event.component.cancelEditData()
        }
      })
    }
  }

  selectLicitationItems() {
    if (!this.licitation?.uuid) {
      this.toastr.send({
        warning: true,
        title: 'Alterta',
        message: 'Não existe licitação vinculada',
      })
    } else if (!this.licitationItems || this.licitationItems.length == 0) {
      this.loading = true
      this.contractingService
        .getIndividual(this.licitation.uuid)
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(
          res => {
            this.licitationItems = res.dados.processoAdministrativo.itens
            this.openLicitationItemDialog()
          },
          error => {
            this.loading = false
            this.toastr.send({
              error: true,
              title: 'Error',
              message: error.mensagens
                ? error.mensagens
                : 'Falha ao carregar itens da licitação',
            })
          },
        )
    } else {
      this.openLicitationItemDialog()
    }
  }

  private openLicitationItemDialog() {
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar itens'
    dialogRef.componentRef.instance.columnsTemplate = this.getColumnsTemplate()
    dialogRef.componentRef.instance.multiple = true
    dialogRef.componentRef.instance.dataGrid = this.licitationItems
    dialogRef.onClose.subscribe((res: ProductItemInterface[]) => {
      if (res) {
        this.updateLocalItems(res)
      }
    })
  }

  private updateLocalItems(res: ProductItemInterface[]) {
    const productMap = new Map<string, ProductItemInterface>()
    res.forEach(item => productMap.set(item.uuid, item))
    this.items.forEach(item => productMap.set(item.uuid, item))
    const newItems = Array.from(productMap.values()).sort(item => item.ordem)
    this.onUpdateItems.emit(newItems)
  }
}
