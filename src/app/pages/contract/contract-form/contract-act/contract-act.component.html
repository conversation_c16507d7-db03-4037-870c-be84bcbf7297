<nb-tabset (changeTab)="onChangeTab($event)">
  <nb-tab tabTitle="Atos" class="p-0">
    <div style="position: relative">
      <eqp-loading *ngIf="loading"></eqp-loading>
      <eqp-grid
        gridId="contract-act-grid"
        gridName="Atos contratuais"
        gridSingularName="Ato contratual"
        [columnsTemplate]="actColumnsTemplate"
        [gridData]="acts"
        gridEditMode="row"
        (onRowPrepared)="onActRowPrepared($event)"
        (onRowRemoving)="onActRemoving($event)"
      >
      </eqp-grid>
    </div>
  </nb-tab>
  <nb-tab
    tabId="inexecution-effect"
    tabTitle="Efeito de inexecução"
    class="p-0"
  >
    <eqp-inexecution-effect
      *ngIf="associatedUuid && tabWasTouched('inexecution-effect')"
      [associatedUuid]="associatedUuid"
    ></eqp-inexecution-effect>
  </nb-tab>
  <nb-tab tabTitle="Responsabilidade administrativa" class="p-0">
    <eqp-contract-administrative-responsability
      [associatedUuid]="associatedUuid"
    >
    </eqp-contract-administrative-responsability>
  </nb-tab>
  <nb-tab
    tabId="regularity-certificate"
    tabTitle="Certificados de regularidade"
    class="p-0"
  >
    <eqp-contract-regularity-certificate
      *ngIf="associatedUuid && tabWasTouched('regularity-certificate')"
      [associatedUuid]="associatedUuid"
    >
      ></eqp-contract-regularity-certificate
    >
  </nb-tab>
  <nb-tab tabTitle="Publicação órgão oficial" class="p-0">
    <eqp-official-organ-publication
      [associatedUuid]="associatedUuid"
    ></eqp-official-organ-publication>
  </nb-tab>
</nb-tabset>
