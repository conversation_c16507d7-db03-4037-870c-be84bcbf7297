import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ContractRegularityCertificateComponent } from './contract-regularity-certificate.component';

describe('ContractRegularityCertificateComponent', () => {
  let component: ContractRegularityCertificateComponent;
  let fixture: ComponentFixture<ContractRegularityCertificateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ContractRegularityCertificateComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractRegularityCertificateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
