import { Component, Input, OnInit } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { RegularityCertificateInterface } from '@pages/contract/interfaces/regularity-certificate'
import { RegularityCertificateService } from '@pages/contract/services/regularity-certificate.service'
import { ContractPreLoadInterface } from '@pages/pre-load'
import { PreLoadService } from '@pages/pre-load.service'
import { finalize, take } from 'rxjs/operators'

@Component({
  selector: 'eqp-contract-regularity-certificate',
  templateUrl: './contract-regularity-certificate.component.html',
  styleUrls: ['./contract-regularity-certificate.component.scss'],
})
export class ContractRegularityCertificateComponent implements OnInit {
  loading = false
  columnsTemplate: DxColumnInterface[] = []

  @Input() associatedUuid

  private _contractPreload: ContractPreLoadInterface

  certificates: RegularityCertificateInterface[] = []

  constructor(
    private preLoadService: PreLoadService,
    private regularityCertificateService: RegularityCertificateService,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this._contractPreload = this.preLoadService.contract
    this.columnsTemplate = this.getColumnsTemplate()
    this.loading = true
    this.regularityCertificateService
      .get(this.associatedUuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          this.certificates = res.dados
        },
        err => {
          this.toastr.send({
            error: true,
            title: 'Erro',
            message:
              err.mensagem || 'Falha ao buscar certificados de regularidade',
          })
        },
      )
  }

  getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Documento',
        dataField: 'codigo',
      },
      {
        caption: 'Tipo',
        dataField: 'tipo',
        lookup: {
          // dataSource: this._contractPreload.tiposCertificadoRegularidade,
          dataSource: [],
          valueExpr: 'chave',
          displayExpr: 'valor',
        },
      },
      {
        caption: 'Data emissão',
        dataField: 'dataEmissao',
        dataType: 'date',
      },
      {
        caption: 'Data validade',
        dataField: 'dataValidade',
        dataType: 'date',
      },
      {
        caption: 'Data apresentação',
        dataField: 'dataApresentacao',
        dataType: 'date',
      },
      {
        caption: 'Número da certidão',
        dataField: 'numeroCertidao',
      },
    ]
    return template
  }
}
