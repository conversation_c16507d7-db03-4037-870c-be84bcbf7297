import { Component, Input, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { InexecutionEffectInterface } from '@pages/contract/interfaces/inexecution-effect'
import { PersonInterface } from '@pages/contract/interfaces/person'
import { PersonService } from '@pages/contract/services/person.service'
import { ContractPreLoadInterface } from '@pages/pre-load'
import { PreLoadService } from '@pages/pre-load.service'
import {
  allMomentToDate,
  enumsToSelectDto,
} from '@pages/shared/helpers/parsers.helper'
import { Observable } from 'rxjs'
import { finalize, pluck, take } from 'rxjs/operators'
import { InexecutionEffectService } from './../../../../services/inexecution-effect.service'
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto'
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component'

@Component({
  selector: 'eqp-inexecution-effect-dialog',
  templateUrl: './inexecution-effect-dialog.component.html',
  styleUrls: ['./inexecution-effect-dialog.component.scss'],
})
export class InexecutionEffectDialogComponent implements OnInit {
  loading = false
  model: FormGroup
  private _personsData: PersonInterface[] = []

  contractPreload: ContractPreLoadInterface
  tipoEfeitoTemplate: NebularSelectDto[] = []
  tipoPenalidadeTemplate: NebularSelectDto[] = []

  @Input() associatedUuid
  @Input() uuid

  constructor(
    private dialogRef: NbDialogRef<InexecutionEffectDialogComponent>,
    private personService: PersonService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    private preLoadService: PreLoadService,
    private builder: FormBuilder,
    private service: InexecutionEffectService,
  ) {}

  get parteOriginaria() {
    return this.model.get('parteOriginaria').value
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.contractPreload = this.preLoadService.contract
    this.tipoEfeitoTemplate = enumsToSelectDto(this.contractPreload.tiposEfeito)
    this.tipoPenalidadeTemplate = enumsToSelectDto(
      this.contractPreload.tiposPenalidade,
    )
    if (this.uuid) {
      this.fetchInitialData()
    }
  }

  fetchInitialData() {
    this.loading = true
    this.service
      .getIndividual(this.associatedUuid, this.uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          this.model.patchValue(res.dados)
        },
        error => {
          this.loading = false
          this.toastr.send({
            error: true,
            title: 'Error',
            message: error.mensagens
              ? error.mensagens
              : 'Falha ao carregar efeito',
          })
        },
      )
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      codigo: [],
      parteOriginaria: [undefined, [Validators.required]],
      tipo: [undefined, [Validators.required]],
      penalidade: this.builder.group({
        uuid: [],
        tipo: [],
        data: [],
        dataPublicacao: [],
        valor: [],
        motivacao: [],
      }),
    })
  }

  private prepare(formData: any): InexecutionEffectInterface {
    const dto = allMomentToDate(formData) as InexecutionEffectInterface
    return dto
  }

  dispose() {
    this.dialogRef.close()
  }

  async onOriginalPartInput(event?: any) {
    this.loading = true
    this._personsData = await this.personService.syncFetchData(
      this._personsData,
      this.toastr,
    )
    this.loading = false
    const index = this._personsData.findIndex(
      person => person.dadoPessoal.codigo == event,
    )
    if (index >= 0) {
      this.model.get('parteOriginaria').patchValue(this._personsData[index])
    } else {
      this.model.get('parteOriginaria').reset()
    }
  }

  async onOriginalPartDialog() {
    this.loading = true
    this._personsData = await this.personService.syncFetchData(
      this._personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar parte originária'
    dialogRef.componentRef.instance.dataGrid = this._personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('parteOriginaria').patchValue(res)
      }
    })
  }

  confirm() {
    if (this.model.valid) {
      let request: Observable<any>
      const dto = this.prepare(this.model.getRawValue())
      if (!this.uuid) {
        request = this.service.post(this.associatedUuid, dto)
      } else {
        request = this.service.put(this.associatedUuid, dto, this.uuid)
      }
      request
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
          pluck('body', 'dados'),
        )
        .subscribe(
          res => {
            this.toastr.send({
              success: true,
              title: 'Sucesso',
              message: `Efeito ${
                this.uuid ? 'atualizado' : 'cadastrado'
              }(a) com sucesso`,
            })
            this.dialogRef.close(res)
          },
          error => {
            this.toastr.send({
              error: true,
              title: 'Error',
              message:
                error.mensagens ||
                `Falha ao ${this.uuid ? 'atualizar' : 'cadastrar'} efeito`,
            })
          },
        )
    }
  }
}
