import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InexecutionEffectDialogComponent } from './inexecution-effect-dialog.component';

describe('InexecutionEffectDialogComponent', () => {
  let component: InexecutionEffectDialogComponent;
  let fixture: ComponentFixture<InexecutionEffectDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ InexecutionEffectDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InexecutionEffectDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
