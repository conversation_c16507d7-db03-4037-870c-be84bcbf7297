<eqp-nebular-dialog
  id="inexecution-effect-dialog"
  [dialogTitle]="'Efeito de Inexecução'"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [dialogSize]="'extra-large'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'submit-inexecution-effect-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="!model?.valid"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-inexecution-effect-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>
  <ng-container [formGroup]="model">
    <div class="row">
      <div class="col-5 col-md-1">
        <eqp-nebular-input
          [style]="'basic'"
          [type]="'number'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Código"
          placeholder="Código"
          formControlName="codigo"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col">
        <label class="label">Parte originária*</label>
        <div class="d-flex align-items-end">
          <eqp-nebular-search-input
            [placeholder]="
              parteOriginaria?.dadoPessoal?.codigo ||
              'Código da parte originária'
            "
            (onSearch)="onOriginalPartInput($event)"
            (onButtonClick)="onOriginalPartDialog()"
          ></eqp-nebular-search-input>
          <p class="label ml-2 my-2">
            {{
              parteOriginaria?.dadoPessoal?.nome ||
                'Parte originária não selecionada'
            }}
          </p>
        </div>
      </div>
      <div class="col">
        <eqp-nebular-select
          label="Tipo*"
          [size]="'small'"
          formControlName="tipo"
          [selectValues]="tipoEfeitoTemplate"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="mt-3" formGroupName="penalidade">
      <div class="row">
        <div class="col">
          <eqp-nebular-select
            label="Penalidade"
            [size]="'small'"
            formControlName="tipo"
            [selectValues]="tipoPenalidadeTemplate"
          ></eqp-nebular-select>
        </div>
        <div class="col">
          <eqp-field-date
            formControlName="data"
            name="data"
            label="Data"
          ></eqp-field-date>
        </div>
        <div class="col">
          <eqp-field-date
            formControlName="dataPublicacao"
            name="dataPublicacao"
            label="Data publicação"
          ></eqp-field-date>
        </div>
        <div class="col">
          <eqp-nebular-input
            [style]="'basic'"
            [type]="'number'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="valor"
            name="valor"
            label="Valor"
            placeholder="00,00"
          >
          </eqp-nebular-input>
        </div>
      </div>
      <div>
        <eqp-nebular-input
          [style]="'textArea'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="motivacao"
          name="motivacao"
          label="Motivação"
          placeholder="Motivação"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </ng-container>
</eqp-nebular-dialog>
