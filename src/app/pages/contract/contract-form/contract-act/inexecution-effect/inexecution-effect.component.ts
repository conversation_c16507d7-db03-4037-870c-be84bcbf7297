import { Component, Input, OnInit } from '@angular/core'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogService } from '@nebular/theme'
import { ContractPreLoadInterface } from '@pages/pre-load'
import { filter, finalize, take } from 'rxjs/operators'
import { PreLoadService } from './../../../../pre-load.service'
import { InexecutionEffectInterface } from './../../../interfaces/inexecution-effect'
import { InexecutionEffectService } from './../../../services/inexecution-effect.service'
import { InexecutionEffectDialogComponent } from './inexecution-effect-dialog/inexecution-effect-dialog.component'

@Component({
  selector: 'eqp-inexecution-effect',
  templateUrl: './inexecution-effect.component.html',
  styleUrls: ['./inexecution-effect.component.scss'],
})
export class InexecutionEffectComponent implements OnInit {
  loading = false
  columnsTemplate: DxColumnInterface[] = []
  private _contractPreload: ContractPreLoadInterface

  @Input() associatedUuid

  inexecutionEffects: InexecutionEffectInterface[] = []

  constructor(
    private preLoadService: PreLoadService,
    private dialogService: NbDialogService,
    private inexecutionEffectService: InexecutionEffectService,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this._contractPreload = this.preLoadService.contract
    this.columnsTemplate = this.getColumnsTemplate()
    this.loading = true
    this.inexecutionEffectService
      .get(this.associatedUuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.inexecutionEffects = res.dados
      })
  }

  getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'codigo',
      },
      {
        caption: 'Parte Originária',
        dataField: 'parteOriginaria.dadoPessoal.nome',
      },
      {
        caption: 'Efeito',
        dataField: 'tipo',
        lookup: {
          dataSource: this._contractPreload.tiposEfeito,
          // dataSource: [],
          valueExpr: 'chave',
          displayExpr: 'valor',
        },
      },
      {
        caption: 'Penalidade',
        dataField: 'penalidade.tipo',
        lookup: {
          dataSource: this._contractPreload.tiposPenalidade,
          // dataSource: [],
          valueExpr: 'chave',
          displayExpr: 'valor',
        },
      },
      {
        caption: 'Data',
        dataField: 'penalidade.data',
        dataType: 'date',
      },
      {
        caption: 'Publicação',
        dataField: 'penalidade.dataPublicacao',
        dataType: 'date',
      },
      {
        caption: 'Valor',
        dataField: 'penalidade.valor',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
      },
      {
        caption: 'Ações',
        type: 'buttons',
        dataField: 'uuid',
        buttons: [
          {
            name: 'edit',
            icon: 'fas fa-edit',
          },
          {
            name: 'delete',
            icon: 'fas fa-trash-alt',
          },
        ],
      },
    ]
    return template
  }

  onRowPrepared(event?: any) {
    if (event.isEditing || event.isNewRow) {
      const dialogRef = this.dialogService.open(
        InexecutionEffectDialogComponent,
        {
          closeOnBackdropClick: false,
          closeOnEsc: false,
        },
      )
      dialogRef.componentRef.instance.associatedUuid = this.associatedUuid
      if (!event.isNewRow) {
        dialogRef.componentRef.instance.uuid = event.data.uuid
      }
      dialogRef.onClose.subscribe(res => {
        if (res) {
          Object.keys(res).forEach(key => (event.data[key] = res[key]))
          event.component.saveEditData()
        } else {
          event.component.cancelEditData()
        }
      })
    }
  }

  onRowRemoving(event: any) {
    const { uuid } = event.data
    const dialogRef = this.dialogService.open(ConfirmationComponent)
    dialogRef.onClose.pipe(filter(req => req)).subscribe(_ => {
      this.inexecutionEffectService
        .delete(this.associatedUuid, uuid)
        .pipe(take(1))
        .subscribe(
          _ => {
            this.toastr.send({
              success: true,
              title: 'Sucesso',
              message: 'Efeito de inexecução removido(a) com sucesso!',
            })
          },
          err => {
            this.toastr.send({
              success: true,
              title: 'Erro',
              message: err.mensagem || 'Falha ao remover efeito de inexecução',
            })
          },
        )
    })
  }
}
