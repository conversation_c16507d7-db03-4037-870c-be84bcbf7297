import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InexecutionEffectComponent } from './inexecution-effect.component';

describe('InexecutionEffectComponent', () => {
  let component: InexecutionEffectComponent;
  let fixture: ComponentFixture<InexecutionEffectComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ InexecutionEffectComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InexecutionEffectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
