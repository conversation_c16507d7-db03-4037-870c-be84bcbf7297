import { Component, Input, OnInit } from '@angular/core';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { DxColumnInterface } from '@design-tools/interfaces/column-interface';
import { NbDialogService } from '@nebular/theme';
import { AdministrativeResponsabilityInterface } from '@pages/contract/interfaces/administrative-responsability';
import { AdministrativeResponsabilityService } from '@pages/contract/services/administrative-responsability.service';
import { ContractPreLoadInterface } from '@pages/pre-load';
import { PreLoadService } from '@pages/pre-load.service';
import { allMomentToDate, enumsToSelectDto } from '@pages/shared/helpers/parsers.helper';
import { Subscription } from 'rxjs';
import { finalize, take } from 'rxjs/operators';
import { AdministrativeResponsabilityDialogComponent } from './administrative-responsability-dialog/administrative-responsability-dialog.component';
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto';

@Component({
  selector: 'eqp-contract-administrative-responsability',
  templateUrl: './contract-administrative-responsability.component.html',
  styleUrls: ['./contract-administrative-responsability.component.scss']
})
export class ContractAdministrativeResponsabilityComponent implements OnInit {
  @Input() associatedUuid
  loading: boolean = false

  administrativeResponsabilityColumnsTemplate: DxColumnInterface[] = []

  gridData: AdministrativeResponsabilityInterface[] = []

  responsaveis?: NebularSelectDto[] = []

  tiposResponsabilidadeAdministrativa?: NebularSelectDto[] = []

  responsavelSbs: Subscription

  constructor(
    private preLoadService: PreLoadService,
    protected service: AdministrativeResponsabilityService,
    private dialogService: NbDialogService,
    private toastr: ToastrService
  ) { }

  ngOnInit(): void {
    this.fetchGrid()
    this.getAdministrativeResponsabilityType()
    this.administrativeResponsabilityColumnsTemplate = this.getAdministrativeResponsabilityColumnsTemplate()
  }

  prepare(data: Partial<AdministrativeResponsabilityInterface>): AdministrativeResponsabilityInterface {
    const formData = allMomentToDate(data)
    return formData as AdministrativeResponsabilityInterface
  }

  public fetchGrid(): void {
    this.loading = true
    this.service
      .get(this.associatedUuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        data => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  getAdministrativeResponsabilityType() {
    const contractPreLoad = this.preLoadService
      .contract as ContractPreLoadInterface
      this.tiposResponsabilidadeAdministrativa = enumsToSelectDto(
        contractPreLoad.tiposResponsabilidadeAdministrativa|| []
      )
  }

  getAdministrativeResponsabilityColumnsTemplate() {
    const template: DxColumnInterface[] = [
      {
        caption: 'Número',
        dataField: 'codigo',
        dataType: 'number'
      },
      {
        caption: 'Tipo',
        dataField: 'tipo',
        lookup: {
          dataSource: this.tiposResponsabilidadeAdministrativa,
          valueExpr: 'valor',
          displayExpr: 'texto'
        }
      },
      {
        caption: 'Data',
        dataField: 'data',
        dataType: 'date'
      },
      {
        caption: 'Data Inicial',
        dataField: 'dataInicial',
        dataType: 'date'
      },
      {
        caption: 'Data Final',
        dataField: 'dataFinal',
        dataType: 'date'
      },
      {
        caption: 'Ato de Designação',
        dataField: 'atoDesignacao'
      },
      {
        caption: 'Responsável',
        dataField: 'responsavel.dadoPessoal.nome'
      },
    ]
    return template
  }

  onAdministrativeResponsabilityRowPrepared(event?: any) {
    if (event.isEditing || event.isNewRow) {
      const dialogRef = this.dialogService.open(AdministrativeResponsabilityDialogComponent, {
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      dialogRef.componentRef.instance.dialogTitle = 'Inserir responsabilidade administrativa'
      if (!event.isNewRow)
      dialogRef.componentRef.instance.initialValue = event.data
      dialogRef.onClose.subscribe(res => {
        if (res) {
          const dto = this.prepare(res) as AdministrativeResponsabilityInterface
          if(!event.isNewRow) {
            this.service
            .put(this.associatedUuid, dto, dto.uuid)
            .pipe(take(1))
            .subscribe(
              res => {
                event.data = res
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Responsabilidade administrativa atualizada com sucesso!'
                })
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao atualizar responsabilidade administrativa!',
                })
              },
            )
          } else {
            this.service
            .post(this.associatedUuid, dto)
            .pipe(take(1))
            .subscribe(
              res => {
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Responsabilidade administrativa adicionada com sucesso!'
                })
                this.fetchGrid()
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao adicionar responsabilidade administrativa!',
                })
              },
            )
          }
          Object.keys(res).forEach(key => (event.data[key] = res[key]))
          event.component.saveEditData()
        } else {
          event.component.cancelEditData()
        }
      })
    }
  }


  public remove(event: any): void {
    this.loading = true
    this.service
      .delete(this.associatedUuid, event.data.uuid)
      .pipe(take(1))
      .subscribe({
        error: e => {
          this.toastr.send({
            error: true,
            title: 'Erro',
            message: e.message || 'Falha ao deletar a responsabilidade administrativa!',
          })
        },
        next: () => {
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: 'Responsabilidade administrativa deletada com sucesso!',
          })
        },
        complete: () => (this.loading = false),
      })
  }
}
