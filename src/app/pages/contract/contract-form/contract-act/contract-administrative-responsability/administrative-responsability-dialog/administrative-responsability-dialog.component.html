<eqp-nebular-dialog
  id="administrative-responsability-dialog"
  [dialogTitle]="dialogTitle"
  [spinnerStatus]="'info'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-administrative-responsability-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="!model.valid"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-administrative-responsability-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>

<ng-container [formGroup]="model">
  <div class="row">
    <div class="col-4 col-md-1">
      <eqp-nebular-input
          [style]="'basic'"
          [type]="'number'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Número"
          placeholder=""
          formControlName="codigo"
          [disabled]="true"
        >
        </eqp-nebular-input>
    </div>
    <div class="col-4 col-md-3">
      <eqp-nebular-select
           formControlName="tipo"
            label="Tipo"
            [size]="'small'"
            [selectValues]="tiposResponsabilidadeAdministrativa"
            ></eqp-nebular-select>
    </div>
    <div class="col">
      <eqp-nebular-input
      [style]="'date'"
      [size]="'small'"
      [shape]="'rectangle'"
      formControlName="data"
      name="data"
      label="Data"
      placeholder="00/00/0000"
    >
    </eqp-nebular-input>
    </div>
    <div class="col">
      <eqp-nebular-input
      [style]="'date'"
      [size]="'small'"
      [shape]="'rectangle'"
      formControlName="dataInicial"
      name="dataInicial"
      label="Data Inicial"
      placeholder="00/00/0000"
    >
    </eqp-nebular-input>
    </div>
  </div>

  <br>

  <div class="row">
    <div class="col">
      <eqp-nebular-input
      [style]="'date'"
      [size]="'small'"
      [shape]="'rectangle'"
      formControlName="dataFinal"
      name="dataFinal"
      label="Data Final"
      placeholder="00/00/0000"
    >
    </eqp-nebular-input>
    </div>
    <div class="col">
      <eqp-nebular-input
          [style]="'basic'"
          [type]="'text'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Ato de Designação"
          placeholder="Ato de Designação"
          formControlName="atoDesignacao"
        >
        </eqp-nebular-input>
    </div>
    <div class="col">
      <label class="label">Responsável</label>
       <eqp-nebular-search-input
       (onSearch)="onResponsibleSearchInput($event)"
       (onButtonClick)="onResponsibleSearchDialog()"
       [placeholder]="responsavel?.dadoPessoal.nome || 'Pessoa'"
       ></eqp-nebular-search-input>
     </div>
  </div>
</ng-container>
</eqp-nebular-dialog>
