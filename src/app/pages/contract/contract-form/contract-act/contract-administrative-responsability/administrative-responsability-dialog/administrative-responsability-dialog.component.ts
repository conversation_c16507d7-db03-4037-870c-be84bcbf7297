import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto';
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component';
import { NbDialogRef, NbDialogService } from '@nebular/theme';
import { AdministrativeResponsabilityInterface } from '@pages/contract/interfaces/administrative-responsability';
import { PersonInterface } from '@pages/contract/interfaces/person';
import { PersonService } from '@pages/contract/services/person.service';
import { ContractPreLoadInterface } from '@pages/pre-load';
import { PreLoadService } from '@pages/pre-load.service';
import { dateToMoment, enumsToSelectDto } from '@pages/shared/helpers/parsers.helper';

@Component({
  selector: 'eqp-administrative-responsability-dialog',
  templateUrl: './administrative-responsability-dialog.component.html',
  styleUrls: ['./administrative-responsability-dialog.component.scss']
})
export class AdministrativeResponsabilityDialogComponent implements OnInit {
  @Input() dialogTitle: string = 'Responsável'
  @Input() initialValue: AdministrativeResponsabilityInterface
  loading = false
  model: FormGroup
  personsData: PersonInterface[] = []
  person: PersonInterface

  administrativeResponsabilityMap: { [index: string]: AdministrativeResponsabilityInterface } = {}

  tiposResponsabilidadeAdministrativa: NebularSelectDto[] = []

  tiposResponsaveis: NebularSelectDto[] = []

  constructor(
    private dialogRef: NbDialogRef<AdministrativeResponsabilityDialogComponent>,
    private dialogService: NbDialogService,
    private builder: FormBuilder,
    private preLoadService: PreLoadService,
    private personService: PersonService,
    private toastr: ToastrService
  ) { }

  get responsavel() {
    return this.model.get('responsavel')?.value
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.fetchAdministrativeResponsabilityData()
    if(this.initialValue) {
      const dto = {
        ...this.initialValue,
      data: dateToMoment(this.initialValue.data),
      dataInicial: dateToMoment(this.initialValue.dataInicial),
      dataFinal: dateToMoment(this.initialValue.dataFinal)
      }
      this.model.patchValue(dto) }
  }

  confirm() {
    const dto = this.model.getRawValue()
    this.dialogRef.close(dto)
  }

  dispose() {
    this.dialogRef.close()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      codigo: [],
      tipo: [],
      data: [],
      dataInicial: [],
      dataFinal: [],
      atoDesignacao: [],
      responsavel: []
    })
  }

  fetchAdministrativeResponsabilityData() {
    const contractPreLoad = this.preLoadService
      .contract as ContractPreLoadInterface
      this.tiposResponsabilidadeAdministrativa = enumsToSelectDto(
        contractPreLoad.tiposResponsabilidadeAdministrativa || []
      )
  }

  async onResponsibleSearchInput(event: any) {
    const index = this.personsData.findIndex(person => person.dadoPessoal == event)
    if (index >= 0) {
      this.model.get('pessoaUuid').setValue(this.personsData[index].uuid)
      this.person = this.personsData[index]
    } else {
      this.model.get('pessoaUuid').reset()
      this.person = null
    }
  }

  async onResponsibleSearchDialog() {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar pessoa'
    dialogRef.componentRef.instance.dataGrid = this.personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
        visible: false
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('responsavel').patchValue(res)
        this.person = res
      }
    })
  }

}
