import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { ContractAdditionInterface } from '@pages/contract/interfaces/contract-addition'
import { ContractPreLoadInterface } from '@pages/pre-load'
import { Subscription } from 'rxjs'
import { filter, finalize, take } from 'rxjs/operators'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { PreLoadService } from './../../../pre-load.service'
import { ContractAdditionService } from './../../services/contract-addition.service'
import { ContractAdditionDialogComponent } from './contract-addition-dialog/contract-addition-dialog.component'

@Component({
  selector: 'eqp-contract-act',
  templateUrl: './contract-act.component.html',
  styleUrls: ['./contract-act.component.scss'],
})
export class ContractActComponent implements OnInit, OnDestroy {
  loading = false
  acts: ContractAdditionInterface[] = []
  subscription: Subscription
  contractPreLoad: ContractPreLoadInterface
  actColumnsTemplate: DxColumnInterface[] = []
  touchedTabs: Set<string> = new Set<string>([])

  @Input() associatedUuid: string

  constructor(
    private preLoadService: PreLoadService,
    private dialogService: NbDialogService,
    private contractActService: ContractAdditionService,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.contractPreLoad = this.preLoadService.contract
    this.actColumnsTemplate = this.getActColumnsTemplate()
    this.loading = true
    this.contractActService
      .get(this.associatedUuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.acts = res.dados
      })
  }

  onChangeTab(event: any) {
    const { tabId } = event
    this.touchedTabs.add(tabId)
  }

  ngOnDestroy(): void {
    this.subscription && this.subscription.unsubscribe()
  }

  getActColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'codigo',
        dataType: 'number',
        width: 80,
      },
      {
        caption: 'Tipo de ato',
        dataField: 'tipoAtoContratual',
        dataType: 'text',
        lookup: {
          dataSource: this.contractPreLoad.tiposAtoContratual,
          valueExpr: 'chave',
          displayExpr: 'valor',
        },
      },
      {
        caption: 'Data da assinatura',
        dataField: 'dataAssinatura',
        dataType: 'date',
      },
      {
        caption: 'Término vigência',
        dataField: 'terminoVigencia',
        dataType: 'date',
      },
      {
        caption: 'Término execução',
        dataField: 'terminoExecucao',
        dataType: 'date',
      },
      {
        caption: 'Valor',
        dataField: 'valor',
        dataType: 'number',
        format: ',##0.##',
        customizeText: options => 'R$' + options.valueText,
      },
      {
        caption: 'Ações',
        type: 'buttons',
        dataField: 'uuid',
        buttons: [
          {
            name: 'edit',
            icon: 'fas fa-edit',
          },
          {
            name: 'delete',
            icon: 'fas fa-trash-alt',
          },
        ],
      },
    ]
    return template
  }

  onActRowPrepared(event?: any) {
    if (event.isEditing || event.isNewRow) {
      const dialogRef = this.dialogService.open(
        ContractAdditionDialogComponent,
        {
          closeOnBackdropClick: false,
          closeOnEsc: false,
        },
      )
      dialogRef.componentRef.instance.associatedUuid = this.associatedUuid
      if (!event.isNewRow) {
        dialogRef.componentRef.instance.uuid = event.data.uuid
      }
      dialogRef.onClose.subscribe(res => {
        if (res) {
          Object.keys(res).forEach(key => (event.data[key] = res[key]))
          event.component.saveEditData()
        } else {
          event.component.cancelEditData()
        }
      })
    }
  }

  onActRemoving(event: any) {
    const { uuid } = event.data
    const dialogRef = this.dialogService.open(ConfirmationComponent)
    dialogRef.onClose.pipe(filter(req => req)).subscribe(_ => {
      this.contractActService
        .delete(this.associatedUuid, uuid)
        .pipe(take(1))
        .subscribe(
          _ => {
            this.toastr.send({
              success: true,
              title: 'Sucesso',
              message: 'Ato contratual removido(a) com sucesso!',
            })
          },
          err => {
            this.toastr.send({
              success: true,
              title: 'Erro',
              message: err.mensagem || 'Falha ao remover ato contratual',
            })
          },
        )
    })
  }

  tabWasTouched(tabId: string) {
    return this.touchedTabs.has(tabId)
  }
}
