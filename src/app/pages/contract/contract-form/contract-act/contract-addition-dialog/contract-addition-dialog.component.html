<eqp-nebular-dialog
  id="contract-addition-form-dialog"
  [dialogTitle]="'Ato contratual'"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [dialogSize]="'extra-large'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-addition-form-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="!model?.valid"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-addition-form-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>
  <ng-container [formGroup]="model">
    <nb-tabset>
      <nb-tab tabTitle="Ato contratual" class="p-0">
        <nb-tabset>
          <nb-tab tabTitle="Tela I">
            <div class="row">
              <div class="col-5 col-md-1">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  label="Código"
                  placeholder=""
                  formControlName="codigo"
                  [disabled]="true"
                >
                </eqp-nebular-input>
              </div>
              <div class="col-7 col-md-2">
                <eqp-nebular-select
                  label="Tipo de ato*"
                  [size]="'small'"
                  formControlName="tipoAtoContratual"
                  [selectValues]="tipoAtoContratualTemplate"
                ></eqp-nebular-select>
              </div>
              <div class="col-5 col-md-2">
                <eqp-nebular-select
                  label="Apostilamento"
                  [size]="'small'"
                  formControlName="tipoApostilamento"
                  [selectValues]="tipoApostilamentoTemplate"
                ></eqp-nebular-select>
              </div>
              <div class="col-7 col-md-3">
                <eqp-nebular-select
                  label="Tipo do aditivo*"
                  [size]="'small'"
                  formControlName="tipoAditivo"
                  [selectValues]="tipoAditivoTemplate"
                ></eqp-nebular-select>
              </div>
              <div class="col-6 col-md-2">
                <eqp-field-date
                  formControlName="dataAssinatura"
                  name="dataAssinatura"
                  label="Data da assinatura*"
                ></eqp-field-date>
              </div>
              <div class="col-6 col-md-2">
                <eqp-field-date
                  formControlName="dataInclusaoTce"
                  name="dataInclusaoTce"
                  label="Inclusão TCE"
                ></eqp-field-date>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-7 mt-3">
                <div class="row align-items-end">
                  <div class="col">
                    <label class="label text-uppercase">Novos prazos</label>
                    <div class="row">
                      <div class="col-6">
                        <eqp-field-date
                          formControlName="terminoVigencia"
                          name="terminoVigencia"
                          label="Término vigência"
                        ></eqp-field-date>
                      </div>
                      <div class="col-6">
                        <eqp-field-date
                          formControlName="terminoExecucao"
                          name="terminoExecucao"
                          label="Término execução"
                        ></eqp-field-date>
                      </div>
                    </div>
                  </div>
                  <div class="col col-md-4">
                    <label class="label mb-1">Previsão contratual</label>
                    <nb-radio-group
                      class="d-flex"
                      formControlName="tipoPrevisaoContratual"
                      name="tipoPrevisaoContratual"
                    >
                      <nb-radio
                        *ngFor="let option of tipoPrevisaoContratualTemplate"
                        [value]="option.valor"
                      >
                        {{ option.texto }}
                      </nb-radio>
                    </nb-radio-group>
                  </div>
                  <div class="col col-md-3">
                    <eqp-nebular-input
                      [style]="'basic'"
                      [type]="'number'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="valor"
                      name="valor"
                      label="Valor"
                      placeholder="00,00"
                    >
                    </eqp-nebular-input>
                  </div>
                </div>
              </div>
              <div class="col col-md-5 mt-3" formGroupName="classificacaoSimAm">
                <label class="label text-uppercase">Classificação SIM-AM</label>
                <div class="row">
                  <div class="col-6">
                    <eqp-nebular-select
                      label="Tipo do aditivo"
                      [size]="'small'"
                      formControlName="tipoAditivo"
                      [selectValues]="tipoAditivoTemplate"
                    ></eqp-nebular-select>
                  </div>
                  <div class="col-6">
                    <eqp-nebular-select
                      label="Tipo de operação do aditivo"
                      [size]="'small'"
                      formControlName="tipoOperacaoAditivo"
                      [selectValues]="tipoOperacaoTermoAditivoTemplate"
                    ></eqp-nebular-select>
                  </div>
                </div>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col-12">
                <eqp-nebular-input
                  [style]="'textArea'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="motivacao"
                  label="Motivação*"
                  placeholder="Motivação"
                >
                </eqp-nebular-input>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Tela II">
            <div class="row">
              <div class="col col-4">
                <eqp-nebular-select
                  label="Índice de correção"
                  [size]="'small'"
                  formControlName="tipoIndiceCorrecao"
                  [selectValues]="tipoIndiceCorrecaoTemplate"
                ></eqp-nebular-select>
              </div>
              <div class="col col-4">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="periodicidade"
                  placeholder=""
                  label="Periodicidade (meses)"
                >
                </eqp-nebular-input>
              </div>
              <div class="col col-4">
                <eqp-nebular-select
                  label="Mês do reajuste"
                  [size]="'small'"
                  formControlName="mesReajuste"
                  [selectValues]="mesReajusteTemplate"
                ></eqp-nebular-select>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col col-md-6">
                <eqp-nebular-select
                  label="Motivo da rescisão do contrato"
                  [size]="'small'"
                  formControlName="tipoMotivoRescisao"
                  [selectValues]="tipoMotivoRescisaoTemplate"
                ></eqp-nebular-select>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col col-md-6">
                <eqp-nebular-select
                  label="Tipo redimensionamento de objeto do contrato"
                  [size]="'small'"
                  formControlName="tipoRedimensionamentoObjeto"
                  [selectValues]="tipoRedimensionamentoObjetoTemplate"
                ></eqp-nebular-select>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col col-md-6">
                <eqp-field-date
                  formControlName="dataProrrogacaoCompra"
                  name="dataProrrogacaoCompra"
                  label="Data de prorrogação de compras"
                ></eqp-field-date>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col col-md-6">
                <eqp-nebular-toggle
                  formControlName="atendimentoCovid"
                  label="Atendimento - Covid 2019"
                  title="Atendimento - Covid 2019"
                ></eqp-nebular-toggle>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Tela III">
            <div class="row">
              <div class="col">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorProrrogacao"
                  label="Valor prorrogação"
                  placeholder="00,00"
                >
                </eqp-nebular-input>
              </div>
              <div class="col">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorAcrescimo"
                  label="Valor acréscimo"
                  placeholder="00,00"
                >
                </eqp-nebular-input>
              </div>
              <div class="col">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorReajuste"
                  label="Valor reajuste"
                  placeholder="00,00"
                >
                </eqp-nebular-input>
              </div>
              <div class="col">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorRecomposicao"
                  label="Valor recomposição"
                  placeholder="00,00"
                >
                </eqp-nebular-input>
              </div>
              <div class="col">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="valorSupressao"
                  label="Valor supressão"
                  placeholder="00,00"
                >
                </eqp-nebular-input>
              </div>
              <div class="col">
                <eqp-nebular-input
                  [style]="'basic'"
                  [type]="'number'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  [value]="getTotalValues()"
                  label="Total"
                  placeholder="00,00"
                  [disabled]="true"
                >
                </eqp-nebular-input>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col-12">
                <eqp-nebular-input
                  [style]="'textArea'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="descricao"
                  name="descricao"
                  label="Descrição"
                  placeholder="Descrição"
                >
                </eqp-nebular-input>
              </div>
            </div>
          </nb-tab>
        </nb-tabset>
      </nb-tab>
      <nb-tab tabTitle="Itens">
        <div class="text-right">
          <eqp-nebular-button
            buttonText="Selecionar itens do contrato"
            buttonTitle="Selecionar itens do contrato"
            (buttonEmitter)="selectContractItems()"
            [buttonVisible]="true"
          ></eqp-nebular-button>
        </div>
        <eqp-grid
          gridId="contract-addition-items-grid"
          gridName="Itens"
          gridSingularName="Item"
          [columnsTemplate]="itemColumnsTemplate"
          [gridData]="items || []"
          [allowAdding]="false"
          [allowDeleting]="false"
          gridEditMode="cell"
        >
        </eqp-grid>
      </nb-tab>
      <nb-tab [disabled]="!uuid" tabTitle="Documentos">
        <eqp-document-editor-list
          *ngIf="uuid"
          id="contractAdditionDocumentEditorId"
          [system]="'CONTRACT_ADDITION'"
          [associatedUuid]="uuid"
          [associatedUri]="'contrato/' + associatedUuid + '/ato'"
          [documents]="documentos || []"
        ></eqp-document-editor-list>
      </nb-tab>
      <nb-tab [disabled]="!uuid" tabTitle="Anexos">
        <eqp-attachment
          *ngIf="uuid"
          id="contractAdditionAttachmentId"
          [associatedUuid]="uuid"
          [associatedUri]="'contrato/' + associatedUuid + '/ato'"
        ></eqp-attachment>
      </nb-tab>
    </nb-tabset>
  </ng-container>
</eqp-nebular-dialog>
