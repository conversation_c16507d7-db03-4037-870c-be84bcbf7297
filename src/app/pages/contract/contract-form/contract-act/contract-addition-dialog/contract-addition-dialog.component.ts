import { ContractAdditionService } from './../../../services/contract-addition.service'
import { ContractAdditionInterface } from './../../../interfaces/contract-addition'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { ContractService } from '@pages/contract/services/contract.service'
import { ContractPreLoadInterface } from '@pages/pre-load'
import { PreLoadService } from '@pages/pre-load.service'
import {
  allMomentToDate,
  dateToMoment,
  enumsToSelectDto,
} from '@pages/shared/helpers/parsers.helper'
import { ProductItemInterface } from '@pages/shared/interfaces/product-item'
import { finalize, pluck, take } from 'rxjs/operators'
import { Observable } from 'rxjs'
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto'
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component'

@Component({
  selector: 'eqp-contract-addition-dialog',
  templateUrl: './contract-addition-dialog.component.html',
  styleUrls: ['./contract-addition-dialog.component.scss'],
})
export class ContractAdditionDialogComponent implements OnInit {
  loading = false
  model: FormGroup
  contractPreLoad: ContractPreLoadInterface
  planningPreLoad: any
  contractItems: ProductItemInterface[] = []

  itemColumnsTemplate: DxColumnInterface[] = []

  tipoAtoContratualTemplate: NebularSelectDto[] = []
  tipoAditivoTemplate: NebularSelectDto[] = []
  tipoOperacaoTermoAditivoTemplate: NebularSelectDto[] = []
  tipoIndiceCorrecaoTemplate: NebularSelectDto[] = []
  mesReajusteTemplate: NebularSelectDto[] = []
  tipoMotivoRescisaoTemplate: NebularSelectDto[] = []
  tipoRedimensionamentoObjetoTemplate: NebularSelectDto[] = []
  tipoApostilamentoTemplate: NebularSelectDto[] = [
    {
      texto: 'Sim',
      valor: 'YES',
    },
    {
      texto: 'Não',
      valor: 'NO',
    },
  ]
  tipoPrevisaoContratualTemplate: NebularSelectDto[] = [
    {
      texto: 'Existe',
      valor: 'ISSET',
    },
    {
      texto: 'Não existe',
      valor: 'NOT_ISSET',
    },
  ]

  @Input() associatedUuid
  @Input() uuid

  constructor(
    private dialogService: NbDialogService,
    private dialogRef: NbDialogRef<ContractAdditionDialogComponent>,
    private builder: FormBuilder,
    private preLoadService: PreLoadService,
    private contractService: ContractService,
    private contractAdditionService: ContractAdditionService,
    private toastr: ToastrService,
  ) {}


  get items() {
    return this.model.get('itens').value as ProductItemInterface[]
  }

  set items(value: ProductItemInterface[]) {
    this.model.get('itens').setValue(value)
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.contractPreLoad = this.preLoadService.contract
    this.planningPreLoad = this.preLoadService.planning
    this.loadSelectTemplates()
    this.itemColumnsTemplate = this.getItemColumnsTemplate()
    if (this.uuid) {
      this.fetchInitialData()
    }
  }

  fetchInitialData() {
    this.loading = true
    this.contractAdditionService
      .getIndividual(this.associatedUuid, this.uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          this.model.patchValue(res.dados)
        },
        error => {
          this.loading = false
          this.toastr.send({
            error: true,
            title: 'Error',
            message: error.mensagens
              ? error.mensagens
              : 'Falha ao carregar aditivo',
          })
        },
      )
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      codigo: [],
      tipoAtoContratual: [undefined, [Validators.required]],
      tipoApostilamento: [],
      tipoAditivo: [undefined, [Validators.required]],
      dataAssinatura: [undefined, [Validators.required]],
      dataInclusaoTce: [],
      terminoVigencia: [],
      terminoExecucao: [],
      tipoPrevisaoContratual: [],
      valor: [],
      motivacao: [undefined, [Validators.required]],
      tipoIndiceCorrecao: [],
      periodicidade: [],
      mesReajuste: [],
      tipoMotivoRescisao: [],
      tipoRedimensionamentoObjeto: [],
      dataProrrogacaoCompra: [],
      atendimentoCovid: [],
      valorProrrogacao: [],
      valorAcrescimo: [],
      valorReajuste: [],
      valorRecomposicao: [],
      valorSupressao: [],
      descricao: [],
      classificacaoSimAm: this.builder.group({
        tipoAditivo: [],
        tipoOperacaoAditivo: [],
      }),
      itens: [[], []],
      documentos: [],
      anexos: [],
    })
  }

  confirm() {
    if (this.model.valid) {
      let request: Observable<any>
      const dto = this.prepare(this.model.getRawValue())
      if (!this.uuid) {
        request = this.contractAdditionService.post(this.associatedUuid, dto)
      } else {
        request = this.contractAdditionService.put(
          this.associatedUuid,
          dto,
          this.uuid,
        )
      }
      request
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
          pluck('body', 'dados'),
        )
        .subscribe(
          res => {
            this.toastr.send({
              success: true,
              title: 'Sucesso',
              message: `Aditivo ${
                this.uuid ? 'atualizado' : 'cadastrado'
              }(a) com sucesso`,
            })
            this.dialogRef.close(res)
          },
          error => {
            this.toastr.send({
              error: true,
              title: 'Error',
              message:
                error.mensagens ||
                `Falha ao ${this.uuid ? 'atualizar' : 'cadastrar'} aditivo`,
            })
          },
        )
    }
  }

  private prepare(formData: any): ContractAdditionInterface {
    const dto = allMomentToDate(formData) as ContractAdditionInterface
    return dto
  }

  dispose() {
    this.dialogRef.close()
  }

  private loadSelectTemplates() {
    this.tipoAtoContratualTemplate = enumsToSelectDto(
      this.contractPreLoad.tiposAtoContratual,
    )
    this.tipoIndiceCorrecaoTemplate = enumsToSelectDto(
      this.contractPreLoad.tiposIndiceCorrecao,
    )
    this.mesReajusteTemplate = enumsToSelectDto(
      this.planningPreLoad.mesFechamento,
    )
    this.tipoAditivoTemplate = enumsToSelectDto(
      this.contractPreLoad.tiposTermoAditivo,
    )
    this.tipoOperacaoTermoAditivoTemplate = enumsToSelectDto(
      this.contractPreLoad.tiposOperacaoTermoAditivo,
    )
    this.tipoMotivoRescisaoTemplate = enumsToSelectDto(
      this.contractPreLoad.tiposMotivoRescisaoContrato,
    )
    this.tipoRedimensionamentoObjetoTemplate = enumsToSelectDto(
      this.contractPreLoad.tiposRedimensionamentoContrato,
    )
  }

  selectContractItems() {
    if (!this.contractItems || this.contractItems.length == 0) {
      this.loading = true
      this.contractService
        .getIndividual(this.associatedUuid)
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(
          res => {
            // this.contractItems = res.dados.itens
            this.openContractItemDialog()
          },
          error => {
            this.loading = false
            this.toastr.send({
              error: true,
              title: 'Error',
              message: error.mensagens
                ? error.mensagens
                : 'Falha ao carregar itens do contrato',
            })
          },
        )
    } else {
      this.openContractItemDialog()
    }
  }

  openContractItemDialog() {
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar itens'
    dialogRef.componentRef.instance.columnsTemplate =
      this.getItemColumnsTemplate()
    dialogRef.componentRef.instance.multiple = true
    dialogRef.componentRef.instance.dataGrid = this.contractItems
    dialogRef.onClose.subscribe((res: ProductItemInterface[]) => {
      if (res) {
        this.updateAdditionItems(res)
      }
    })
  }

  getTotalValues() {
    const {
      valorProrrogacao = 0,
      valorAcrescimo = 0,
      valorReajuste = 0,
      valorRecomposicao = 0,
      valorSupressao = 0,
    } = this.model.getRawValue()
    return (
      Number(valorProrrogacao) +
      Number(valorAcrescimo) +
      Number(valorReajuste) +
      Number(valorRecomposicao) +
      Number(valorSupressao)
    )
  }

  private updateAdditionItems(res: ProductItemInterface[]) {
    const productMap = new Map<string, ProductItemInterface>()
    res.forEach(item => productMap.set(item.uuid, item))
    this.items.forEach(item => productMap.set(item.uuid, item))
    const newItems = Array.from(productMap.values()).sort(item => item.ordem)
    this.items = newItems
  }

  private getItemColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'produto.codigo',
        allowEditing: false,
      },
      {
        caption: 'Nome',
        dataField: 'produto.nome',
        allowEditing: false,
      },
      {
        caption: 'Lote',
        dataField: 'lote',
        dataType: 'number',
        allowEditing: false,
      },
      {
        caption: 'Quantidade',
        dataField: 'quantidade',
        dataType: 'number',
        formItem: {
          isRequired: true,
        },
      },
      {
        caption: 'Preço min.',
        dataField: 'precoMinimo',
        dataType: 'number',
        format: ',##0.##',
        allowEditing: false,
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Preço max.',
        dataField: 'precoMaximo',
        dataType: 'number',
        format: ',##0.##',
        allowEditing: false,
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Total',
        dataField: 'total',
        dataType: 'number',
        format: ',##0.##',
        allowEditing: false,
        customizeText: options => 'R$' + options.valueText,
        formItem: {
          visible: false,
        },
      },
    ]
    return template
  }
}
