import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto'
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { OfficialOrganInterface } from '@pages/contract/interfaces/official-organ'
import { OfficialOrganPublicationInterface } from '@pages/contract/interfaces/official-organ-publication'
import { OfficialOrganService } from '@pages/contract/services/official-organ.service'
import { ContractPreLoadInterface } from '@pages/pre-load'
import { PreLoadService } from '@pages/pre-load.service'
import {
  dateToMoment,
  enumsToSelectDto,
} from '@pages/shared/helpers/parsers.helper'
import { EnumDataInterface } from '@pages/shared/interfaces/enum-data'
import { Subscription } from 'rxjs'

@Component({
  selector: 'eqp-official-organ-publication-dialog',
  templateUrl: './official-organ-publication-dialog.component.html',
  styleUrls: ['./official-organ-publication-dialog.component.scss'],
})
export class OfficialOrganPublicationDialogComponent implements OnInit {
  @Input() dialogTitle: string = 'Publicação Órgão Oficial'
  @Input() initialValue: OfficialOrganPublicationInterface
  loading: boolean = false

  model: FormGroup
  officialOrganSbs: Subscription

  officialOrganData: OfficialOrganInterface[] = []
  officialOrgan: OfficialOrganInterface

  tipoOrgaoOficialTemplate: NebularSelectDto[] = []
  orgaoOficial: OfficialOrganInterface[] = []

  officialOrganTypes: EnumDataInterface[] = []

  constructor(
    private dialogRef: NbDialogRef<OfficialOrganPublicationDialogComponent>,
    private officialOrganService: OfficialOrganService,
    private builder: FormBuilder,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
    private preLoadService: PreLoadService,
  ) {}

  get orgao() {
    return this.model.get('orgao')?.value
  }

  ngOnInit(): void {
    this.getOfficialOrganType()
    this.fetchLoadData()
    this.model = this.getNewModel()
    if (this.initialValue) {
      const dto = {
        ...this.initialValue,
        dataPublicacao: dateToMoment(this.initialValue.dataPublicacao),
        dataInclusaoTce: dateToMoment(this.initialValue.dataInclusaoTce),
      }
      this.model.patchValue(dto)
    }
  }

  confirm() {
    const dto = this.model.getRawValue()
    this.dialogRef.close(dto)
  }

  dispose() {
    this.dialogRef.close()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      orgao: [],
      dataPublicacao: [],
      dataInclusaoTce: [],
    })
  }

  getOfficialOrganPlaceholder(res): string {
    const officialOrganType = this.officialOrganTypes.find(
      item => item.chave == res,
    )
    return officialOrganType ? officialOrganType.valor : 'Órgão Oficial'
  }

  async onOfficialOrganSearchInput(event: any) {
    const index = this.officialOrganData.findIndex(
      officialOrgan => officialOrgan == event,
    )
    if (index >= 0) {
      this.model.get('uuid').setValue(this.officialOrganData[index].uuid)
      this.officialOrgan = this.officialOrganData[index]
    } else {
      this.model.get('uuid').reset()
      this.officialOrgan = null
    }
  }

  async onOfficialOrganSearchDialog() {
    this.loading = true
    this.officialOrganData = await this.officialOrganService.syncFetchData(
      this.officialOrganData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar Órgão Oficial'
    dialogRef.componentRef.instance.dataGrid = this.officialOrganData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'codigo',
        dataType: 'number',
      },
      {
        caption: 'Tipo Órgão',
        dataField: 'tipoOrgao',
        lookup: {
          dataSource: this.tipoOrgaoOficialTemplate,
          valueExpr: 'valor',
          displayExpr: 'texto',
        },
      },
      {
        caption: 'Pessoa',
        dataField: 'pessoa.dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('orgao').patchValue(res)
        this.officialOrgan = res
      }
    })
  }

  fetchLoadData() {
    this.officialOrganSbs = this.officialOrganService.get().subscribe(res => {
      this.orgaoOficial = res.dados
    })
  }

  getOfficialOrganType() {
    const contractPreLoad = this.preLoadService
      .contract as ContractPreLoadInterface
    this.officialOrganTypes = contractPreLoad.tiposOrgaoOficial || []
    this.tipoOrgaoOficialTemplate = enumsToSelectDto(
      contractPreLoad.tiposOrgaoOficial || [],
    )
  }
}
