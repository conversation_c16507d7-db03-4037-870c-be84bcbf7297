<eqp-nebular-dialog
  id="official-organ-publication-dialog"
  [dialogTitle]="dialogTitle"
  [spinnerStatus]="'info'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-official-organ-publication-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="!model.valid"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-official-organ-publication-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>
<ng-container [formGroup]="model">
  <div class="container">
    <div class="row">
      <div class="col">
        <label class="label">Órgão Oficial</label>
        <eqp-nebular-search-input
        formControlName="orgao"
        (onSearch)="onOfficialOrganSearchInput($event)"
        (onButtonClick)="onOfficialOrganSearchDialog()"
        [placeholder]="getOfficialOrganPlaceholder(orgao?.tipoOrgao)"
        ></eqp-nebular-search-input>
      </div>
      <div class="col">
        <eqp-nebular-input
        [style]="'date'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="dataPublicacao"
        name="dataPublicacao"
        label="Data de Publicação"
        placeholder="00/00/0000"
      >
      </eqp-nebular-input>
      </div>
      <div class="col">
        <eqp-nebular-input
        [style]="'date'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="dataInclusaoTce"
        name="dataInclusaoTce"
        label="Data Inclusão TCE"
        placeholder="00/00/0000"
      >
      </eqp-nebular-input>
      </div>
    </div>
  </div>
</ng-container>
</eqp-nebular-dialog>
