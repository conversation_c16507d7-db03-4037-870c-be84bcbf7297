import { Component, Input, OnInit } from '@angular/core';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { DxColumnInterface } from '@design-tools/interfaces/column-interface';
import { NbDialogService } from '@nebular/theme';
import { OfficialOrganInterface } from '@pages/contract/interfaces/official-organ';
import { OfficialOrganPublicationInterface } from '@pages/contract/interfaces/official-organ-publication';
import { OfficialOrganPublicationService } from '@pages/contract/services/official-organ-publication.service';
import { OfficialOrganService } from '@pages/contract/services/official-organ.service';
import { ContractPreLoadInterface } from '@pages/pre-load';
import { PreLoadService } from '@pages/pre-load.service';
import { allMomentToDate, enumsToSelectDto } from '@pages/shared/helpers/parsers.helper';
import { Subscription } from 'rxjs';
import { finalize, take } from 'rxjs/operators';
import { OfficialOrganPublicationDialogComponent } from './official-organ-publication-dialog/official-organ-publication-dialog.component';
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto';

@Component({
  selector: 'eqp-official-organ-publication',
  templateUrl: './official-organ-publication.component.html',
  styleUrls: ['./official-organ-publication.component.scss']
})
export class OfficialOrganPublicationComponent implements OnInit {
  @Input() associatedUuid
  loading: boolean = false

  orgaoOficial: OfficialOrganInterface[] = []

  officialOrganPublicationColumnsTemplate: DxColumnInterface[] = []

  tipoOrgaoOficialTemplate: NebularSelectDto[] = []

  gridData: OfficialOrganPublicationInterface[] = []

  officialOrganSbs: Subscription

  constructor(
    private preLoadService: PreLoadService,
    protected officialOrganPublicationService: OfficialOrganPublicationService,
    private officialOrganService: OfficialOrganService,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
  ) { }

  ngOnInit(): void {
    this.fetchGrid()
    this.fetchLoadData()
    this.getOfficialOrganType()
    this.officialOrganPublicationColumnsTemplate = this.getOfficialOrganPublicationColumnsTemplate()
  }

  public fetchGrid(): void {
    this.loading = true
    this.officialOrganPublicationService
      .get(this.associatedUuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        data => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  fetchLoadData() {
    this.officialOrganSbs = this.officialOrganService.get().subscribe(res => {
      this.orgaoOficial = res.dados
    })
  }

  prepare(data: Partial<OfficialOrganPublicationInterface>): OfficialOrganPublicationInterface {
    const formData = allMomentToDate(data)
    return formData as OfficialOrganPublicationInterface
  }

  getOfficialOrganPublicationColumnsTemplate() {
    const template: DxColumnInterface[] = [
      {
        caption: 'Orgão Oficial',
        dataField: 'orgao.tipoOrgao',
        lookup: {
          dataSource: this.tipoOrgaoOficialTemplate,
          valueExpr: 'valor',
          displayExpr: 'texto'
        }
      },
      {
        caption: 'Data de Publicação',
        dataField: 'dataPublicacao',
        dataType: 'date'
      },
      {
        caption: 'Data de Inclusão TCE',
        dataField: 'dataInclusaoTce',
        dataType: 'date'
      },
    ]
    return template
  }

  getOfficialOrganType() {
    const contractPreLoad = this.preLoadService
      .contract as ContractPreLoadInterface
      this.tipoOrgaoOficialTemplate = enumsToSelectDto(
        contractPreLoad.tiposOrgaoOficial || []
      )
  }

  onOfficialOrganPublicationRowPrepared(event?: any) {
    if (event.isEditing || event.isNewRow) {
      const dialogRef = this.dialogService.open(OfficialOrganPublicationDialogComponent, {
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      dialogRef.componentRef.instance.dialogTitle = 'Inserir Publicação Órgão Oficial'
      if (!event.isNewRow)
      dialogRef.componentRef.instance.initialValue = event.data
      dialogRef.onClose.subscribe(res => {
        if (res) {
          const dto = this.prepare(res) as OfficialOrganPublicationInterface
          if(!event.isNewRow) {
            this.officialOrganPublicationService
            .put(this.associatedUuid, dto, dto.uuid)
            .pipe(take(1))
            .subscribe(
              res => {
                event.data = res
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Publicação órgão oficial atualizada com sucesso!'
                })
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao atualizar publicação órgão oficial!',
                })
              },
            )
          } else {
            this.officialOrganPublicationService
            .post(this.associatedUuid, dto)
            .pipe(take(1))
            .subscribe(
              res => {
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Publicação órgão oficial adicionada com sucesso!'
                })
                this.fetchGrid()
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao adicionar publicação órgão oficial!',
                })
              },
            )
          }
          Object.keys(res).forEach(key => (event.data[key] = res[key]))
          event.component.saveEditData()
        } else {
          event.component.cancelEditData()
        }
      })
    }
  }

  public remove(event: any): void {
    this.loading = true
    this.officialOrganPublicationService
    .delete(this.associatedUuid, event.data.uuid)
    .pipe(take(1))
    .subscribe({
      error: e => {
        this.toastr.send({
          error: true,
          title: 'Erro',
          message: e.message || "Falha ao deletar a publicação órgão oficial",
        })
      },
      next: () => {
        this.toastr.send({
          success: true,
          title: 'Sucesso',
          message: "Publicação órgão oficial deletada com sucesso!",
        })
      },
      complete: () => (this.loading = false),
    })
  }

}
