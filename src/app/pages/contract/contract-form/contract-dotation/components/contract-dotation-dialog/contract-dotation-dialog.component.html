<eqp-nebular-dialog
  [dialogTitle]="title"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonId]="'submit-item-update'"
  [rightFirstButtonDisabled]="!model.valid"
  (rightFirstButtonEmitter)="addNewDotation()"
  dialogSize="extra-large"
>
  <ng-container [formGroup]="model">
    <div class="row">
      <div class="col-12 col-md-5 mb-2">
        <app-enhanced-search-field
          label="Previsão despesa"
          formControlName="despesa"
          uri="licitacao/contrato/previsao_inicial_despesa"
          dialogTitle="Previsão despesa"
          nameKey="previsaoInicialDespesa.planoDespesa.nome"
          [columns]="colunasPrevisaoInicialDespesa"
          [disabled]="data"
          [returnAllData]="true"
          [required]="true"
        ></app-enhanced-search-field>
      </div>
      <div class="col-12 col-md mb-2">
        <eqp-nebular-select
          [required]="true"
          [disabled]="data"
          label="Grupo da fonte"
          formControlName="grupoFonte"
          [dataSource]="sourceGroup"
          displayExpr="nome"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col-12 col-md mb-2">
        <eqp-nebular-input
          *ngIf="data; else newDotation"
          [disabled]="true"
          [style]="'basic'"
          [type]="'text'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Natureza de despesa"
          placeholder=""
          formControlName="naturezaDespesa"
        ></eqp-nebular-input>

        <ng-template #newDotation>
          <eqp-nebular-select
            [required]="true"
            label="Natureza de despesa"
            formControlName="naturezaDespesa"
            [dataSource]="expenseNature"
            displayExpr="codigoNome"
            valueExpr="uuid"
          ></eqp-nebular-select>
        </ng-template>
      </div>
    </div>
    <div class="row">
      <div class="col-12 col-md-3 col-xxl-2 mb-2">
        <eqp-field-date
          [required]="true"
          formControlName="dataInclusaoTce"
          name="dataInclusaoTce"
          label="Inclusão TCE"
        ></eqp-field-date>
      </div>
      <div class="col-12 col-md-3 col-xxl-2 mb-2">
        <eqp-nebular-input
          [readonly]="true"
          [type]="'text'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Valor dotado nos itens"
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          placeholder=""
          formControlName="dotadoItens"
        ></eqp-nebular-input>
      </div>
      <div class="col-12 col-md-3 col-xxl-2">
        <eqp-nebular-input
          [readonly]="true"
          [style]="'basic'"
          [type]="'text'"
          [size]="'small'"
          [shape]="'rectangle'"
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          label="Valor previsto dotado"
          placeholder=""
          formControlName="previstoDotado"
        ></eqp-nebular-input>
      </div>
    </div>
  </ng-container>
</eqp-nebular-dialog>
