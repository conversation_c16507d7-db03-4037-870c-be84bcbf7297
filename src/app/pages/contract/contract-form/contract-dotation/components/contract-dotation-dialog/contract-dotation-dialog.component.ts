import { Component, Input, OnDestroy, OnInit } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import { Subject } from 'rxjs'
import {
  finalize,
  switchMap,
  take,
} from 'rxjs/operators'
import * as _ from 'lodash'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ContractDotationService } from '@pages/contract/services/contract-dotation.service'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { DefaultColumnSearchInterface } from '@pages/shared/interfaces/default-search'
import { colunasPrevisaoInicialDespesa } from '@pages/shared/search/search-column-type/colunas-previsao-inicial-despesa'
import { ordenarNaturezaDespesa } from '@pages/shared/interfaces/ordenar-natureza-despesa'

@Component({
  selector: 'eqp-contract-dotation-dialog',
  templateUrl: './contract-dotation-dialog.component.html',
  styleUrls: ['./contract-dotation-dialog.component.scss'],
})
export class ContractDotationDialogComponent implements OnInit, OnDestroy {
  unsubs$ = new Subject<void>()

  loading = false
  model: FormGroup
  currencyFormat = currencyFormat
  sourceGroup: any
  expenseAccount: any
  expenseNature: any
  title = ''
  colunasPrevisaoInicialDespesa: DefaultColumnSearchInterface[] = []

  @Input() hasUuid: boolean
  @Input() data: any
  @Input() parentUuid: string

  constructor(
    private dialogRef: NbDialogRef<ContractDotationDialogComponent>,
    private dialogService: NbDialogService,
    private crudService: CrudService,
    private contractDotationService: ContractDotationService,
    private toastr: ToastrService,
    private builder: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    if (this.data) {
      this.patchValue()
      this.title = 'Editar dotação'
    } else {
      this.title = 'Adicionar dotação'
    }
    this.initialObservable()
    this.colunasPrevisaoInicialDespesa = colunasPrevisaoInicialDespesa()
  }

  initialObservable() {
    this.loading = true
    this.crudService
      .getSingleObject(`licitacao/contrato/grupo_fonte`)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.sourceGroup = res.data
      })

    this.model
      .get('despesa')
      .valueChanges.pipe(
        switchMap(despesaValue => {
          this.model
            .get('previstoDotado')
            .patchValue(despesaValue.valorAutorizado)
          return this.crudService.getSingleObject<any[]>(
            `licitacao/contrato/natureza_despesa/${despesaValue.uuid}`,
            finalize(() => (this.loading = false)),
          )
        }),
      )
      .subscribe(res => {
        this.expenseNature = ordenarNaturezaDespesa(res.dados) 
      })
  }

  patchValue() {
    this.model.patchValue({
      uuid: this.data.uuid,
      previstoDotado: this.data.valorPrevistoDotado,
      dotadoItens: this.data.valorDotadoItens,
      naturezaDespesa: this.data.planoDespesa.codigoNome,
      grupoFonte: this.data.grupoFonte.uuid,
      despesa: this.data.previsaoInicialDespesaFonte,
      dataInclusaoTce: this.data.dataInclusaoTce,
    })
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [''],
      previstoDotado: [''],
      quantidade: [''],
      dotadoItens: [''],
      naturezaDespesa: [''],
      grupoFonte: [''],
      despesa: [''],
      dataInclusaoTce: [''],
    })
  }

  ngOnDestroy(): void {
    this.unsubs$.next()
    this.unsubs$.complete()
  }

  cancel() {
    this.dialogRef.close()
  }

  get dataInclusaoTce() {
    return this.model.get('dataInclusaoTce')?.value
  }

  confirm() {
    this.loading = true
    if (this.data) {
      const dto = {
        ...this.data,
        valorPrevistoDotado: this.model.get('previstoDotado').value,
        dataInclusaoTce: this.dataInclusaoTce,
      }

      this.contractDotationService
        .putAllocation(this.parentUuid, this.data.uuid, dto)
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(res => {
          this.toastr.send({
            title: 'Sucesso',
            message: `Editado com sucesso!`,
            success: true,
          })
          this.dialogRef.close(res)
        })
    } else {
      const dto = this.model.getRawValue()
      const formattedData = {
        grupoFonte: {
          uuid: dto.grupoFonte,
        },
        planoDespesa: {
          uuid: dto.naturezaDespesa,
        },
        previsaoInicialDespesaFonte: {
          uuid: dto.despesa.uuid,
        },
        valorDotadoItens: dto.dotadoItens,
        valorPrevistoDotado: dto.previstoDotado,
        dataInclusaoTce: this.dataInclusaoTce,
      }

      this.contractDotationService
        .postAllocation(this.parentUuid, formattedData)
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(res => {
          this.toastr.send({
            title: 'Sucesso',
            message: `Criado com sucesso!`,
            success: true,
          })
          this.dialogRef.close(res)
        })
    }
  }

  addNewDotation() {
    if (this.hasUuid) {
      this.confirm()
    } else {
      this.dialogService
        .open(ConfirmationComponent, {
          context: {
            confirmationContent: {
              body: 'O carregamento das dotações não foi salvo, deseja incluir nova dotação e descartar as demais, caso contrário clique em cancelar e salve antes o carregamento. Deseja cancelar ou confirmar ? ',
            },
          },
          closeOnEsc: false,
          closeOnBackdropClick: false,
        })
        .onClose.pipe(take(1))
        .subscribe(res => {
          if (res) {
            this.confirm()
          }
        })
    }
  }
}
