<ng-container class="pt-3">
  <eqp-loading *ngIf="loading"></eqp-loading>
  <nb-tabset>
    <nb-tab tabTitle="Exercício Atual" class="col">
      <dx-data-grid
        [dataSource]="filteredDataCurrentExercise"
        [allowColumnResizing]="true"
        [columnAutoWidth]="true"
        [showColumnLines]="false"
        [showRowLines]="false"
        [showBorders]="false"
        [rowAlternationEnabled]="true"
        [wordWrapEnabled]="true"
        [loadPanel]="false"
        [columnHidingEnabled]="false"
        [remoteOperations]="true"
        keyExpr="uuidUnique"
        (onToolbarPreparing)="onToolbarPreparing($event)"
      >
        <dxo-paging [pageSize]="10"></dxo-paging>

        <dxo-pager
          [showInfo]="true"
          [showNavigationButtons]="true"
          [showPageSizeSelector]="false"
        >
        </dxo-pager>

        <dxo-sorting mode="false"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

        <dxo-group-panel
          [visible]="false"
          [emptyPanelText]="''"
        ></dxo-group-panel>

        <dxo-editing
          mode="form"
          [allowUpdating]="false"
          [allowDeleting]="false"
          [allowAdding]="true"
          [useIcons]="true"
        >
        </dxo-editing>
        <dxi-column
          alignment="left"
          dataField="exercicio"
          dataType="number"
          caption="Exercício"
          [width]="120"
        ></dxi-column>

        <dxi-column
          alignment="left"
          dataField="previsaoInicialDespesaFonte.codigo"
          dataType="number"
          caption="Conta despesa"
          [width]="120"
        ></dxi-column>

        <dxi-column
          dataField="grupoFonte.nome"
          caption="Grupo fonte"
        ></dxi-column>

        <dxi-column
          dataField="planoDespesa.codigoNome"
          caption="Natureza de despesa"
        ></dxi-column>
        <dxi-column
          *ngIf="hasUuid"
          dataField="dataInclusaoTce"
          dataType="date"
          caption="Inclusão TCE"
          [width]="120"
        >
        </dxi-column>
        <dxi-column
          dataField="valorDotadoItens"
          caption="Valor dotado nos itens"
          alignment="right"
          cellTemplate="priceTemplate"
          [width]="120"
        ></dxi-column>
        <dxi-column
          dataField="valorPrevistoDotado"
          caption="Valor previsto dotado"
          cellTemplate="priceTemplate"
          alignment="right"
          [width]="120"
        ></dxi-column>

        <dxi-column
          alignment="left"
          dataField="uuid"
          caption=""
          [width]="80"
          [allowFiltering]="false"
          [allowSorting]="false"
          cellTemplate="acaoColumn"
        ></dxi-column>

        <div *dxTemplate="let data of 'priceTemplate'">
          {{data.value | currency: 'BRL'}}
        </div>

        <div *dxTemplate="let data of 'acaoColumn'">
          <div
            class="w-100 d-flex justify-content-center"
            style="gap: 0.5rem"
            *ngIf="hasUuid"
          >
            <a
              title="Editar"
              (click)="edit(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              title="Remover"
              (click)="remove(data.value)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </div>
      </dx-data-grid>
    </nb-tab>
    <nb-tab tabTitle="Outros exercícios" class="col">
      <dx-data-grid
        id="another-exercise"
        [allowColumnResizing]="true"
        [columnAutoWidth]="true"
        [dataSource]="filteredDataAnotherExercise"
        [showColumnLines]="false"
        [showRowLines]="false"
        [showBorders]="false"
        [rowAlternationEnabled]="true"
        [wordWrapEnabled]="true"
        [loadPanel]="false"
        [columnHidingEnabled]="false"
        [remoteOperations]="true"
        keyExpr="uuidUnique"
      >
        <dxo-paging [pageSize]="10"></dxo-paging>

        <dxo-pager
          [showInfo]="true"
          [showNavigationButtons]="true"
          [showPageSizeSelector]="false"
        >
        </dxo-pager>

        <dxo-sorting mode="false"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

        <dxo-group-panel
          [visible]="false"
          [emptyPanelText]="''"
        ></dxo-group-panel>

        <dxo-editing
          mode="form"
          [allowUpdating]="false"
          [allowDeleting]="false"
          [allowAdding]="false"
          [useIcons]="true"
        >
        </dxo-editing>

        <dxi-column
          alignment="left"
          dataField="exercicio"
          dataType="number"
          caption="Exercício"
          [width]="120"
        ></dxi-column>

        <dxi-column
          alignment="left"
          dataField="previsaoInicialDespesaFonte.codigo"
          dataType="number"
          caption="Conta despesa"
          [width]="120"
        ></dxi-column>

        <dxi-column
          dataField="grupoFonte.nome"
          caption="Grupo fonte"
        ></dxi-column>

        <dxi-column
          dataField="planoDespesa.codigoNome"
          caption="Natureza de despesa"
        ></dxi-column>
        <dxi-column
          *ngIf="hasUuid"
          dataField="dataInclusaoTce"
          dataType="date"
          caption="Inclusão TCE"
          [width]="120"
        >
        </dxi-column>
        <dxi-column
          alignment="right"
          dataField="valorDotadoItens"
          caption="Valor dotado nos itens"
          cellTemplate="priceTemplate"
          [width]="120"
        ></dxi-column>
        <dxi-column
          alignment="right"
          dataField="valorPrevistoDotado"
          caption="Valor previsto dotado"
          cellTemplate="priceTemplate"
          [width]="120"
        ></dxi-column>

        <dxi-column
          alignment="left"
          dataField="uuid"
          caption=""
          [width]="40"
          [allowFiltering]="false"
          [allowSorting]="false"
          cellTemplate="acaoColumn"
        ></dxi-column>

        <div *dxTemplate="let data of 'priceTemplate'">
          {{data.value | currency: 'BRL'}}
        </div>

        <div *dxTemplate="let data of 'acaoColumn'">
          <div class="w-100 d-flex justify-content-center" style="gap: 0.5rem">
            <a
              title="Remover"
              (click)="remove(data.value)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </div>
      </dx-data-grid>
    </nb-tab>
  </nb-tabset>
  <div class="d-flex mt-4 justify-content-end" style="gap: 0.5rem">
    <eqp-nebular-button
      [buttonShape]="'rectangle'"
      [buttonText]="'Voltar'"
      (buttonEmitter)="previous()"
      [buttonType]="'primary'"
      [buttonAppearance]="'outline'"
      [buttonIcon]="'fas fa-undo-alt'"
      [buttonIconVisible]="true"
      [buttonVisible]="true"
    ></eqp-nebular-button>
    <eqp-nebular-button
      [buttonShape]="'rectangle'"
      [buttonText]="'Próximo'"
      (buttonEmitter)="next()"
      [buttonType]="'primary'"
      [buttonAppearance]="'outline'"
      [buttonIcon]="'fas fa-arrow-right'"
      [buttonIconVisible]="true"
      [buttonVisible]="true"
    ></eqp-nebular-button>
  </div>
</ng-container>
