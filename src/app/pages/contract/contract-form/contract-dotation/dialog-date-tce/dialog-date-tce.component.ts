import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { NbDialogRef } from '@nebular/theme'
import { ContractDotationDialogComponent } from '../components/contract-dotation-dialog/contract-dotation-dialog.component'

@Component({
  selector: 'eqp-dialog-date-tce',
  templateUrl: './dialog-date-tce.component.html',
  styleUrls: ['./dialog-date-tce.component.scss'],
})
export class DialogDateTceComponent implements OnInit {
  model: FormGroup

  constructor(
    private dialogRef: NbDialogRef<ContractDotationDialogComponent>,
    private builder: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      dataInclusaoTce: [''],
    })
  }
  cancel() {
    this.dialogRef.close()
  }

  confirm() {
    const res = this.model.get('dataInclusaoTce').value.format('YYYY-MM-DD')
    this.dialogRef.close(res)
  }
}
