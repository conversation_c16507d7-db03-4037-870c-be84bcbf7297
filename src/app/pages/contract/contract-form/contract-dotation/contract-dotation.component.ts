import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { ContractDotationService } from '@pages/contract/services/contract-dotation.service'
import { currencyFormat } from '@pages/shared/helpers/format.helper'
import { filter, finalize } from 'rxjs/operators'
import { ContractDotationDialogComponent } from './components/contract-dotation-dialog/contract-dotation-dialog.component'
import { ToolbarPreparingEvent } from 'devextreme/ui/data_grid'
import { UserDataService } from '@guards/services/user-data.service'

@Component({
  selector: 'eqp-contract-dotation',
  templateUrl: './contract-dotation.component.html',
  styleUrls: ['./contract-dotation.component.scss'],
})
export class ContractDotationComponent implements OnInit {
  pageTitle: string = 'Dotações'
  quantityLoads = 0
  currencyFormat = currencyFormat
  parentUuid: string
  dataSource = []
  filteredDataCurrentExercise = []
  filteredDataAnotherExercise = []
  hasUuid = false

  constructor(
    private service: ContractDotationService,
    private dialogService: NbDialogService,
    private crudService: CrudService,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private userService: UserDataService,
  ) {}

  ngOnInit(): void {
    const { uuid } = this.route.snapshot.params
    this.parentUuid = uuid
    this.fetchGrid()
  }

  fetchGrid() {
    this.filteredDataCurrentExercise = []
    this.filteredDataAnotherExercise = []
    this.quantityLoads++
    this.crudService
      .getSingleData<any>(`licitacao/contrato/${this.parentUuid}/dotacao`, { take: 0 })
      .pipe(finalize(() => (this.quantityLoads--)))
      .subscribe(res => {
        this.dataSource = res.data
          .sort((a, b) => a.previsaoInicialDespesaFonte.codigo - b.previsaoInicialDespesaFonte.codigo)
        this.dataSource.forEach((item, index) => {
          item.uuidUnique = index + 1
          if (item.previsaoInicialDespesaFonte.exercicioUuid == this.userService.userData.exercicioUuid) {
            this.filteredDataCurrentExercise.push(item)
          } else {
            this.filteredDataAnotherExercise.push(item)
          }
        })
        this.hasUuid = !!this.filteredDataCurrentExercise[0]?.uuid
      })
  }

  public onToolbarPreparing(event: ToolbarPreparingEvent): void {
    if (event?.toolbarOptions?.items) {
      event.toolbarOptions.items[0].showText = 'ever'
      event.toolbarOptions.items[0].options.text = 'Dotação'
      event.toolbarOptions.items[0].options.hint = 'Nova dotação'
      event.toolbarOptions.items[0].options.onClick = () =>
        this.openAddItemDialog()
    }
  }

  edit(value) {
    const dialogRef = this.dialogService.open(ContractDotationDialogComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.componentRef.instance.data = value.data
    dialogRef.componentRef.instance.parentUuid = this.parentUuid
    dialogRef.onClose.pipe(filter(res => res)).subscribe(_ => {
      this.fetchGrid()
    })
  }

  remove(uuidAllocation: any) {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.subscribe(res => {
      if (res === 'S') {
        this.service
          .deleteAllocationSingle(this.parentUuid, uuidAllocation)
          .pipe()
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: `Dotação removida com sucesso.`,
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  openAddItemDialog() {
    const dialogRef = this.dialogService.open(ContractDotationDialogComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.componentRef.instance.parentUuid = this.parentUuid
    dialogRef.componentRef.instance.hasUuid = this.hasUuid
    dialogRef.onClose.pipe(filter(res => res)).subscribe(_ => {
      this.fetchGrid()
    })
  }

  next() {
    this.router.navigate([
      `contrato/edit/${this.parentUuid}/atos-contratuais/ato`,
    ])
  }
  previous() {
    this.router.navigate([`contrato/edit/${this.parentUuid}/item`])
  }

  get loading() {
    return !!this.quantityLoads
  }
}
