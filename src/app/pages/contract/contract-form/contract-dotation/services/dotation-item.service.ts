import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ResponseDto } from '@common/interfaces/dtos/response-dto'
import { DotationItemInterface } from '@pages/contract/interfaces/dotation-item'
import { BehaviorSubject, Observable, combineLatest, of } from 'rxjs'
import { switchMap, take, tap } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class DotationItemService {
  store$ = new BehaviorSubject<DotationItemInterface[]>([])

  constructor(private http: HttpClient) {}

  initializeData(data: DotationItemInterface[]) {
    this.store$.next(data)
  }

  addBatch(data: DotationItemInterface[]) {
    return combineLatest([
      this.store$,
      this.store$.pipe(
        switchMap(items => {
          const indexes = data.map(item => {
            const issetIndex = items.findIndex(i => {
              return (
                i.dotacaoUuid == item.dotacaoUuid &&
                i.item.uuid == item.item.uuid
              )
            })
            return issetIndex
          })
          return of(indexes)
        }),
      ),
    ]).pipe(
      take(1),
      switchMap(([items, indexes]) => {
        data.forEach((item, index) => {
          if (indexes[index] >= 0) {
            items[indexes[index]].quantidade += item.quantidade
          } else {
            items.push(item)
          }
        })
        return of(items)
      }),
      tap(items => this.store$.next(items)),
    )
  }

  removeByKeys(keys: string[]) {
    return combineLatest([
      this.store$,
      this.store$.pipe(
        switchMap(items => {
          const indexes = items.reduce((acc, item, index) => {
            if (keys.includes(item.uuid)) acc.push(index)
            return acc
          }, [] as number[])
          return of(indexes)
        }),
      ),
    ]).pipe(
      take(1),
      switchMap(([items, indexes]) => {
        indexes
          .sort()
          .reverse()
          .forEach(index => items.splice(index, 1))
        return of(items)
      }),
      tap(items => this.store$.next(items)),
    )
  }

  public putBatch(
    items: DotationItemInterface[],
    uuid: string,
    uri: string = 'solicitacao',
  ) {
    return this.http.put<ResponseDto<DotationItemInterface>>(
      `licitacao/${uri}/${uuid}/dotacao_item/lote`,
      items,
    )
  }

  public deleteBatch(uuid: string, uri: string = 'solicitacao') {
    return this.http.delete<ResponseDto<DotationItemInterface>>(
      `${uri}/${uuid}/dotacao_item`,
    )
  }
}
