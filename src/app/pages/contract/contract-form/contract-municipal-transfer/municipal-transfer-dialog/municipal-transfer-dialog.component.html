<eqp-nebular-dialog
  id="municipal-trannfer-dialog"
  [dialogTitle]="dialogTitle"
  [spinnerStatus]="'info'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-municipal-trannfer-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="!model.valid"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-municipal-trannfer-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>

<ng-container [formGroup]="model">
<div class="row">
  <div class="col">
    <eqp-nebular-input
          [style]="'basic'"
          [type]="'string'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="exercicio"
          name="exercicio"
          label="Exercício"
          [placeholder]="''"
          [disabled]="true"
        >
        </eqp-nebular-input>
  </div>
  <div class="col">
    <eqp-nebular-input
    [style]="'basic'"
    [type]="'number'"
    [size]="'small'"
    [shape]="'rectangle'"
    formControlName="codigo"
    name="codigo"
    label="Número"
    [placeholder]="''"
    [disabled]="true"
  >
  </eqp-nebular-input>
  </div>
  <div class="col">
    <label class="label">Transferência Municipal</label>
          <eqp-nebular-search-input
            [placeholder]="municipalTransfer?.codigo || 'Transferência Municipal'"
            (onSearch)="onMunicipalTransferSearchInput($event)"
            (onButtonClick)="onMunicipalTransferSearchDialog()"
          ></eqp-nebular-search-input>
  </div>
  <div class="col">
    <eqp-nebular-input
      [style]="'date'"
      [size]="'small'"
      [shape]="'rectangle'"
      formControlName="dataInclusao"
      name="dataInclusao"
      label="Data de Inclusão TCE"
      placeholder="00/00/0000"
    >
      </eqp-nebular-input>
  </div>
</div>
</ng-container>

</eqp-nebular-dialog>
