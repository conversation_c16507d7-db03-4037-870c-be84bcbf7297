import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MunicipalTransferDialogComponent } from './municipal-transfer-dialog.component';

describe('MunicipalTransferDialogComponent', () => {
  let component: MunicipalTransferDialogComponent;
  let fixture: ComponentFixture<MunicipalTransferDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MunicipalTransferDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MunicipalTransferDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
