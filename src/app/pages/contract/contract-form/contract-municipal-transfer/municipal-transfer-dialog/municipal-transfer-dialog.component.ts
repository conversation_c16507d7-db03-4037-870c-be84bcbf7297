import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component';
import { NbDialogRef, NbDialogService } from '@nebular/theme';
import { MunicipalTransferAssociationInterface, MunicipalTransferInterface } from '@pages/contract/interfaces/municipal-transfer';
import { MunicipalTransferService } from '@pages/contract/services/municipal-transfer.service';
import { dateToMoment } from '@pages/shared/helpers/parsers.helper';

@Component({
  selector: 'eqp-municipal-transfer-dialog',
  templateUrl: './municipal-transfer-dialog.component.html',
  styleUrls: ['./municipal-transfer-dialog.component.scss']
})
export class MunicipalTransferDialogComponent implements OnInit {
  @Input() dialogTitle: string = 'Transferência Municipal'
  @Input() initialValue: MunicipalTransferAssociationInterface

  model: FormGroup
  loading: boolean = false

  municipalTransferData: MunicipalTransferInterface[] = []
  municipalTransfer: MunicipalTransferInterface

  municipalTransferMap: { [index: string]: MunicipalTransferInterface } = {}

  constructor(
    private dialogRef: NbDialogRef<MunicipalTransferDialogComponent>,
    private dialogService: NbDialogService,
    private builder: FormBuilder,
    private municipalTransferService: MunicipalTransferService,
    private toastr: ToastrService,
  ) { }

  ngOnInit(): void {
    this.model = this.getNewModel()
    if(this.initialValue) {
      const dto = {
        ...this.initialValue,
      dataInclusao: dateToMoment(this.initialValue.dataInclusao)
      }
      this.model.patchValue(dto) }

  }

  confirm() {
    const dto = this.model.getRawValue()
    this.dialogRef.close(dto)
  }

  dispose() {
    this.dialogRef.close()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      transferenciaMunicipalUuid: [],
      exercicio: [],
      codigo: [],
      transferenciaMunicipal: [undefined, [Validators.required]],
      dataInclusao: [],
    })
  }

  async onMunicipalTransferSearchInput(event: any) {
    const index = this.municipalTransferData.findIndex(municipalTransfer => municipalTransfer == event)
    if (index >= 0) {
      this.model.get('transferenciaMunicipalUuid').setValue(this.municipalTransferData[index].uuid)
      this.municipalTransfer = this.municipalTransferData[index]
    } else {
      this.model.get('transferenciaMunicipalUuid').reset()
      this.municipalTransfer = null
    }
  }


  async onMunicipalTransferSearchDialog() {
    this.loading = true
    this.municipalTransferData = await this.municipalTransferService.syncFetchData(
      this.municipalTransferData,
      this.toastr
    )
    this.loading = false

    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar transferência municipal'
    dialogRef.componentRef.instance.dataGrid = this.municipalTransferData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Exercício',
        dataField: 'exercicio',
      },
      {
        caption: 'Número',
        dataField: 'codigo',
        dataType: 'number'
      },
    ]
    dialogRef.onClose.subscribe(res => {
     if(res) {
       this.model.get('transferenciaMunicipal').patchValue(res)
       this.municipalTransferData = res
     }
    })

  }


}
