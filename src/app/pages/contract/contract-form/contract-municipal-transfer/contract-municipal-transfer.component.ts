import { Component, Input, OnInit } from '@angular/core';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { DxColumnInterface } from '@design-tools/interfaces/column-interface';
import { NbDialogService } from '@nebular/theme';
import { MunicipalTransferAssociationInterface, MunicipalTransferInterface } from '@pages/contract/interfaces/municipal-transfer';
import { MunicipalTransferService } from '@pages/contract/services/municipal-transfer.service';
import { allMomentToDate } from '@pages/shared/helpers/parsers.helper';
import { finalize, take } from 'rxjs/operators';
import { MunicipalTransferDialogComponent } from './municipal-transfer-dialog/municipal-transfer-dialog.component';

@Component({
  selector: 'eqp-contract-municipal-transfer',
  templateUrl: './contract-municipal-transfer.component.html',
  styleUrls: ['./contract-municipal-transfer.component.scss']
})
export class ContractMunicipalTransferComponent implements OnInit {
  @Input() uuid: string
  loading: boolean = false

  municipalTransferColumnsTemplate: DxColumnInterface[] = []

  gridData: MunicipalTransferAssociationInterface[] = []

  constructor(
    private dialogService: NbDialogService,
    private toastr: ToastrService,
    private service: MunicipalTransferService
  ) { }

  ngOnInit(): void {
    this.fetchGrid()
    this.municipalTransferColumnsTemplate = this.getMunicipalTransferColumnsTemplate()
  }

  getMunicipalTransferColumnsTemplate() {
    const template: DxColumnInterface[] = [
      {
        caption: 'Exercício',
        dataField: 'exercicio'
      },
      {
        caption: 'Número',
        dataField: 'codigo',
        dataType: 'number'
      },
      {
        caption: 'Transferência Municipal',
        dataField: 'transferenciaMunicipal'
      },
      {
        caption: 'Data de Inclusão TCE',
        dataField: 'dataInclusao',
        dataType: 'date'
      },
    ]
    return template
  }

  public fetchGrid(): void {
    this.loading = true
    this.service
      .get(this.uuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        data => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  prepare(data: Partial<MunicipalTransferAssociationInterface>): MunicipalTransferAssociationInterface {
    const formData = allMomentToDate(data)
    return formData as MunicipalTransferAssociationInterface
  }


  onMunicipalTranferRowPrepared(event?: any) {
    if (event.isEditing || event.isNewRow) {
      const dialogRef = this.dialogService.open(MunicipalTransferDialogComponent, {
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      dialogRef.componentRef.instance.dialogTitle = 'Inserir Transferência Municipal'
      if (!event.isNewRow)
      dialogRef.componentRef.instance.initialValue = event.data
      dialogRef.onClose.subscribe(res => {
        if (res) {
          const dto = this.prepare(res) as MunicipalTransferAssociationInterface
          if(!event.isNewRow) {
            this.service
            .put(dto, dto.uuid, this.uuid)
            .pipe(take(1))
            .subscribe(
              res => {
                event.data = res
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Transferência municipal atualizada com sucesso!'
                })
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao atualizar transferência municipal!',
                })
              },
            )
          } else {
            this.service
            .post(this.uuid, dto)
            .pipe(take(1))
            .subscribe(
              res => {
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Transferência municipal cadastrada com sucesso!'
                })
                this.fetchGrid()
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao cadastrar transferência municipal!',
                })
              },
            )
          }
          Object.keys(res).forEach(key => (event.data[key] = res[key]))
          event.component.saveEditData()
        } else {
          event.component.cancelEditData()
        }
      })
    }
  }

  public remove(event: any): void {
    this.loading = true
    this.service
      .delete(event.data.uuid, this.uuid)
      .pipe(take(1))
      .subscribe({
        error: e => {
          this.toastr.send({
            error: true,
            title: 'Erro',
            message: e.message || 'Falha ao deletar a transferência municipal!',
          })
        },
        next: () => {
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: 'Transferência municipal deletada com sucesso!',
          })
        },
        complete: () => (this.loading = false),
      })
  }

}



