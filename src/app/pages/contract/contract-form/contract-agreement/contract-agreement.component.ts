import { Component, Injector, Input, OnInit } from '@angular/core'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { AgreementInterface } from '@pages/contract/interfaces/agreement'
import { AgreementTypeInterface } from '@pages/contract/interfaces/agreement-type'
import { AgreementService } from '@pages/contract/services/agreement.service'
import { CrudComponent } from '@pages/shared/crud/crud-component'
import { forkJoin } from 'rxjs'
import { finalize } from 'rxjs/operators'

@Component({
  selector: 'eqp-contract-agreement',
  templateUrl: './contract-agreement.component.html',
  styleUrls: ['./contract-agreement.component.scss'],
})
export class ContractAgreementComponent
  extends CrudComponent<AgreementInterface>
  implements OnInit
{
  @Input() contractUuid: string
  @Input() agreements: AgreementInterface[] = []

  agreementTypes: AgreementTypeInterface[] = []

  columnsTemplate: DxColumnInterface[] = []

  constructor(
    protected service: AgreementService,
    protected injector: Injector,
  ) {
    super(service, 'convenio', 'Convênio', 'Convênios', injector)
  }

  ngOnInit(): void {
    this.loadSelectTemplates()
    this.columnsTemplate = this.getColumnsTemplate()
  }

  loadSelectTemplates() {
    this.loading = true
    forkJoin([
      this.service.getAgreementTypes()
    ]).pipe(
      finalize(() => this.loading = false),
    ).subscribe(([agreementTypes]) => {
      this.agreementTypes = agreementTypes.dados
    })
  }

  prepare<AgreementInterface>(formData: any): AgreementInterface {
    return {
      ...formData,
      contratoUuid: this.contractUuid,
    } as AgreementInterface
  }

  getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Exercício',
        dataField: 'exercicio',
        dataType: 'number',
      },
      {
        caption: 'Número',
        dataField: 'numero',
      },
      {
        caption: 'Nome',
        dataField: 'tipoConvenioUuid',
        lookup: {
          dataSource: this.agreementTypes,
          valueExpr: 'uuid',
          displayExpr: 'nome',
        },
      },
      {
        caption: 'Data inclusão (TCE)',
        dataField: 'dataInclusaoTce',
        dataType: 'date',
      },
      {
        caption: 'Ações',
        type: 'buttons',
        dataField: 'uuid',
        buttons: [
          {
            name: 'edit',
            icon: 'fas fa-edit',
          },
          {
            name: 'delete',
            icon: 'fas fa-trash-alt',
          },
        ],
      },
    ]
    return template
  }
}
