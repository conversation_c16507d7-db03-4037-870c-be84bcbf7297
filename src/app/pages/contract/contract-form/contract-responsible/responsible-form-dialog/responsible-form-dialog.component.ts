import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto';
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component';
import { NbDialogRef, NbDialogService } from '@nebular/theme';
import { PersonInterface } from '@pages/contract/interfaces/person';
import { ResponsibleInterface } from '@pages/contract/interfaces/responsible';
import { PersonService } from '@pages/contract/services/person.service';
import { ResponsibleService } from '@pages/contract/services/responsible.service';
import { ContractPreLoadInterface } from '@pages/pre-load';
import { PreLoadService } from '@pages/pre-load.service';
import { dateToMoment, enumsToSelectDto } from '@pages/shared/helpers/parsers.helper';
import { Subscription } from 'rxjs';

@Component({
  selector: 'eqp-responsible-form-dialog',
  templateUrl: './responsible-form-dialog.component.html',
  styleUrls: ['./responsible-form-dialog.component.scss']
})
export class ResponsibleFormDialogComponent implements OnInit {

  @Input() dialogTitle: string = 'Responsável'
  @Input() initialValue: ResponsibleInterface
  loading = false
  model: FormGroup
  personsData: PersonInterface[] = []
  person: PersonInterface
  tipoResponsavelTemplate: NebularSelectDto[] = []
  personSbs: Subscription

  responsiblesMap: { [index: string]: ResponsibleInterface } = {}

  constructor(
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ResponsibleFormDialogComponent>,
    private dialogService: NbDialogService,
    private preLoadService: PreLoadService,
    private personService: PersonService,
    private toastr: ToastrService,
    private responsibleService: ResponsibleService
  ) {}

  get pessoa() {
    return this.model.get('pessoa')?.value
  }

  ngOnInit(): void {
    this.fetchLoadData()
    this.model = this.getNewModel()
    if(this.initialValue) {
    const dto = {
      ...this.initialValue,
    dataInclusaoTce: dateToMoment(this.initialValue.dataInclusaoTce)
    }
    this.model.patchValue(dto) }
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      pessoaUuid: [],
      tipoResponsavel: [],
      pessoa: [],
      dataInclusaoTce: [],
    })
  }

  confirm() {
    const dto = this.model.getRawValue()
    dto['responsavel'] = this.responsiblesMap[dto]
    this.dialogRef.close(dto)
  }

  dispose() {
    this.dialogRef.close()
  }

  async onPersonSearchInput(event: any) {
    const index = this.personsData.findIndex(person => person.dadoPessoal == event)
    if (index >= 0) {
      this.model.get('pessoaUuid').setValue(this.personsData[index].uuid)
      this.person = this.personsData[index]
    } else {
      this.model.get('pessoaUuid').reset()
      this.person = null
    }
  }

  async onPersonSearchDialog() {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar pessoa'
    dialogRef.componentRef.instance.dataGrid = this.personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
        visible: false
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('pessoa').patchValue(res)
        this.person = res
      }
    })
  }

  fetchLoadData() {
    const contractPreLoad = this.preLoadService
      .contract as ContractPreLoadInterface
      this.tipoResponsavelTemplate = enumsToSelectDto(
        contractPreLoad.tiposResponsavel || []
      )
  }

}
