import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ResponsibleFormDialogComponent } from './responsible-form-dialog.component';

describe('ResponsibleFormDialogComponent', () => {
  let component: ResponsibleFormDialogComponent;
  let fixture: ComponentFixture<ResponsibleFormDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ResponsibleFormDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ResponsibleFormDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
