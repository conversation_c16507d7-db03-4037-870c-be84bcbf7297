<eqp-nebular-dialog
  id="product-form-dialog"
  [dialogTitle]="dialogTitle"
  [spinnerStatus]="'info'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonIcon]="'fas fa-save'"
  [rightFirstButtonId]="'confirm-product-form-dialog-button'"
  [rightFirstButtonTitle]="'Confirmar'"
  [rightFirstButtonDisabled]="!model.valid"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Cancelar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo-alt'"
  [bottomLeftButtonId]="'cancel-product-form-dialog-button'"
  [bottomLeftButtonTitle]="'Cancelar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>

<ng-container [formGroup]="model">
  <div class="row">
      <div class="col">
        <eqp-nebular-select
           formControlName="tipoResponsavel"
            label="Tipo do Responsável"
            [size]="'small'"
            [selectValues]="tipoResponsavelTemplate"
            ></eqp-nebular-select>
        </div>
       <div class="col">
         <label class="label">Pessoa</label>
          <eqp-nebular-search-input
          (onSearch)="onPersonSearchInput($event)"
          (onButtonClick)="onPersonSearchDialog()"
          [placeholder]="pessoa?.dadoPessoal.nome || 'Pessoa'"
          ></eqp-nebular-search-input>
        </div>
        <div class="col">
          <eqp-field-date
            formControlName="dataInclusaoTce"
            name="dataInclusaoTce"
            label="Data Inclusão TCE"
          ></eqp-field-date>
        </div>
      </div>
  </ng-container>
</eqp-nebular-dialog>
