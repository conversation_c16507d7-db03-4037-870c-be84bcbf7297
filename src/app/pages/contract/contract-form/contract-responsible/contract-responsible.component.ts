import { Component, Injector, Input, OnInit } from '@angular/core';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { DxColumnInterface } from '@design-tools/interfaces/column-interface';
import { NbDialogService } from '@nebular/theme';
import { PersonInterface } from '@pages/contract/interfaces/person';
import { ResponsibleInterface } from '@pages/contract/interfaces/responsible';
import { ResponsibleService } from '@pages/contract/services/responsible.service';
import { ContractPreLoadInterface } from '@pages/pre-load';
import { PreLoadService } from '@pages/pre-load.service';
import { allMomentToDate, dateToMoment, enumsToSelectDto } from '@pages/shared/helpers/parsers.helper';
import { Subscription } from 'rxjs';
import { finalize, take } from 'rxjs/operators';
import { ResponsibleFormDialogComponent } from './responsible-form-dialog/responsible-form-dialog.component';
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto';

@Component({
  selector: 'eqp-contract-responsible',
  templateUrl: './contract-responsible.component.html',
  styleUrls: ['./contract-responsible.component.scss']
})
export class ContractResponsibleComponent implements OnInit {
  @Input() uuid: string
  loading: boolean = false

  protected subscription: Subscription


  responsibleColumnsTemplate: DxColumnInterface[] = []

  gridData: ResponsibleInterface

  tipoResponsavelTemplate?: NebularSelectDto[] = []

  personSbs: Subscription
  persons: PersonInterface[] = []

  constructor(
    protected responsibleService: ResponsibleService,
    private dialogService: NbDialogService,
    protected service: ResponsibleService,
    private preLoadService: PreLoadService,
    private toastr: ToastrService,
    ){}

  ngOnInit(): void {
    this.fetchGrid()
    this.getResponsibleType()
    this.responsibleColumnsTemplate = this.getResponsibleColumnsTemplate()
  }

  prepare(data: Partial<ResponsibleInterface>): ResponsibleInterface {
    const formData = allMomentToDate(data)
    formData.pessoaUuid = data.pessoa.uuid
    return formData as ResponsibleInterface
  }

  public fetchGrid(): void {
    this.loading = true
    this.service
      .get(this.uuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        data => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  getResponsibleType() {
    const contractPreLoad = this.preLoadService
      .contract as ContractPreLoadInterface
      this.tipoResponsavelTemplate = enumsToSelectDto(
        contractPreLoad.tiposResponsavel || []
      )
  }

  getResponsibleColumnsTemplate() {
    const template: DxColumnInterface[] = [
      {
        caption: 'Tipo do Responsável',
        dataField: 'tipoResponsavel',
        lookup: {
          dataSource: this.tipoResponsavelTemplate,
          valueExpr: 'valor',
          displayExpr: 'texto'
        }
      },
      {
        caption: 'Pessoa',
        dataField: 'pessoa.dadoPessoal.nome'
      },
      {
        caption: 'Data de Inclusão TCE',
        dataField: 'dataInclusaoTce',
        dataType: 'date'
      },
    ]
    return template
  }

  onResponsibleRowPrepared(event?: any) {
    if (event.isEditing || event.isNewRow) {
      const dialogRef = this.dialogService.open(ResponsibleFormDialogComponent, {
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      dialogRef.componentRef.instance.dialogTitle = 'Inserir Responsável'
      if (!event.isNewRow)
      dialogRef.componentRef.instance.initialValue = event.data
      dialogRef.onClose.subscribe(res => {
        if (res) {
          const dto = this.prepare(res) as ResponsibleInterface
          if(!event.isNewRow) {
            this.responsibleService
            .put(dto, dto.uuid, this.uuid)
            .pipe(take(1))
            .subscribe(
              res => {
                event.data = res
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Responsável atualizado com sucesso!'
                })
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao atualizar responsável!',
                })
              },
            )
          } else {
            this.responsibleService
            .post(this.uuid, dto)
            .pipe(take(1))
            .subscribe(
              res => {
                event.data = res
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Responsável cadastrado com sucesso!'
                })
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao cadastrar responsável!',
                })
              },
            )
          }
          Object.keys(res).forEach(key => (event.data[key] = res[key]))
          event.component.saveEditData()
        } else {
          event.component.cancelEditData()
        }
      })
    }
  }

public remove(event: any): void {
  this.loading = true
  this.subscription = this.service.delete(event.data.uuid, this.uuid)
  .subscribe({
    error: e => {
      this.toastr.send({
        error: true,
        title: 'Erro',
        message: e.message || "Falha ao deletar o responsável",
      })
    },
    next: () => {
      this.toastr.send({
        success: true,
        title: 'Sucesso',
        message: "Responsável deletado com sucesso!",
      })
    },
    complete: () => (this.loading = false),
  })
}


}
