import { Component, Injector, Input, OnInit } from '@angular/core';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { DxColumnInterface } from '@design-tools/interfaces/column-interface';
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto';
import { OccurrencyInterface } from '@pages/contract/interfaces/occurrency';
import { OccurrencyService } from '@pages/contract/services/occurrency.service';
import { ContractPreLoadInterface } from '@pages/pre-load';
import { PreLoadService } from '@pages/pre-load.service';
import { enumsToSelectDto } from '@pages/shared/helpers/parsers.helper';
import { Subscription } from 'rxjs';
import { finalize, take } from 'rxjs/operators';

@Component({
  selector: 'eqp-contract-occurrency',
  templateUrl: './contract-occurrency.component.html',
  styleUrls: ['./contract-occurrency.component.scss']
})
export class ContractOccurrencyComponent implements OnInit {
  @Input() uuid: string

  occurrences?: OccurrencyInterface[] = []

  tipoOcorrenciaTemplate: NebularSelectDto[] = []

  occurrencyColumnsTemplate: DxColumnInterface[] = []

  public gridData: OccurrencyInterface

  private subscription: Subscription
  public loading: boolean = false

  constructor(
    private preLoadService: PreLoadService,
    private toastr: ToastrService,
    private occurrencyService: OccurrencyService,
    protected injector: Injector
  ) {}

  ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  ngOnInit(): void {
    this.fetchLoadData()
    this.occurrencyColumnsTemplate = this.getOccurrencyColumnsTemplate()
  }

  fetchGrid() {
    this.subscription = this.occurrencyService
      .get(this.uuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        data => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  fetchLoadData() {
    const contractPreLoad = this.preLoadService
      .contract as ContractPreLoadInterface
      this.tipoOcorrenciaTemplate = enumsToSelectDto(
        contractPreLoad.tiposOcorrencia || []
      )
  }

  prepare<OccurrencyInterface>(formData: any): OccurrencyInterface {
    return {
      ...formData,
      uuid: this.uuid,
    } as OccurrencyInterface
  }

  getOccurrencyColumnsTemplate() {
    const template: DxColumnInterface[] = [
      {
        caption: 'Tipo Ocorrência',
        dataField: 'tipoOcorrencia',
        lookup: {
          dataSource: this.tipoOcorrenciaTemplate,
          valueExpr: 'valor',
          displayExpr: 'texto',
        },
        formItem: {
          isRequired: true
        }
      },
      {
        caption: 'Data/Hora',
        dataField: 'dataHora',
        dataType: 'datetime',
        formItem: {
          isRequired: true
        }
      },
      {
        caption: 'Descrição',
        dataField: 'descricao'
      },
    ]
    return template
  }


  create(event: any): void {
    const dto = event.data
    this.occurrencyService
            .post(this.uuid, dto)
            .pipe(take(1))
            .subscribe(
              res => {
                event.data = res
                this.toastr.send({
                  success: true,
                  title: 'Sucesso',
                  message: 'Ocorrência cadastrada com sucesso!'
                })
              },
              error => {
                this.toastr.send({
                  error: true,
                  title: 'Error',
                  message: error.message || 'Falha ao cadastrar ocorrência!',
                })
              }
            )}

  update(event: any): void {
    const { codigo, orgaoOficial, dataPublicacao, dataInclusaoTCE } = event.newData

    const dto = {
      uuid: event.oldData.uuid,
      codigo: codigo ? codigo : event.oldData.codigo,
      orgaoOficial: orgaoOficial ? orgaoOficial : event.oldData.orgaoOficial,
      dataPublicacao: dataPublicacao ? dataPublicacao : event.oldData.dataPublicacao,
      dataInclusaoTCE: dataInclusaoTCE ? dataInclusaoTCE : event.oldData.dataInclusaoTCE,
    }
      this.occurrencyService
      .put(dto, dto.uuid, this.uuid)
      .pipe(take(1))
      .subscribe(
        res => {
          event.data = res
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: 'Ocorrência atualizada com sucesso!'
          })
        },
        error => {
          this.toastr.send({
            error: true,
            title: 'Error',
            message: error.message || 'Falha ao atualizar ocorrência!',
          })
        },
      )
    }

    public remove(event: any): void {
      this.loading = true
      this.occurrencyService
      .delete(event.data.uuid, this.uuid)
      .pipe(take(1))
      .subscribe({
        error: e => {
          this.toastr.send({
            error: true,
            title: 'Erro',
            message: e.message || "Falha ao deletar o ocorrência",
          })
        },
        next: () => {
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: "Ocorrência deletada com sucesso!",
          })
        },
        complete: () => (this.loading = false),
      })
    }


}
