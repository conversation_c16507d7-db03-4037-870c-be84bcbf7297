import { Component, OnInit } from '@angular/core'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { ManagerInterface } from './../../interfaces/manager'

@Component({
  selector: 'eqp-contract-manager',
  templateUrl: './contract-manager.component.html',
  styleUrls: ['./contract-manager.component.scss'],
})
export class ContractManagerComponent implements OnInit {
  managers?: ManagerInterface[] = []

  managerColumnsTemplate: DxColumnInterface[] = []

  constructor() {}

  ngOnInit(): void {
    this.managerColumnsTemplate = this.getManagerColumnsTemplate()
  }

  getManagerColumnsTemplate() {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'codigo',
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
      {
        caption: 'Data início',
        dataField: 'inicio',
        dataType: 'date',
        visible: false,
      },
      {
        caption: 'Data fim',
        dataField: 'fim',
        dataType: 'date',
        visible: false,
      },
    ]
    return template
  }
}
