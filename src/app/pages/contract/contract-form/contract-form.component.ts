import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelectDto } from '@design-tools/interfaces/nebular-select-dto'
import { NbDialogService } from '@nebular/theme'
import { ContractPreLoadInterface } from '@pages/pre-load'
import {
  allMomentToDate,
  enumsToSelectDto,
} from '@pages/shared/helpers/parsers.helper'
import { forkJoin } from 'rxjs'
import { finalize, first, pluck, take } from 'rxjs/operators'
import { LocalInterface } from '../interfaces/local'
import { PersonInterface } from '../interfaces/person'
import { ContractingService } from '../services/contracting.service'
import { LocalService } from '../services/local.service'
import { PersonService } from '../services/person.service'
import { PreLoadService } from './../../pre-load.service'
import { ProductItemInterface } from './../../shared/interfaces/product-item'
import { ExpenseAccountService } from './../../shared/services/expense-account.service'
import { ProductService } from './../../shared/services/product.service'
import { UnitMeasurementService } from './../../shared/services/unit-measurement.service'
import { ContractInterface } from './../interfaces/contract'
import { ContractingInterface } from './../interfaces/contracting'
import { ContractService } from './../services/contract.service'
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component'
@Component({
  selector: 'eqp-contract-form',
  templateUrl: './contract-form.component.html',
  styleUrls: ['./contract-form.component.scss'],
})
export class ContractFormComponent implements OnInit {
  pageTitle = 'Contrato'
  loading = false
  uuid?: string
  model?: FormGroup
  itemsData: ProductItemInterface[] = []
  touchedTabs: Set<string> = new Set<string>([])

  tipoAtoContratualTemplate: NebularSelectDto[] = []
  tipoContratoTemplate: NebularSelectDto[] = []
  tipoGarantiaTemplate: NebularSelectDto[] = []
  tipoRegimeExecucaoTemplate: NebularSelectDto[] = []
  tipoMultaTemplate: NebularSelectDto[] = []
  tipoPrevisaoSubcontratacaoTemplate: NebularSelectDto[] = []
  tipoFornecimentoMedioTemplate: NebularSelectDto[] = []

  licitationsData: ContractingInterface[] = []
  localsData: LocalInterface[] = []
  personsData: PersonInterface[] = []

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private builder: FormBuilder,
    private toastr: ToastrService,
    private contractService: ContractService,
    private dialogService: NbDialogService,
    private contractingService: ContractingService,
    private localService: LocalService,
    private personService: PersonService,
    private preLoadService: PreLoadService,
    private productService: ProductService,
    private expenseAccountService: ExpenseAccountService,
    private unitMeasurementService: UnitMeasurementService,
  ) {}

  get licitacao() {
    return this.model.get('licitacao')?.value
  }

  get local() {
    return this.model.get('local')?.value
  }

  get fornecedor() {
    return this.model.get('fornecedor')?.value
  }

  get representante() {
    return this.model.get('representante')?.value
  }

  get recursos() {
    return this.model.get('recursos')?.value
  }

  get documentos() {
    return this.model.get('documentos')?.value
  }

  ngOnInit(): void {
    this.fetchLoadData()
    this.model = this.getNewModel()
    this.route.params.pipe(first()).subscribe(param => {
      this.uuid = param.uuid
    })
    if (!this.uuid) {
      this.pageTitle += ': Novo'
    } else {
      // this.contractService
      //   .getIndividual(this.uuid)
      //   .pipe(take(1), pluck('dados'))
      //   .subscribe(res => {
      //     this.pageTitle += ': ' + res.codigo
      //     this.loadData(res)
      //   })
    }
  }

  onChangeTab(event: any) {
    const { tabId } = event
    this.touchedTabs.add(tabId)
  }

  tabWasTouched(tabId: string) {
    return this.touchedTabs.has(tabId)
  }

  fetchLoadData() {
    this.loading = true
    forkJoin([
      this.unitMeasurementService.get(),
      this.productService.get(),
      this.expenseAccountService.get(),
    ])
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(resAll => {
        this.unitMeasurementService.store = resAll[0].dados
        this.productService.store = resAll[1].dados
        this.expenseAccountService.store = resAll[2].dados
      })
    const contractPreLoad = this.preLoadService
      .contract as ContractPreLoadInterface
    this.tipoAtoContratualTemplate = enumsToSelectDto(contractPreLoad.tiposAto)
    this.tipoContratoTemplate = enumsToSelectDto(contractPreLoad.tiposContrato)
    this.tipoGarantiaTemplate = enumsToSelectDto(
      contractPreLoad.tiposGarantiaContrato,
    )
    this.tipoMultaTemplate = enumsToSelectDto(
      contractPreLoad.tiposMultaContrato,
    )
    this.tipoRegimeExecucaoTemplate = enumsToSelectDto(
      contractPreLoad.tiposRegimeExecucaoContrato,
    )
    this.tipoPrevisaoSubcontratacaoTemplate = [
      {
        texto: 'Existe previsão',
        valor: 'ISSET',
      },
      {
        texto: 'Não existe previsão',
        valor: 'NOT_ISSET',
      },
    ]
    this.tipoFornecimentoMedioTemplate = [
      {
        texto: 'Imediato',
        valor: 'IMMEDIATE',
      },
      {
        texto: 'Não imediato',
        valor: 'NOT_IMMEDIATE',
      },
    ]
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      codigo: [],
      exercicio: [],
      entidade: [],
      tipoAtoContratual: [],
      numeroTce: [],
      inclusaoTce: [],
      inclusaoTceParteContrato: [],
      semLicitacao: [],
      licitacao: [],
      valorOriginal: [],
      acrescimo: [],
      anulacao: [],
      totalAditivos: [],
      valorAtualizado: [],
      vigenciaOriginal: this.builder.group({
        uuid: [],
        inicio: [],
        prazo: [],
        tipoPrazo: [],
        fim: [],
      }),
      periodoExecucao: this.builder.group({
        uuid: [],
        inicio: [],
        prazo: [],
        tipoPrazo: [],
        fim: [],
      }),
      tipoContrato: [],
      fundamentoLegal: [],
      formaPagamento: [],
      formaPagamentoTce: [],
      dataEntrega: [],
      local: [],
      pais: [],
      descricaoGarantia: [],
      tipoGarantia: [],
      fornecedor: [],
      representante: [],
      tipoRegimeExecucao: [],
      previsaoSubcontratacao: [],
      fornecimentoMedio: [],
      recursos: this.builder.group({
        uuid: [],
        proprios: [],
        estaduais: [],
        federais: [],
        operacoesCredito: [],
      }),
      assinatura: this.builder.group({
        uuid: [],
        data: [],
        contratante: [],
        contratada: [],
        testemunhaContratante: [],
        testemunhaContratada: [],
        publicarInternet: [],
        atendimentoCovid: [],
      }),
      multa: this.builder.group({
        uuid: [],
        tipo: [],
        descricao: [],
      }),
      previsaoCessaoContratual: [],
      controlarSaldoSeparadamente: [],
      dataProrrogacaoCompras: [],
      bloquearRequisicaoCompra: [],
      itens: [],
      documentos: [],
    })
  }

  back() {
    this.router.navigate(['contratos'])
  }

  submit() {
    if (this.model.valid) {
      let payload = this.prepare(this.model.getRawValue())
      // payload.itens = this.itemsData
      if (!this.uuid) {
        this.contractService
          .post(payload)
          .pipe(take(1), pluck('body', 'dados'))
          .subscribe(
            res => {
              this.toastr.send({
                success: true,
                title: 'Sucesso',
                message: 'Contrato cadastrado com sucesso!',
              })
              this.router.navigate(['contratos', 'form', res.uuid])
            },
            error => {
              this.toastr.send({
                error: true,
                title: 'Error',
                message: error.message,
              })
            },
          )
      } else {
        this.contractService
          .put(payload, this.uuid)
          .pipe(take(1))
          .subscribe(
            _ => {
              this.toastr.send({
                success: true,
                title: 'Sucesso',
                message: 'Contrato atualizado com sucesso!',
              })
              this.back()
            },
            error => {
              this.toastr.send({
                error: true,
                title: 'Error',
                message: error.message,
              })
            },
          )
      }
    }
  }

  prepare(data: Partial<ContractInterface>): ContractInterface {
    const parsedData = allMomentToDate(data)
    return parsedData as ContractInterface
  }

  async loadData(data: ContractInterface) {
    // this.itemsData = data.itens
    this.model.patchValue(data)
  }

  getTotalResources() {
    const {
      proprios = 0,
      estaduais = 0,
      federais = 0,
      operacoesCredito = 0,
    } = this.recursos
    return (
      Number(proprios) +
      Number(estaduais) +
      Number(federais) +
      Number(operacoesCredito)
    )
  }

  async onLicitationInput(event?: any) {
    this.loading = true
    this.licitationsData = await this.contractingService.syncFetchData(
      this.licitationsData,
      this.toastr,
    )
    this.loading = false
    const index = this.licitationsData.findIndex(
      licitation => licitation.codigo == Number(event),
    )
    if (index >= 0) {
      this.model.get('licitacao').patchValue(this.licitationsData[index])
    } else {
      this.model.get('licitacao').reset()
    }
  }

  async onLicitationDialog() {
    this.loading = true
    this.licitationsData = await this.contractingService.syncFetchData(
      this.licitationsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar licitação'
    dialogRef.componentRef.instance.dataGrid = this.licitationsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'codigo',
        dataType: 'number',
      },
      {
        caption: 'Objeto',
        dataField: 'objeto',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('licitacao').patchValue(res)
      }
    })
  }

  async onLocalInput(event?: any) {
    this.loading = true
    this.localsData = await this.localService.syncFetchData(
      this.localsData,
      this.toastr,
    )
    this.loading = false
    const index = this.localsData.findIndex(
      local => local.codigo == Number(event),
    )
    if (index >= 0) {
      this.model.get('local').patchValue(this.localsData[index])
    } else {
      this.model.get('local').reset()
    }
  }

  async onLocalDialog() {
    this.loading = true
    this.localsData = await this.localService.syncFetchData(
      this.localsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar local'
    dialogRef.componentRef.instance.dataGrid = this.localsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'codigo',
        dataType: 'number',
      },
      {
        caption: 'Nome',
        dataField: 'nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('local').patchValue(res)
      }
    })
  }

  async onProviderInput(event?: any) {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const index = this.personsData.findIndex(
      person => person.dadoPessoal.codigo == event,
    )
    if (index >= 0) {
      this.model.get('fornecedor').patchValue(this.personsData[index])
    } else {
      this.model.get('fornecedor').reset()
    }
  }

  async onProviderDialog() {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar fornecedor'
    dialogRef.componentRef.instance.dataGrid = this.personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('fornecedor').patchValue(res)
      }
    })
  }

  async onRepresentativeInput(event?: any) {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const index = this.personsData.findIndex(
      person => person.dadoPessoal.codigo == event,
    )
    if (index >= 0) {
      this.model.get('representante').patchValue(this.personsData[index])
    } else {
      this.model.get('representante').reset()
    }
  }

  async onRepresentativeDialog() {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar representante'
    dialogRef.componentRef.instance.dataGrid = this.personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('representante').patchValue(res)
      }
    })
  }
}
