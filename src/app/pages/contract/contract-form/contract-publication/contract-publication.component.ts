import { Component, Injector, Input, OnInit } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { OfficialOrganInterface } from '@pages/contract/interfaces/official-organ'
import { PublicationInterface } from '@pages/contract/interfaces/publication'
import { OfficialOrganService } from '@pages/contract/services/official-organ.service'
import { PublicationService } from '@pages/contract/services/publication.service'
import {
  allMomentToDate,
  allUtcToMoment,
} from '@pages/shared/helpers/parsers.helper'
import { Subscription } from 'rxjs'
import { finalize, take } from 'rxjs/operators'

@Component({
  selector: 'eqp-contract-publication',
  templateUrl: './contract-publication.component.html',
  styleUrls: ['./contract-publication.component.scss'],
})
export class ContractPublicationComponent implements OnInit {
  @Input() uuid: string
  loading: boolean = false

  publications?: PublicationInterface[] = []

  publicationColumnsTemplate: DxColumnInterface[] = []

  gridData: PublicationInterface[] = []

  initialValue: PublicationInterface

  officialOrganSbs: Subscription

  public orgaoOficial: OfficialOrganInterface[] = []

  constructor(
    protected service: PublicationService,
    protected injector: Injector,
    private toastr: ToastrService,
    private officialOrganService: OfficialOrganService,
  ) {}

  ngOnInit(): void {
    this.fetchGrid()
    this.fetchLoadData()
    this.publicationColumnsTemplate = this.getPublicationColumnsTemplate()
  }

  fetchLoadData() {
    this.officialOrganSbs = this.officialOrganService.get().subscribe(res => {
      this.orgaoOficial = res.dados
    })
  }
  public fetchGrid(): void {
    this.loading = true
    this.service
      .get(this.uuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        data => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  getPublicationColumnsTemplate() {
    const template: DxColumnInterface[] = [
      {
        caption: 'Órgão',
        dataField: 'orgaos',
        lookup: {
          dataSource: this.orgaoOficial,
          valueExpr: 'valor',
          displayExpr: 'texto',
        },
      },
      {
        caption: 'Data de Publicação',
        dataField: 'dataPublicacao',
        dataType: 'date',
      },
      {
        caption: 'Data de Inclusão TCE',
        dataField: 'dataInclusaoTce',
        dataType: 'date',
      },
    ]
    return template
  }

  create(event: any): void {
    const dto = allMomentToDate(allUtcToMoment(event.data))
    this.service
      .post(this.uuid, dto)
      .pipe(take(1))
      .subscribe(
        res => {
          event.data = res
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: 'Publicação cadastrada com sucesso!',
          })
        },
        error => {
          this.toastr.send({
            error: true,
            title: 'Error',
            message: error.message || 'Falha ao cadastrar publicação!',
          })
        },
      )
  }

  update(event: any): void {
    const { codigo, orgaoOficial, dataPublicacao, dataInclusaoTCE } =
      event.newData

    const dto = {
      uuid: event.oldData.uuid,
      codigo: codigo ? codigo : event.oldData.codigo,
      orgaoOficial: orgaoOficial ? orgaoOficial : event.oldData.orgaoOficial,
      dataPublicacao: dataPublicacao
        ? dataPublicacao
        : event.oldData.dataPublicacao,
      dataInclusaoTCE: dataInclusaoTCE
        ? dataInclusaoTCE
        : event.oldData.dataInclusaoTCE,
    }
    this.service
      .put(this.uuid, dto, dto.uuid)
      .pipe(take(1))
      .subscribe(
        res => {
          event.data = res
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: 'Publicação atualizada com sucesso!',
          })
        },
        error => {
          this.toastr.send({
            error: true,
            title: 'Error',
            message: error.message || 'Falha ao atualizar publicação!',
          })
        },
      )
  }

  public remove(event: any): void {
    this.loading = true
    this.service
      .delete(event.data.uuid, this.uuid)
      .pipe(take(1))
      .subscribe({
        error: e => {
          this.toastr.send({
            error: true,
            title: 'Erro',
            message: e.message || 'Falha ao deletar a publicação',
          })
        },
        next: () => {
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message: 'Publicação deletada com sucesso!',
          })
        },
        complete: () => (this.loading = false),
      })
  }
}
