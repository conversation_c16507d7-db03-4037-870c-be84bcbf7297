import { Component, Input, OnInit } from '@angular/core'
import { FormGroup, FormGroupDirective } from '@angular/forms'
import { NbDialogService } from '@nebular/theme'
import { PersonService } from '@pages/contract/services/person.service'
import { ToastrService } from './../../../../@common/services/toastr/toastr.service'
import { PersonInterface } from './../../interfaces/person'
import { SearchDialogComponent } from '@design-tools/search-dialog/search-dialog.component'

@Component({
  selector: 'eqp-contract-signature',
  templateUrl: './contract-signature.component.html',
  styleUrls: ['./contract-signature.component.scss'],
})
export class ContractSignatureComponent implements OnInit {
  loading: boolean
  model: FormGroup

  personsData: PersonInterface[] = []

  @Input() formGroupName: string

  constructor(
    private formGroupDirective: FormGroupDirective,
    private personService: PersonService,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
  ) {}

  get contratante() {
    return this.model.get('contratante')?.value
  }
  get contratada() {
    return this.model.get('contratada')?.value
  }
  get testemunhaContratante() {
    return this.model.get('testemunhaContratante')?.value
  }
  get testemunhaContratada() {
    return this.model.get('testemunhaContratada')?.value
  }

  ngOnInit(): void {
    this.model = this.formGroupDirective.control.get(
      this.formGroupName,
    ) as FormGroup
  }

  async onContractorInput(event?: any) {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const index = this.personsData.findIndex(
      person => person.dadoPessoal.codigo == event,
    )
    if (index >= 0) {
      this.model.get('contratante').patchValue(this.personsData[index])
    } else {
      this.model.get('contratante').reset()
    }
  }

  async onContractorDialog() {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar contratante'
    dialogRef.componentRef.instance.dataGrid = this.personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('contratante').patchValue(res)
      }
    })
  }

  async onHiredInput(event?: any) {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const index = this.personsData.findIndex(
      person => person.dadoPessoal.codigo == event,
    )
    if (index >= 0) {
      this.model.get('contratada').patchValue(this.personsData[index])
    } else {
      this.model.get('contratada').reset()
    }
  }

  async onHiredDialog() {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar contratada'
    dialogRef.componentRef.instance.dataGrid = this.personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('contratada').patchValue(res)
      }
    })
  }

  async onContractorWitnessInput(event: any) {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const index = this.personsData.findIndex(
      person => person.dadoPessoal.codigo == event,
    )
    if (index >= 0) {
      this.model
        .get('testemunhaContratante')
        .patchValue(this.personsData[index])
    } else {
      this.model.get('testemunhaContratante').reset()
    }
  }

  async onContractorWitnessDialog() {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar testemunha 01'
    dialogRef.componentRef.instance.dataGrid = this.personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('testemunhaContratante').patchValue(res)
      }
    })
  }

  async onHiredWitnessInput(event: any) {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const index = this.personsData.findIndex(
      person => person.dadoPessoal.codigo == event,
    )
    if (index >= 0) {
      this.model.get('testemunhaContratada').patchValue(this.personsData[index])
    } else {
      this.model.get('testemunhaContratada').reset()
    }
  }

  async onHiredWitnessDialog() {
    this.loading = true
    this.personsData = await this.personService.syncFetchData(
      this.personsData,
      this.toastr,
    )
    this.loading = false
    const dialogRef = this.dialogService.open(SearchDialogComponent, {})
    dialogRef.componentRef.instance.dialogTitle = 'Selecionar testemunha 02'
    dialogRef.componentRef.instance.dataGrid = this.personsData
    dialogRef.componentRef.instance.columnsTemplate = [
      {
        caption: 'Código',
        dataField: 'dadoPessoal.codigo',
        dataType: 'number',
      },
      {
        caption: 'Nome',
        dataField: 'dadoPessoal.nome',
      },
    ]
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get('testemunhaContratada').patchValue(res)
      }
    })
  }
}
