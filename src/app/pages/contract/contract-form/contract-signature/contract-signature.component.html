<nb-card
  class="border-0"
  [nbSpinner]="loading"
  [formGroup]="model">
  <div class="row justify-content-start">
    <div class="col col-md-6 d-flex">
      <eqp-field-date
        formControlName="data"
        name="data"
        label="Data"
      ></eqp-field-date>
    </div>
    <div class="col col-md-2">
      <eqp-nebular-toggle
        label="Publicar na internet"
        formControlName="publicarInternet"
        title="Publicar na internet"
      ></eqp-nebular-toggle>
    </div>
    <div class="col col-md-2">
      <eqp-nebular-toggle
        label="Atendimento COVID-19"
        formControlName="atendimentoCovid"
        title="Atendimento COVID-19"
      ></eqp-nebular-toggle>
    </div>
  </div>
  <div class="row mt-4">
    <div class="col">
      <label class="label">Contratante</label>
      <div class="d-flex align-items-end">
        <eqp-nebular-search-input
          [placeholder]="contratante?.dadoPessoal?.codigo ||
                          'Código do contratante'"
          (onSearch)="onContractorInput($event)"
          (onButtonClick)="onContractorDialog()"
        ></eqp-nebular-search-input>
        <p class="label ml-2 my-2">
          {{ contratante?.dadoPessoal?.nome ||
                            'Nenhum contratante selecionado' }}
        </p>
      </div>
    </div>
    <div class="col">
      <label class="label">Contratada</label>
      <div class="d-flex align-items-end">
        <eqp-nebular-search-input
          [placeholder]="contratada?.dadoPessoal?.codigo ||
                          'Código da contratada'"
          (onSearch)="onHiredInput($event)"
          (onButtonClick)="onHiredDialog()"
        ></eqp-nebular-search-input>
        <p class="label ml-2 my-2">
          {{ contratada?.dadoPessoal?.nome ||
                            'Nenhuma contratada selecionada' }}
        </p>
      </div>
    </div>
  </div>
  <div class="row mt-4">
    <div class="col">
      <label class="label">Testemunha</label>
      <div class="d-flex align-items-end">
        <eqp-nebular-search-input
          [placeholder]="
            testemunhaContratante?.dadoPessoal?.codigo ||
                          'Código da testemunha'
          "
          (onSearch)="onContractorWitnessInput($event)"
          (onButtonClick)="onContractorWitnessDialog()"
        ></eqp-nebular-search-input>
        <p class="label ml-2 my-2">
          {{ testemunhaContratante?.dadoPessoal?.nome ||
                            'Nenhuma testemunha selecionada' }}
        </p>
      </div>
    </div>
    <div class="col">
      <label class="label">Testemunha</label>
      <div class="d-flex align-items-end">
        <eqp-nebular-search-input
          [placeholder]="testemunhaContratada?.dadoPessoal?.codigo ||
                          'Código da testemunha'"
          (onSearch)="onHiredWitnessInput($event)"
          (onButtonClick)="onHiredWitnessDialog()"
        ></eqp-nebular-search-input>
        <p class="label ml-2 my-2">
          {{ testemunhaContratada?.dadoPessoal?.nome ||
                            'Nenhuma testemunha selecionada' }}
        </p>
      </div>
    </div>
  </div>
</nb-card>
