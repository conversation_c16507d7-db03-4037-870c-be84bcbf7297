<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'primary'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-undo-alt'"
  [topRightButtonId]="'back-to-contract-list'"
  [topRightButtonTitle]="'Voltar para lista'"
  (topRightButtonEmitter)="back()"
>
  <div class="row">
    <div class="col-12">
      <ng-container [formGroup]="model">
        <nb-tabset (changeTab)="onChangeTab($event)">
          <nb-tab tabId="contrato" tabTitle="Contrato" class="p-0">
            <div class="mt-2 d-flex justify-content-end">
              <eqp-nebular-button
                buttonText="Salvar"
                buttonIcon="fas fa-save"
                buttonTitle="Salvar"
                buttonClass="rounded-0"
                buttonType="success"
                buttonId="contractSubmitButtonId"
                [buttonDisabled]="!model.valid || model.pristine"
                (buttonEmitter)="submit()"
                [buttonIconVisible]="true"
                [buttonVisible]="true"
              ></eqp-nebular-button>
            </div>
          </nb-tab>
          <nb-tab tabId="itens" tabTitle="Itens">
            <eqp-contract-items
              [licitation]="licitacao"
              [items]="itemsData"
              (onUpdateItems)="itemsData = $event"
            ></eqp-contract-items>
          </nb-tab>
          <nb-tab
            tabId="dotacoes"
            tabTitle="Dotações"
            [disabled]="!uuid"
            class="p-0"
          >
            <eqp-contract-dotation
              *ngIf="uuid"
              [uuid]="uuid"
            ></eqp-contract-dotation>
          </nb-tab>
          <nb-tab
            tabId="convenios"
            tabTitle="Convênios"
            [disabled]="!uuid"
            class="p-0"
          >
            <eqp-contract-agreement
              *ngIf="uuid"
              [uuid]="uuid"
            ></eqp-contract-agreement>
          </nb-tab>
          <nb-tab
            tabId="gestores"
            tabTitle="Gestores"
            [disabled]="!uuid"
            class="p-0"
          >
            <eqp-contract-manager
              *ngIf="uuid"
              [uuid]="uuid"
            ></eqp-contract-manager>
          </nb-tab>
          <nb-tab
            tabId="atos-contratuais"
            tabTitle="Atos contratuais"
            [disabled]="!uuid"
            class="p-0"
          >
            <eqp-contract-act
              [associatedUuid]="uuid"
              *ngIf="uuid && tabWasTouched('atos-contratuais')"
            ></eqp-contract-act>
          </nb-tab>
          <nb-tab
            tabId="ocorrencias"
            tabTitle="Ocorrências"
            [disabled]="!uuid"
            class="p-0"
          >
            <eqp-contract-occurrency
              *ngIf="uuid"
              [uuid]="uuid"
            ></eqp-contract-occurrency>
          </nb-tab>
          <nb-tab
            tabId="publicacao"
            tabTitle="Publicação"
            [disabled]="!uuid"
            class="p-0"
          >
            <eqp-contract-publication
              *ngIf="uuid"
              [uuid]="uuid"
            ></eqp-contract-publication>
          </nb-tab>
          <nb-tab
            tabId="responsaveis"
            tabTitle="Responsáveis"
            [disabled]="!uuid"
            class="p-0"
          >
            <eqp-contract-responsible
              *ngIf="uuid"
              [uuid]="uuid"
            ></eqp-contract-responsible>
          </nb-tab>
          <nb-tab
            tabTitle="Transferência Municipal"
            [disabled]="!uuid"
            class="p-0"
          >
            <eqp-contract-municipal-transfer
              *ngIf="uuid"
              [uuid]="uuid"
            ></eqp-contract-municipal-transfer>
          </nb-tab>
          <nb-tab
            tabId="documentos"
            [disabled]="!uuid"
            tabTitle="Documentos"
            class="p-0"
          >
            <eqp-document-editor-list
              *ngIf="uuid"
              id="contractDocumentEditorId"
              [system]="'CONTRACT'"
              [associatedUuid]="uuid"
              associatedUri="contrato"
              [documents]="documentos || []"
            ></eqp-document-editor-list>
          </nb-tab>
          <nb-tab tabId="anexos" [disabled]="!uuid" tabTitle="Anexos">
            <eqp-attachment
              *ngIf="uuid"
              id="contractAttachmentId"
              [associatedUuid]="uuid"
              associatedUri="contrato"
            ></eqp-attachment>
          </nb-tab>
        </nb-tabset>
      </ng-container>
    </div>
  </div>
</eqp-standard-page>
