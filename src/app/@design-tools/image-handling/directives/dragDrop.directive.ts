import {
  Directive,
  EventEmitter,
  HostBinding,
  HostListener,
  Output,
} from '@angular/core'
import { Dom<PERSON>anitizer, SafeUrl } from '@angular/platform-browser'

export interface FileHandle {
  file: File
  url: SafeUrl
}

@Directive({
  selector: '[eqpDragDrop]',
})
export class DragDropDirective {
  @Output() files: EventEmitter<FileHandle[]> = new EventEmitter()

  @HostBinding('style.background') private background = '#eee'

  constructor(private sanitizer: <PERSON>Sanitizer) {}

  @HostListener('dragover', ['$event']) public onDragOver(event: DragEvent) {
    event.preventDefault()
    event.stopPropagation()
    this.background = '#999'
  }

  @HostListener('dragleave', ['$event']) public onDragLeave(event: DragEvent) {
    event.preventDefault()
    event.stopPropagation()
    this.background = '#eee'
  }

  @HostListener('drop', ['$event']) public onDrop(event: DragEvent) {
    event.preventDefault()
    event.stopPropagation()
    this.background = '#eee'

    let files: FileHandle[] = []
    for (let i = 0; i < event.dataTransfer.files.length; i++) {
      const file = event.dataTransfer.files[i]
      const url = this.sanitizer.bypassSecurityTrustUrl(
        window.URL.createObjectURL(file),
      )
      files.push({ file, url })
    }
    if (files.length > 0) {
      this.files.emit(files)
    }
  }
}
