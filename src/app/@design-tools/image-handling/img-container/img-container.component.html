<ng-container>
  <div
    [ngClass]="imageShape"
    [ngStyle]="{'width.px': imgWidth ? imgWidth : 160, 'height': imgHeight ? imgHeight : '20vh' }"
    class="uploader-area"
    eqpDragDrop
    (files)="filesDropped($event)"
  >
    <ng-container *ngIf="!croppedImage">
      <span>Arraste uma imagem ou <b
          class="pointer"
          (click)="openAdjuster()"
        >clique
          aqui!</b> </span>
    </ng-container>

    <img [src]="croppedImage">
    <!-- <img
      src="https://signature-generator.com/images/temp/temp2.png"
      alt=""
    > -->

  </div>
</ng-container>
