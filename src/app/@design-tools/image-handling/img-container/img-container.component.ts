import { Component, Input, OnInit } from '@angular/core'
import {
  FileHandle,
} from '@design-tools/image-handling/directives/dragDrop.directive'
import { NbDialogService } from '@nebular/theme'

import { ImgAdjusterComponent } from './../img-adjuster/img-adjuster.component'

@Component({
  selector: 'eqp-img-container',
  templateUrl: './img-container.component.html',
  styleUrls: ['./img-container.component.scss'],
})
export class ImgContainerComponent implements OnInit {
  @Input() public imageShape: 'default' | 'rounded' | 'signature' = 'default'

  @Input() public imgWidth: string
  @Input() public imgHeight: string

  @Input() public adjusterSize: 'small' | 'medium' | 'large' | 'extra-large' =
    'small'

  public files: FileHandle[] = []
  public croppedImage: string

  constructor(private dialogService: NbDialogService) {}

  public ngOnInit(): void {
    console.log(this.imgHeight, this.imgWidth)
  }

  public filesDropped(files: FileHandle[]): void {
    this.files = files
    this.openAdjuster()
  }

  public openAdjuster(): void {
    const dialogRef = this.dialogService.open(ImgAdjusterComponent, {})

    dialogRef.componentRef.instance.originalImage = this.files[0]
    dialogRef.componentRef.instance.adjusterSize = this.adjusterSize

    dialogRef.onClose.subscribe(data => {
      if (data) {
        this.croppedImage = data
        console.log(this.croppedImage)
      } else null
    })
  }
}
