<label
  class="label"
  [attr.aria-label]="label"
  for="{{ formControlName }}"
>{{ label + (required ? ' *' : '') }}</label>
<nb-form-field>
  <input
    #input
    [type]="'text'"
    [readOnly]="true"
    [fullWidth]="true"
    [fieldSize]="'small'"
    [value]="documentData?.filename"
    placeholder=""
    nbInput
    [required]="required"
  />
  <button
    [size]="'small'"
    [title]="'Selecionar arquivo'"
    [disabled]="disabled"
    nbSuffix
    (click)="selectFile()"
    nbButton
    status="primary"
  >
    <nb-icon icon="upload-outline" pack="eva"> </nb-icon>
  </button>
</nb-form-field>
<div
  *ngIf="
    getAbsControl().errors?.required &&
    (getAbsControl()?.touched || !getAbsControl()?.pristine)
  "
  class="invalid-feedback"
  [ngClass]="{ 'd-block': getAbsControl()?.errors }"
>
  <div>{{ label + ' é obrigatório' }}</div>
</div>
