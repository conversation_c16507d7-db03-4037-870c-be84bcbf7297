import { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, inject, Input, Provider } from '@angular/core';
import { ControlValueAccessor, ControlContainer, NG_VALUE_ACCESSOR } from '@angular/forms';
import { FieldFileInterface } from './field-file';

const FIELD_FILE_PROVIDER: Provider = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => FieldFileComponent),
  multi: true
}

@Component({
  selector: 'app-field-file',
  templateUrl: './field-file.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FIELD_FILE_PROVIDER]
})
export class FieldFileComponent implements ControlValueAccessor {

  #cd = inject(ChangeDetectorRef)
  #controlContainer = inject(ControlContainer)

  #documentData: FieldFileInterface = {
    base64: '',
    filename: ''
  }

  @Input() label: string
  @Input() formControlName: string
  @Input() disabled = false
  @Input() required = false

  onChange = (obj: any) => {}
  onTouched = (obj: any) => {}

  get documentData() {
    return this.#documentData
  }

  set documentData(obj: FieldFileInterface) {
    if (obj?.base64 != this.#documentData?.base64) {
      this.#documentData = obj
      this.onChange(obj)
      this.#cd.markForCheck()
    }
  }

  writeValue(obj: FieldFileInterface): void {
    this.documentData = obj
  }

  registerOnChange(fn: any): void {
    this.onChange = fn
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn
  }

  getAbsControl(): any {
    if (this.#controlContainer) {
      return this.#controlContainer.control.get(this.formControlName)
    }
    return null
  }

  selectFile() {
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.onchange = (event: any) => {
      const file = event.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e: any) => {
          const base64Data = e.target.result
          this.documentData = {
            base64: base64Data,
            filename: file.name
          }
          this.#cd.markForCheck()
        }
        reader.readAsDataURL(file)
      }
    }
    fileInput.click()
  }
}

