<div class="form-control-group flex-control center-label">
  <label
    class="label"
    *ngIf="label && !useNebularLabel"
    [attr.aria-label]="label"
    for="{{ name }}"
  >{{ label }}
  </label>
  <nb-toggle
    #model="ngModel"
    [(ngModel)]="currentValue"
    [name]="name"
    [disabled]="disabled"
    [labelPosition]="nebularLabelPosition"
    (ngModelChange)="changeValue($event)"
    [tabIndex]="tabIndex"
  >
  {{ nebularLabel }}
  </nb-toggle>
</div>
