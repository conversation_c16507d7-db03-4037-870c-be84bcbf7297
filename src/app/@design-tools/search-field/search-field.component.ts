import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
} from '@angular/core'
import {
  ControlValueAccessor,
  FormBuilder,
  FormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { DefaultSearchDialogComponent } from '@pages/shared/search/default-search-dialog/default-search-dialog.component'
import { SEARCH_COLUMNS } from '@pages/shared/search/default-search-dialog/search-columns'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  take,
} from 'rxjs/operators'

@Component({
  selector: 'eqp-search-field',
  templateUrl: './search-field.component.html',
  styleUrls: ['./search-field.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: SearchFieldComponent,
      multi: true,
    },
  ],
})
export class SearchFieldComponent implements ControlValueAccessor, OnInit {
  @Input() label: string = 'Texto Padrão'
  @Input() searchColumnsType: string = ''
  @Input() codeLabel: string = 'Código'
  @Input() nameLabel: string = 'Nome'
  @Input() codeKey = 'codigo'
  @Input() uri = 'uri_padrao'
  @Input() filter: any[]
  @Input() sort?: any[]
  @Input() dialogTitle = this.label
  @Input() nameKey: string = 'nome'
  @Input() multipleNames: string[]
  @Input() idKey = 'uuid'
  @Input() hideName = false
  @Input() returnAllData = false
  @Input() hideButton = false
  @Input() disabled = false
  @Input() disabledCodeInput = false
  @Input() required = false
  @Input() displayError = false
  @Input() codeInputWidth = null
  @Input() nameValue
  @Input() searchPanelVisible = true
  @Input() dataSourceLookup: any[]
  @Input() objIsData = true
  @Input() errorMenssage = 'Não encontrado(a).'
  @Input() dataSourceMap: (res: any) => any

  model: FormGroup
  codeSubscription: Subscription
  onChange: (value: string) => void

  onTouched = () => {}
  touched = false

  constructor(
    private builder: FormBuilder,
    private _dialogService: NbDialogService,
    private _crudService: CrudService,
    private _toastr: ToastrService,
  ) {}

  registerOnTouched(touched: any) {
    this.onTouched = touched
  }

  ngOnInit(): void {
    this.model = this.builder.group({
      [this.idKey]: [undefined],
      codigo: [undefined],
      [this.nameKey]: [undefined],
    })
    if (!this.disabledCodeInput) {
      this.initializeHandlres()
    }
  }

  initializeHandlres() {
    this.codeSubscription = this.model
      .get('codigo')
      .valueChanges.pipe(debounceTime(700), distinctUntilChanged())
      .subscribe(value => {
        this.onInputChange(value)
        this.markAsTouched()
      })
  }

  formatFilter(filter: string[]) {
    const formattedFilter = JSON.stringify(filter)
      .replace(/,/g, ',')
      .replace(/\["/g, '"')
      .replace(/"\]/g, '"');

    return formattedFilter;
  }

  writeValue(value: any) {
    function returnValue(res, key) {
      let value = { ...res }
      for (let i = 0; i < key.length; i++) {
        value = value?.[key[i]]
      }
      return value
    }
    if (value) {
      this.model.patchValue(
        {
          ...value,
          codigo: returnValue(value, this.codeKey.split('.')),
          [this.nameKey]: returnValue(value, this.nameKey.split('.')),
        },
        { emitEvent: false },
      )
    } else {
      this.model.reset(undefined, { emitEvent: false })
    }
    this.markAsTouched()
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched()
      this.touched = true
    }
  }

  registerOnChange(fn: (value: any) => void) {
    this.onChange = fn
  }

  async onInputChange(value: any) {
    const uri = this.uri
    if (!value || value == '') {
      this.onChange(undefined)
      this.model.reset()
      return
    }
    if (!this.filter) {
      if (this.objIsData) {
        await this._crudService
          .getDataSourceFiltro(
            'uuid',
            `${uri}`,
            1,
            `${this.codeKey}`,
            `${value}`,
          )
          .load()
          .then(
            res => {
              onResponseGet.apply(this, [res])
            },
            err => onReject.apply(this, [err]),
          )
      } else {
        this._crudService
          .getSingleObject<any>(`${uri}`, {
            filter: `["${this.codeKey}","=","${value}"]`,
          })
          .pipe()
          .subscribe(
            res => {
              const result = res.dados.filter(
                item => item[this.codeKey] == value,
              )
              onResponseGet.apply(this, [result])
            },
            error => {
              this._toastr.send({
                error: true,
                title: 'Error',
                message: 'Não encontrado',
              })
            },
          )
      }
      // await this._crudService
      //   .getDataSourceFiltro('uuid', `${uri}`, 1, `${this.codeKey}`, `${value}`)
      //   .load()
      //   .then(
      //     res => {
      //       console.log(res)
      //       onResponseGet.apply(this, [res])
      //     },
      //     err => onReject.apply(this, [err]),
      //   )
      // // this._crudService
      // //   .getSingleObject(`${uri}`, { filter: [this.codeKey, '=', value] })
      // //   .pipe()
      // //   .subscribe(res => {
      // //     console.log(res)
      // //     onResponseGet.apply(this, [res.dados])
      // //   })
    } else {
      new DataSource({
        store: this._crudService.getDataSourceFiltro(
          'uuid',
          `${uri}`,
          1,
          `${this.codeKey}`,
          `${value}`,
        ),
        filter: this.filter,
      })
        .load()
        .then(
          res => onResponseGet.apply(this, [res]),
          err => onReject.apply(this, [err]),
        )
    }
    function onResponseGet(res) {
      if (res.length == 0) {
        this._toastr.send({
          error: true,
          title: 'Erro',
          message: this.errorMenssage,
        })
        this.onChange(undefined)
        this.model.reset()
      } else {
        const newData = this.returnAllData ? res[0] : res[0][`${this.idKey}`]
        this.onChange(newData)
        this.loadForm(res[0])
      }
    }
    function onReject(err) {
      this._toastr.bulkSend({
        error: true,
        message: `${err}`,
      })
    }
  }

  private loadForm(res: any): void {
    function returnValue(res, key) {
      let value = { ...res }
      for (let i = 0; i < key.length; i++) {
        value = value?.[key[i]]
      }
      return value
    }

    let code = returnValue(res, this.codeKey.split('.'))
    let name: any
    if (this.multipleNames) {
      name = this.multipleNames
        .map(value => returnValue(res, value.split('.')))
        .join(' - ')
    } else {
      name = returnValue(res, this.nameKey.split('.'))
    }
    let id = returnValue(res, this.idKey.split('.'))

    this.model.patchValue(
      {
        codigo: code,
        [this.nameKey]: name,
        [this.idKey]: id,
      },
      { emitEvent: false },
    )
  }

  onButtonClick() {
    const dialogRef = this._dialogService.open(DefaultSearchDialogComponent, {
      context: {
        searchData: {
          uri: this.uri,
          dialogTitle: this.dialogTitle,
          columns: SEARCH_COLUMNS[`${this.searchColumnsType}`],
          filter: this.filter,
          sort: this.sort
        },
        searchPanelVisible: this.searchPanelVisible,
        dataSourceLookup: this.dataSourceLookup,
        objIsData: this.objIsData,
        dataSourceMap: this.dataSourceMap
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose
      .pipe(
        filter(res => res),
        take(1),
      )
      .subscribe(res => {
        if (res) {
          const newData = this.returnAllData ? res : res[`${this.idKey}`]
          this.onChange(newData)
          this.loadForm(res)
        }
      })
  }
}
