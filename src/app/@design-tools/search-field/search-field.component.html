<label class="label">{{ required ? label + ' *' : label }}</label>
<div class="d-flex w-100" [formGroup]="model">
  <nb-form-field [ngClass]="{ 'w-100': hideName }">
    <input
      [type]="'text'"
      [fullWidth]="true"
      [fieldSize]="'small'"
      [formControlName]="'codigo'"
      [placeholder]="codeLabel"
      nbInput
      [readonly]="disabled || disabledCodeInput"
    />
    <button
      [size]="'small'"
      [title]="'Buscar'"
      nbSuffix
      nbButton
      ghost
      [disabled]="disabled"
      [hidden]="disabled || hideButton"
      (click)="onButtonClick()"
    >
      <nb-icon icon="search" pack="eva"></nb-icon>
    </button>
  </nb-form-field>
  <input
    *ngIf="!hideName"
    class="ml-1"
    [type]="'text'"
    [fullWidth]="true"
    [fieldSize]="'small'"
    [formControlName]="nameKey"
    [placeholder]="nameLabel"
    [value]="nameValue"
    nbInput
    readonly
  />
</div>
<div
  *ngIf="displayError"
  class="invalid-feedback"
  [ngClass]="{
    'd-block': true
  }"
>
  <div>{{ label + ' é obrigatório' }}</div>
</div>
