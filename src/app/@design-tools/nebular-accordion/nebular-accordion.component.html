<ng-container
  *ngIf="showOpenedControl"
  [formGroup]="model"
>
  <div class="row">
    <div class="col-md-12">
      <eqp-nebular-toggle
        [value]="model.value.opened"
        [nebularLabel]="model.value.opened ? openedControlOnValue : openedControlOffValue"
        [useNebularLabel]="false"
        [nebularLabelPosition]="'end'"
        [status]="'basic'"
        [checked]="false"
        [disabled]="false"
        size="large"
        formControlName="opened"
        name="opened"
        class="float-right"
        [label]="openedControlLabel"
        (ngModelChange)="sectionController($event)"
      >
      </eqp-nebular-toggle>
    </div>
  </div>
</ng-container>

<ng-container>
  <nb-accordion
    [multi]="isMultiple"
    #accordion
  >
    <nb-accordion-item
      *ngFor="let item of items; index as i"
      [collapsed]="item?.collapsed"
      [expanded]="item?.expanded"
      [disabled]="item?.disabled"
    >
      <nb-accordion-item-header>{{ item?.title }}</nb-accordion-item-header>
      <nb-accordion-item-body>
        <ng-container *ngTemplateOutlet="templates[item?.template]">
        </ng-container>
      </nb-accordion-item-body>
    </nb-accordion-item>
  </nb-accordion>
</ng-container>
