import { Component, Input, OnInit, forwardRef } from '@angular/core'
import {
  ControlValueAccessor,
  NG_VALUE_ACCESSOR
} from '@angular/forms'

@Component({
  selector: 'eqp-field-document-editor',
  templateUrl: './field-document-editor.component.html',
  styleUrls: ['./field-document-editor.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: forwardRef(() => FieldDocumentEditorComponent),
    },
  ],
})
export class FieldDocumentEditorComponent
  implements ControlValueAccessor, OnInit
{
  @Input() disabled = false
  @Input() readonly = false
  @Input() required = false
  @Input() placeholder = 'Insira o texto aqui...'

  currentValue = ''

  ngOnInit(): void {}

  onChanged(value?: any) {
    this.currentValue = value
  }

  onTouched: (value?: any) => {}

  set value(value: any) {
    this.currentValue = value
  }

  changeValue(value: any) {
    this.onChanged(value)
    this.onTouched()
  }

  writeValue(value: any) {
    this.value = value
  }

  registerOnChange(fn: any): void {
    this.onChanged = fn
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn
  }

  setDisabledState(isDisabled: boolean) {
    this.disabled = isDisabled
  }

  public modelChanged(event: any): void {
    this.onChanged(event)
  }
}
