input {
  display: none;
}

.img-preview-container {
  background-color: #f7f7f7;
  position: relative;

  .img-preview {
    background: center center no-repeat;
    background-size: contain;
    height: 116px;
    width: 116px;
  }
}

.img-loading-overlay {
  background-color: black;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  opacity: 0.2;
  position: absolute;
}

.img-spinning-circle {
  display: inline-block;
  width: 64px;
  height: 64px;
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
.img-spinning-circle:after {
  content: ' ';
  display: block;
  width: 46px;
  height: 46px;
  margin: 1px;
  border-radius: 50%;
  border: 5px solid #fff;
  border-color: #fff transparent #fff transparent;
  animation: lds-dual-ring 1.2s linear infinite;
}
@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
