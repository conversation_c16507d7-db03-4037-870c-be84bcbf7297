import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ColumnDirective } from '@design-tools/wrapped-dx-grid/directives/column.directive'
import { NbMomentDateModule } from '@nebular/moment'
import {
  NbAccordionModule,
  NbActionsModule,
  NbAlertModule,
  NbAutocompleteModule,
  NbButtonModule,
  NbCardModule,
  NbCheckboxModule,
  NbContextMenuModule,
  NbDatepickerModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbLayoutModule,
  NbListModule,
  NbMenuModule,
  NbPopoverModule,
  NbProgressBarModule,
  NbRadioModule,
  NbRouteTabsetModule,
  NbSelectModule,
  NbSidebarModule,
  NbSpinnerModule,
  NbStepperModule,
  NbTabsetModule,
  NbTimepickerModule,
  NbToggleModule,
  NbTooltipModule,
  NbWindowModule,
} from '@nebular/theme'
import { TextMaskModule } from 'angular2-text-mask'
import {
  DxButtonGroupModule,
  DxChartModule,
  DxCheckBoxModule,
  DxDataGridModule,
  DxDateBoxModule,
  DxDropDownButtonModule,
  DxFileUploaderModule,
  DxHtmlEditorModule,
  DxNumberBoxModule,
  DxPieChartModule,
  DxPivotGridModule,
  DxPopoverModule,
  DxProgressBarModule,
  DxSelectBoxModule,
  DxTabPanelModule,
  DxTextBoxModule,
  DxDropDownBoxModule
} from 'devextreme-angular'
import { NgxCurrencyModule } from 'ngx-currency'
import { ImageCropperModule } from 'ngx-image-cropper'
import { NgxMaskModule } from 'ngx-mask'
import { DxBarChartComponent } from './dx-bar-chart/dx-bar-chart.component'
import { DxStandardGridComponent } from './dx-standard-grid/dx-standard-grid.component'
import { ChooseFilesComponent } from './file-handling/choose-files/choose-files.component'
import { FileInputComponent } from './file-handling/file-input/file-input.component'
import { FileParser } from './file-handling/file-parser'
import { DragDropDirective } from './image-handling/directives/dragDrop.directive'
import { ImgAdjusterComponent } from './image-handling/img-adjuster/img-adjuster.component'
import { ImgContainerComponent } from './image-handling/img-container/img-container.component'
import { ImageUploadComponent } from './image-upload/image-upload.component'
import { LoadingComponent } from './loading/loading.component'
import { NebularAccordionComponent } from './nebular-accordion/nebular-accordion.component'
import { NebularAutoCompleteComponent } from './nebular-auto-complete/nebular-auto-complete.component'
import { NebularButtonComponent } from './nebular-button/nebular-button.component'
import { NebularCardComponent } from './nebular-card/nebular-card.component'
import { NebularCheckboxComponent } from './nebular-checkbox/nebular-checkbox.component'
import { NebularDialogComponent } from './nebular-dialog/nebular-dialog.component'
import { NebularInputComponent } from './nebular-input/nebular-input.component'
import { NebularSearchFieldComponent } from './nebular-search-field/nebular-search-field.component'
import { NebularSearchInputComponent } from './nebular-search-input/nebular-search-input.component'
import { NebularSelectComponent } from './nebular-select/nebular-select.component'
import { NebularToggleComponent } from './nebular-toggle/nebular-toggle.component'
import { NebularWindowComponent } from './nebular-window/nebular-window.component'
import { SearchDialogComponent } from './search-dialog/search-dialog.component'
import { StandardPageComponent } from './standard-page/standard-page.component'
import { SuggestionBoxComponent } from './suggestion-box/suggestion-box.component'
import { WrappedDxGridComponent } from './wrapped-dx-grid/wrapped-dx-grid.component';
import { FieldCheckboxComponent } from './field-checkbox/field-checkbox.component';
import { SearchFieldComponent } from './search-field/search-field.component'
import { FieldDateComponent } from './field-date/field-date.component'
import { FieldsetComponent } from './fieldset/fieldset.component'
import { FieldToggleComponent } from './field-toggle/field-toggle.component'
import { FieldDocumentEditorComponent } from './field-document-editor/field-document-editor.component'
import { EnhancedSearchFieldComponent } from './enhanced-search-field/enhanced-search-field.component'
import { FieldFileComponent } from './field-file/field-file.component'

@NgModule({
  declarations: [
    StandardPageComponent,
    NebularButtonComponent,
    NebularInputComponent,
    NebularSelectComponent,
    NebularToggleComponent,
    DxStandardGridComponent,
    NebularCardComponent,
    NebularCheckboxComponent,
    NebularWindowComponent,
    NebularDialogComponent,
    NebularAutoCompleteComponent,
    NebularAccordionComponent,
    SuggestionBoxComponent,
    WrappedDxGridComponent,
    DxBarChartComponent,
    ColumnDirective,
    FileInputComponent,
    ChooseFilesComponent,
    ImageUploadComponent,
    LoadingComponent,
    ImgContainerComponent,
    ImgAdjusterComponent,
    DragDropDirective,
    SearchDialogComponent,
    NebularSearchFieldComponent,
    NebularSearchInputComponent,
    FieldCheckboxComponent,
    SearchFieldComponent,
    FieldDateComponent,
    FieldsetComponent,
    FieldToggleComponent,
    FieldFileComponent,
    EnhancedSearchFieldComponent,
    FieldDocumentEditorComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NbCardModule,
    NbButtonModule,
    NbIconModule,
    NbInputModule,
    NbSelectModule,
    NbToggleModule,
    NbCheckboxModule,
    NbTooltipModule,
    NbRadioModule,
    NbProgressBarModule,
    NbDatepickerModule,
    NbMomentDateModule,
    NbSpinnerModule,
    NbAccordionModule,
    NbListModule,
    NbAutocompleteModule,
    NbListModule,
    NbAlertModule,
    NbCardModule,
    NbTabsetModule,
    NbPopoverModule,
    NbAccordionModule,
    NbContextMenuModule,
    NbActionsModule,
    NbWindowModule.forChild(),
    DxDataGridModule,
    DxDateBoxModule,
    DxPopoverModule,
    DxPieChartModule,
    DxChartModule,
    DxSelectBoxModule,
    DxHtmlEditorModule,
    DxTabPanelModule,
    DxCheckBoxModule,
    ImageCropperModule,
    TextMaskModule,
    NgxMaskModule.forRoot(),
    NgxCurrencyModule,
    DxPivotGridModule,
    NbFormFieldModule,
    NbDatepickerModule,
  ],
  exports: [
    DxDropDownBoxModule,
    NbListModule,
    NbAlertModule,
    NbCardModule,
    NbTabsetModule,
    NbPopoverModule,
    NbAccordionModule,
    NbRadioModule,
    NbContextMenuModule,
    NbStepperModule,
    NbSelectModule,
    NbFormFieldModule,
    NbMenuModule,
    NbButtonModule,
    NbIconModule,
    NbToggleModule,
    NbInputModule,
    DxButtonGroupModule,
    DxDataGridModule,
    DxDateBoxModule,
    DxPopoverModule,
    DxPieChartModule,
    DxChartModule,
    DxHtmlEditorModule,
    NbDatepickerModule,
    DxTabPanelModule,
    DxTextBoxModule,
    DxNumberBoxModule,
    NebularButtonComponent,
    NebularInputComponent,
    NebularSelectComponent,
    NebularToggleComponent,
    DxStandardGridComponent,
    DxFileUploaderModule,
    DxDropDownButtonModule,
    DxProgressBarModule,
    NebularCardComponent,
    NebularCheckboxComponent,
    NebularWindowComponent,
    NebularDialogComponent,
    NebularAutoCompleteComponent,
    NebularAccordionComponent,
    WrappedDxGridComponent,
    DxBarChartComponent,
    ColumnDirective,
    StandardPageComponent,
    SuggestionBoxComponent,
    FileInputComponent,
    ChooseFilesComponent,
    ImageUploadComponent,
    LoadingComponent,
    DragDropDirective,
    SearchDialogComponent,
    NebularSearchInputComponent,
    NbLayoutModule,
    NbSidebarModule,
    NbSpinnerModule,
    NbTimepickerModule,
    DxSelectBoxModule,
    NbRouteTabsetModule,
    NebularSearchFieldComponent,
    NbIconModule,
    FieldCheckboxComponent,
    SearchFieldComponent,
    FieldDateComponent,
    FieldsetComponent,
    NbDatepickerModule,
    FieldToggleComponent,
    FieldFileComponent,
    EnhancedSearchFieldComponent,
    FieldDocumentEditorComponent
  ],
  entryComponents: [ChooseFilesComponent],
  providers: [
    FileParser,
    // {
    //   provide: NB_TIME_PICKER_CONFIG,
    //   useValue: {},
    // },
  ],
})
export class EqpDesignToolsModule {}
