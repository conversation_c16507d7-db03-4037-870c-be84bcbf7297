import {
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core'
import {
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
} from '@angular/forms'
import { NbComponentStatus } from '@nebular/theme'

const valueProvider = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => FieldCheckboxComponent),
  multi: true,
}

@Component({
  selector: 'eqp-field-checkbox',
  templateUrl: './field-checkbox.component.html',
  styleUrls: ['./field-checkbox.component.scss'],
  providers: [valueProvider],
})
export class FieldCheckboxComponent implements ControlValueAccessor, OnInit {
  @Input() private formControlName: string
  @Input() public name: string
  @Input() public label: string
  @Input() public disabled?: boolean = false
  @Input() public indeterminate?: boolean = false
  @Input() public status: NbComponentStatus = 'primary'
  @Input() public nebularLabelPosition: string = 'start'

  @Input() public checkboxClick?: ($event: any, that: any) => any
  @Input() public checkboxChange?: ($event: any, that: any) => any
  @Input() public this?: any

  currentValue: boolean;

  onChanged(value?: any) {
    this.currentValue = value
  }

  onTouched: (value?: any) => {};

  ngOnInit(): void {}

  set value(value: any) {
    this.currentValue = value == 'S';
  }

  changeValue(value: any) {
    const outValue = value ? 'S' : 'N';
    this.onChanged(outValue);
    this.onTouched();
  }

  writeValue(value: any) {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChanged = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean) {
    this.disabled = isDisabled;
  }

  public modelChanged(event: any): void {
    this.onChanged(event);
  }
}
