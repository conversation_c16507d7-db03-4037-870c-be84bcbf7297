<div
  [class]="'row ' + dialogSize"
  [ngClass]="{ 'no-gutters': dialogSize == 'full' }"
>
  <div class="col-md-12">
    <nb-card
      [nbSpinner]="spinnerActive"
      [nbSpinnerStatus]="spinnerStatus"
      [nbSpinnerSize]="spinnerSize"
      [nbSpinnerMessage]="spinnerMessage"
      [ngStyle]="{
        'max-height': dialogSize != 'full' ? '90vh' : '100vh',
        height: dialogSize == 'full' ? '100vh' : 'initial',
        width: dialogSize == 'full' ? '100vw' : 'initial'
      }"
    >
      <nb-card-header *ngIf="headerVisible">
        <div class="row">
          <div class="col-md-8 d-flex align-items-center" style="gap: 0.5rem">
            <h5>{{ dialogTitle }}</h5>
            <span
              class="mt-2"
              *ngIf="enableTag"
              [ngClass]="
                effectivated ? 'badge badge-success ' : 'badge badge-warning'
              "
            >
              {{ tagName }}
            </span>
          </div>
          <div class="col-md-4">
            <button
              *ngIf="topRightButtonVisible"
              [appearance]="topRightButtonAppearance"
              [status]="topRightButtonType"
              [shape]="topRightButtonSize"
              [size]="topRightButtonSize"
              [disabled]="topRightButtonDisabled"
              id="{{ topRightButtonId }}"
              title="{{ topRightButtonTitle }}"
              class="float-right ml-2"
              nbButton
              (click)="topRightButtonClick()"
            >
              <i
                *ngIf="topRightButtonIconVisible"
                [ngClass]="{ 'mr-1': topRightButtonText }"
                class="{{ topRightButtonIcon }}"
              ></i>
              {{ topRightButtonText }}
            </button>
          </div>
        </div>
      </nb-card-header>
      <nb-card-body>
        <ng-content></ng-content>
      </nb-card-body>
      <nb-card-footer>
        <div class="row">
          <div class="col-md-6">
            <button
              *ngIf="bottomLeftButtonVisible"
              [appearance]="bottomLeftButtonAppearance"
              [status]="bottomLeftButtonType"
              [shape]="bottomLeftButtonSize"
              [size]="bottomLeftButtonSize"
              [disabled]="bottomLeftButtonDisabled"
              id="{{ bottomLeftButtonId }}"
              title="{{ bottomLeftButtonTitle }}"
              class="float-left secondary-button"
              nbButton
              (click)="bottomLeftButtonClick()"
            >
              {{ bottomLeftButtonText }}
            </button>
          </div>

          <div class="col-md-6">
            <button
              *ngIf="rightFirstButtonVisible"
              [appearance]="rightFirstButtonAppearance"
              [status]="rightFirstButtonType"
              [shape]="rightFirstButtonSize"
              [size]="rightFirstButtonSize"
              [disabled]="rightFirstButtonDisabled"
              id="{{ rightFirstButtonId }}"
              title="{{ rightFirstButtonTitle }}"
              class="float-right ml-2 first-button"
              nbButton
              (click)="rightFirstButtonClick()"
            >
              {{ rightFirstButtonText }}
            </button>

            <button
              *ngIf="rightDenyButtonVisible"
              [appearance]="rightDenyButtonAppearance"
              [status]="rightDenyButtonType"
              [shape]="rightDenyButtonSize"
              [size]="rightDenyButtonSize"
              [disabled]="rightDenyButtonDisabled"
              id="{{ rightDenyButtonId }}"
              title="{{ rightDenyButtonTitle }}"
              class="float-right ml-2"
              nbButton
              (click)="rightDenyButtonClick()"
            >
              {{ rightDenyButtonText }}
            </button>

            <button
              *ngIf="rightSecondButtonVisible"
              [disabled]="rightSecondButtonDisabled"
              [ngClass]="rightSecondButtonClass +' float-right'"
              (click)="rightSecondButtonClick()"
            >
              {{ rightSecondButtonText }}
            </button>

            <button
              *ngIf="rightThirdButtonVisible"
              [appearance]="rightThirdButtonAppearance"
              [status]="rightThirdButtonType"
              [shape]="rightThirdButtonSize"
              [size]="rightThirdButtonSize"
              [disabled]="rightThirdButtonDisabled"
              id="{{ rightThirdButtonId }}"
              title="{{ rightThirdButtonTitle }}"
              class="float-right ml-2"
              nbButton
              (click)="rightThirdButtonClick()"
            >
              {{ rightThirdButtonText }}
            </button>
          </div>
        </div>
      </nb-card-footer>
    </nb-card>
  </div>
</div>
