<label class="label">{{ label }}</label>
<div class="d-flex w-100" [formGroup]="model">
  <nb-form-field *ngIf="!hideCode" [ngClass]="{'w-100': hideName}">
    <input
      [type]="'text'"
      [fullWidth]="true"
      [fieldSize]="'small'"
      [formControlName]="codeKey"
      [placeholder]="codeLabel"
      nbInput
      [readonly]="disabled || disabledCodeInput"
    />
    <button
      [size]="'small'"
      [title]="'Buscar'"
      nbSuffix
      nbButton
      ghost
      [disabled]="disabled"
      [hidden]="disabled || hideButton"
      (click)="onButtonClick.emit(true)"
    >
      <nb-icon icon="search" pack="eva"></nb-icon>
    </button>
  </nb-form-field>
  <button
    *ngIf="hideCode"
    [size]="'small'"
    [title]="'Buscar'"
    nbButton
    appearance="outline"
    [disabled]="disabled"
    [hidden]="disabled || hideButton"
    (click)="onButtonClick.emit(true)"
  >
    <nb-icon icon="search" pack="eva"></nb-icon>
  </button>
  <input
    *ngIf="!hideName"
    class="ml-1"
    [type]="'text'"
    [fullWidth]="true"
    [fieldSize]="'small'"
    [formControlName]="nameKey"
    [placeholder]="nameLabel"
    nbInput
    readonly
  />
</div>
