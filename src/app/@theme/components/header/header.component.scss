@import '~bootstrap/scss/mixins/breakpoints';
@import '~@nebular/theme/styles/global/breakpoints';
@import '../../styles/themes';

@include nb-install-component() {
  display: flex;
  justify-content: space-between;
  width: 100%;

  .logo-container {
    display: flex;
    align-items: center;
    width: calc(#{nb-theme(sidebar-width)} - #{nb-theme(header-padding)});
  }

  nb-action {
    height: auto;
    display: flex;
    align-content: center;
  }

  ::ng-deep nb-badge {
    margin: 10px 5px 10px 10px;
    padding: 0.1rem 0.2rem;
  }

  nb-user {
    cursor: pointer;
  }

  ::ng-deep nb-search button {
    padding: 0 !important;
  }

  .header-container {
    display: flex;
    align-items: center;
    width: auto;

    .sidebar-toggle {
      @include nb-ltr(margin-right, 1.25rem);
      @include nb-rtl(margin-left, 1.25rem);
      text-decoration: none;
      color: nb-theme(text-hint-color);
      nb-icon {
        font-size: 1.75rem;
      }
    }

    .logo {
      padding: 0 1.25rem;
      font-size: 1.75rem;
      @include nb-ltr(border-left, 1px solid nb-theme(divider-color));
      @include nb-rtl(border-right, 1px solid nb-theme(divider-color));
      white-space: nowrap;
      text-decoration: none;
    }

    .img-logo {
      height: 50px;
      margin-right: .5rem;
    }
  }

  @include media-breakpoint-down(md) {
    .header-container {
      .logo {
        font-size: 1.2rem;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    .control-item {
      display: none;
    }
    .user-action {
      border: none;
      padding: 0;
    }
    .header-container {
      .logo {
        font-size: 0.9rem !important;
      }
    }
  }

  @include media-breakpoint-down(is) {
    nb-select {
      display: none;
    }
    .img-logo {
      height: 30px !important;
    }
    .header-container {
      .logo {
        font-size: 0.7rem !important;
      }
    }
  }
}
