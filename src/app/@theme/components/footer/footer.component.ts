import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder } from '@angular/forms'
import { MenuService } from '@pages/menu.service'
import { distinctUntilChanged, filter, first, map, takeUntil, tap } from 'rxjs/operators'
import { environment } from '@environments/environment'
import { EQP_APP_CONFIGURATION as eqpConfig } from '../../../../eqp-configuration'
import { UserDataService } from '@guards/services/user-data.service'
import { Subject } from 'rxjs'
import { DominioService } from '@common/services/dominio/dominio.service'
import { SessaoService } from '@common/services/sessao/sessao.service'

@Component({
  selector: 'eqp-footer',
  styleUrls: ['./footer.component.scss'],
  templateUrl: './footer.component.html',
})
export class FooterComponent implements OnInit, OnDestroy {
  public model = this._getModedl()

  public entities: any[] = []
  public exercises: any[] = []
  public urlTransparencia = ''
  public config = eqpConfig
  public userData: any
  public entity: any;
  
  private _destroy$: Subject<void> = new Subject<void>()

  constructor(
    private _builder: FormBuilder,
    private _userService: UserDataService,
    private _menuService: MenuService,
    private _sessaoService: SessaoService,
    private _dominioService: DominioService,
  ) {}

  ngOnInit(): void {
    this._getEntities()
    this._initialModelData()
    this._loadExercicio(false)
    this._getDomain()
    this._changeEntity()
    this._changeExercise()
  }

  public ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }

  private _getModedl() {
    return this._builder.group({
      entidadeUuid: [],
      exercicioUuid: [],
    })
  }

  private get _controls() {
    return this.model.controls
  }

  private _initialModelData() {
    this.userData = this._userService.getUserData()

    this._controls.entidadeUuid.patchValue(this.userData.entidade.uuid)
    this._controls.exercicioUuid.patchValue(this.userData.exercicioUuid)
  }

  private _getEntities(): void {
    this._menuService
      .entidades()
      .pipe(
        first(),
        map(res => {
          return res.data.map(data => {
            data.nome = `${data.codigo} - ${data.nome}`
            return data
          })
        }),
      )
      .subscribe((data: any) => {
        this.entities = data
      })
  }

  private _loadExercicio(clear: boolean) {
    this._menuService
      .exercicios()
      .pipe(first())
      .subscribe(data => {
        let exerc = []
        data.data.forEach(item => {
          if (item.exercicio >= 2013) {
            item.exercicio = item.exercicio + ' - ' + item.exercicioStatus.nome
            exerc.push(item)
          }
        })

        if (clear) {
          localStorage.removeItem('userData')
          this.userData.exercicioUuid = null
          this.userData.exercicio = null
          localStorage.setItem('userData', JSON.stringify(this.userData))
        }
        this.exercises = exerc
      })
  }

  private _getDomain() {
    const environmentNameAfterRefex = environment.url.match(/-([^.-]+)\./)
    const environmentDescription = environmentNameAfterRefex
      ? `-${environmentNameAfterRefex[1]}.ops.equiplano.com.br`
      : '.equiplano.cloud'
    this._dominioService
      .getDomain()
      .pipe(first())
      .subscribe(res => {
        const domainName = res.dados.dominio
        if (domainName) {
          this.urlTransparencia = `https://portal-${domainName}${environmentDescription}`
        }
      })
  }

  private _changeEntity() {
    this._controls.entidadeUuid.valueChanges
      .pipe(
        distinctUntilChanged(),
        takeUntil(this._destroy$),
      )
      .subscribe(value => {
        if (value) {
          this.entities.forEach(item => {
            if (item.uuid === value) {
              this.entity = item
            }
          })
          this.userData.entidadeUuid = this.entity.uuid
          this.userData.clienteUuid = this.entity.clienteUuid
          this.userData.entidade = this.entity
          this._sessaoService
            .atualizarToken({
              tokenUuid: this.userData.idToken,
              entidadeUuid: this.userData.entidadeUuid,
              entidadeNome: this.userData.entidade.nome,
              clienteUuid: this.userData.clienteUuid,
            })
            .pipe(first())
            .subscribe(data => {
              localStorage.removeItem('userData')
              this.userData.tokenJwt = data.dados
              localStorage.setItem('userData', JSON.stringify(this.userData))
              this.userData = this._userService.getUserData()
              this._userService.crossClientSet('userData', this.userData)
              this.userData.exercicioUuid = null
              this.userData.exercicio = null
              this._loadExercicio(true)
            })
        } else {
          this.exercises = []
        }
      })
  }

  private _changeExercise() {
    this._controls.exercicioUuid.valueChanges
      .pipe(
        distinctUntilChanged(),
        filter(value => value),
        takeUntil(this._destroy$),
      )
      .subscribe(value => {
        this.userData.exercicioUuid = value
          this.exercises.forEach(item => {
            if (this.userData.exercicioUuid === item.uuid) {
              this.userData.exercicio = item.exercicio.substring(0, 4)
              this.userData.exercise = item
              this.userData.exercise.exercicio = item.exercicio.substring(0, 4)
            }
          })

          this._sessaoService
            .atualizarToken({
              tokenUuid: this.userData.idToken,
              exercicioUuid: this.userData.exercicioUuid,
              exercicioAno: this.userData.exercicio,
            })
            .pipe(first())
            .subscribe(data => {
              localStorage.removeItem('userData')
              this.userData.tokenJwt = data.dados
              localStorage.setItem('userData', JSON.stringify(this.userData))
              this.userData = this._userService.getUserData()
              this._userService.crossClientSet('userData', this.userData)

              this._sessaoService
                .atualizaSessao(this.userData)
                .pipe(first())
                .subscribe(sessao => {
                  localStorage.removeItem('userData')
                  localStorage.setItem('userData', JSON.stringify(sessao.dados))

                  window.location.reload()
                })
            })
      })
  }
}
