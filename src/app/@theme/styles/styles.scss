@import url('https://fonts.googleapis.com/css?family=Open+Sans:400,600,700&display=swap');
@import url('https://fonts.googleapis.com/css?family=Roboto:200,300,400,500,600,700,800&display=swap');

// themes - our custom or/and out of the box themes
@import './themes';
@import 'src/assets/scss/_animations.scss';
@import 'src/assets/scss/_utils.scss';
@import 'src/assets/scss/_overrides.scss';

// framework component themes (styles tied to theme variables)
@import '~@nebular/theme/styles/globals';
@import '~@nebular/auth/styles/globals';

@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import '~bootstrap/scss/grid';
@import '~@fortawesome/fontawesome-free/scss/_variables.scss';

@import './material/angular-material';

// loading progress bar theme
@import './pace.theme';

@import './layout';
@import './overrides';
@import './material/material-overrides';

// install the framework and custom global styles
@include nb-install() {
  @include angular-material();

  // framework global styles
  @include nb-theme-global();
  @include nb-auth-global();

  @include ngx-layout();
  // loading progress bar
  @include ngx-pace-theme();

  @include nb-overrides();
  @include material-overrides();
}

nb-spinner {
  z-index: 998 !important;
  background-color: rgba(237, 240, 245, 0.3) !important;
}

.compacted .main-container-fixed .scrollable {
  padding-left: initial !important;
}

.cdk-global-scrollblock {
  position: initial;
  width: initial;
  overflow: hidden;
}

.menu-item a .menu-title {
  flex: 1 1 auto !important;
}

.compacted .main-container-fixed .scrollable {
  padding-left: initial !important;
}
