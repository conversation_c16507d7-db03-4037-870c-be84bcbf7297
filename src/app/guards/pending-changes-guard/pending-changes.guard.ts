import { NbDialogService } from '@nebular/theme';
import { Injectable } from '@angular/core';
import { CanDeactivate } from '@angular/router';
import { Observable } from 'rxjs';
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component';

export interface ComponentCanDeactivate {
  canDeactivate: () => boolean | Observable<boolean>;
}

@Injectable()
export class PendingChangesGuard
  implements CanDeactivate<ComponentCanDeactivate>
{
  constructor(private dialogService: NbDialogService) {}

  canDeactivate(
    component: ComponentCanDeactivate,
  ): boolean | Observable<boolean> {
    if (component.canDeactivate()) {
      return true;
    } else {
      const dialogRef = this.dialogService.open(ConfirmationComponent, {
        context: {},
        closeOnBackdropClick: false,
      });
      dialogRef.componentRef.instance.confirmationContent = {
        body: 'As alterações não foram salvas, deseja realmente sair?',
      };
      return dialogRef.onClose;
    }
  }
}
