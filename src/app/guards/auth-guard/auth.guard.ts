import { Injectable } from '@angular/core'
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router'
import { Observable } from 'rxjs'
import { UserDataService } from '../services/user-data.service'

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private userDataservice: UserDataService,
  ) {}
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    // const storage = new CrossStorageClient(`${environment.backoOfficeUrl}`)
    // const iframe = storage
    // const promisse = storage.onConnect()
    this.userDataservice.crossClientGet('theme')
    this.userDataservice.crossClientGet('devExtremeTheme')
    return this.userDataservice.crossClientGet('userData')
  }
}
