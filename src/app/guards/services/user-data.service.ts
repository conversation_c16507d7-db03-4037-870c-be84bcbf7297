import { Injectable } from '@angular/core'
import { SessaoService } from '@common/services/sessao/sessao.service'
import { environment } from '@environments/environment'
import { UserDataInterface } from '@guards/services/user-data'
import { CrossStorageClient } from 'cross-storage'
import { first } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class UserDataService {
  public storage: CrossStorageClient = new CrossStorageClient(
    `${environment.backoOfficeUrl}`,
  )
  public iframe = this.storage
  public promisse = this.storage.onConnect()
  private _userData: UserDataInterface
  private _theme: string
  private _themeDev: string

  public get userData(): UserDataInterface {
    return this._userData
  }

  public set userData(value: UserDataInterface) {
    this._userData = value
  }

  public get theme(): string {
    return this._theme
  }

  public set theme(value: string) {
    this._theme = value
  }

  public get themeDev(): string {
    return this._themeDev
  }

  public set themeDev(value: string) {
    this._themeDev = value
  }

  constructor(private service: SessaoService) {}

  public getUserData(): any {
    return JSON.parse(localStorage.getItem('userData'))
  }

  public async recuperarToken(idToken: string): Promise<void> {
    this.service
      .recuperaSessao(idToken)
      .pipe(first())
      .subscribe(sessaoSalva => {
        localStorage.removeItem('userData')

        localStorage.setItem('userData', JSON.stringify(sessaoSalva.dados))
      })
  }

  // public crossClientGet(key: string): any {
  //   return this.promisse
  //     .then(() => this.iframe.get(key))
  //     .then((data: string) => {
  //       if (key === 'userData') {
  //         const userData = JSON.parse(data)
  //         this.userData = userData
  //         this.recuperarToken(userData.idToken)
  //       } else if (key === 'theme') {
  //         this.theme = data
  //       } else if (key === 'devExtremeTheme') {
  //         this.themeDev = data
  //       }
  //       localStorage.setItem(key, data)
  //       if (
  //         key !== 'userData' ||
  //         (this.userData &&
  //           this.userData !== undefined &&
  //           this.userData !== null)
  //       ) {
  //         return true
  //       } else {
  //         const url = window.location.href
  //         let prefix: string = '/#/login'
  //         console.log('sessao token')
  //         window.open(
  //           `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(url)}`,
  //           '_self',
  //         )
  //       }
  //     })
  //     .catch(() => {
  //       if (key === 'userData') {
  //         console.log('sessao token erro')
  //         const url = window.location.href
  //         let prefix: string = '/#/login'
  //         window.open(
  //           `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(url)}`,
  //           '_self',
  //         )
  //       }
  //     })
  // }

  public crossClientGet(key: string): any {
    console.log('crossClientGet.key', key);
    const tokenFake = '{"idToken":"ff84314b-6a87-4d4d-bbfa-f07203706fa7","entidadeUuid":"149046c3-7774-4b1e-9290-7ba3c1813e1c","municipioClienteUuid":"ac94a58b-1a5c-4c3b-8956-a2c006a1c389","exercicioUuid":"b165e645-a7f9-4a48-9e5e-87eeaccf5892","exercicio":2025,"clienteUuid":"52485c5c-23bf-40d3-a37a-3ac4f7309373","cpf":"admin","email":"<EMAIL>","entidade":{"uuid":"149046c3-7774-4b1e-9290-7ba3c1813e1c","clienteUuid":"52485c5c-23bf-40d3-a37a-3ac4f7309373","codigo":411,"nome":"Fundação Municipal de Saúde de Alvorada do Sul - PR","uf":"PR","brasao":{"uuid":"371a5c54-cf6a-4c6b-a65d-c21962384317","nome":"abce7f48-8271-462e-b2f0-b9e3f60e5e7c.png","tipo_arquivo":"image/png","identificadorUuid":"BLAZON149046c3-7774-4b1e-9290-7ba3c1813e1c","link":"https://objectstorage.sa-saopaulo-1.oraclecloud.com/p/Q250YvMuTpL03M_b4SQc9PrdIH0vcBLZQoJTsrG9OAZv9g7GepXmBEpQsVYVFwNI/n/gr3m6keec5b3/b/api-anexo/o/upload/homolog/abce7f48-8271-462e-b2f0-b9e3f60e5e7c.png","descricao":"BLAZON","publicaInternet":"NO","etag":"eaa86e0b-62a0-4e2f-8826-137f3f7cf3bc","conteudo":"UZ1OLWFDqgLJgSkUbP4Kow==","requisicaoId":"gru-1:0tH_1hH2XTJ71eVnISel_Eq__rBZptxJkTOy4p_m08moBIJa6jlbtMwplP2BXP9P"}},"nome":"admin","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gdaMFX6R4T7LoVjd5WLcEbdwYyo6ROdK8fvekDDWcW0","tokenJwt":"eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JfzyrBltl3RYL7omtNyUz0ExZT5Bue5reCPw6sPYLC-nH64ofeEw_0WoMukMMhuWI6aitBemC9lqRu2vgw2GTA","uuid":"664f8da6-f13f-4ba6-8bbe-ede27f852b97","exercise":{"status":{"id":4,"uuid":"*************-4115-9bf2-62e1c95e8909","codigo":4,"nome":"Execução"},"texto":"2025","valor":"b165e645-a7f9-4a48-9e5e-87eeaccf5892"}}'
    return (
      this.promisse
        // .then(() => this.iframe.get(key))
        .then(() =>
          key == 'userData'
            ? tokenFake
            : key == 'theme'
            ? 'dark'
            : 'material.nebular.dark',
        )
        .then((data: string) => {
          if (key === 'userData') {
            const userData = JSON.parse(data);
            this.userData = userData;
          } else if (key === 'theme') {
            this.theme = data;
          } else if (key === 'devExtremeTheme') {
            this.themeDev = data;
          }
          localStorage.setItem(key, data);
          if (
            key !== 'userData' ||
            (this.userData &&
              this.userData !== undefined &&
              this.userData !== null)
          ) {
            return true;
          } else {
            const url = window.location.href;
            let prefix: string = '/#/login';
            window.open(
              `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(
                url,
              )}`,
              '_self',
            );
          }
        })
        .catch(() => {
          if (key === 'userData') {
            const url = window.location.href;
            let prefix: string = '/#/login';
            window.open(
              `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(
                url,
              )}`,
              '_self',
            );
          }
        })
    );
  }

  public crossClientSet(key: string, value: any): any {
    return this.promisse
      .then(() =>
        this.iframe.set(
          key,
          typeof value === 'string' ? value : JSON.stringify(value),
        ),
      )
      .then((data: string) => {
        if (key === 'userData') {
          const userData = JSON.parse(data)
          this.userData = userData
        } else if (key === 'theme') {
          this.theme = data
        } else if (key === 'devExtremeTheme') {
          this.themeDev = data
        }

        localStorage.setItem(key, data)

        if (data && data !== undefined && data !== null) {
          return true
        } else return false
      })
      .catch(() => {
        // do something
      })
  }

  public crossClientDel(key: string): any {
    return this.promisse
      .then(() => this.iframe.del(key))
      .then((data: string) => {
        localStorage.removeItem(key)
        if (key === 'userData') {
          console.log('sessao token delete')
          const url = window.location.href
          let prefix: string = '/#/login'
          window.open(
            `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(url)}`,
            '_self',
          )
        }
      })
      .catch(() => {})
  }
}
