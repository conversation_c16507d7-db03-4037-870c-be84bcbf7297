import { Injectable } from "@angular/core";
import { ToastrService } from "@common/services/toastr/toastr.service";
import { UserDataService } from "./services/user-data.service";

@Injectable()
export class ExerciseStatusGuard {
  
  constructor(
    private toastr: ToastrService,
    private userService: UserDataService,
  ) {}

  canActivate() {
    const statusCode = this.userService.userData.exercise.status.codigo
    if ([1,2,6].includes(statusCode)) {
      this.toastr.send({
        error: true,
        message: 'O acesso a essa funcionalidade está bloqueado para o exercício atual.'
      })
      return false
    }
    return true;
  }
}
