import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ResponseDto } from '@common/interfaces/dtos/response-dto'
import { BehaviorSubject, Observable } from 'rxjs'
import { finalize, take, tap } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class SimpleRequestService {
  public readonly loading$ = new BehaviorSubject<boolean>(false)

  constructor(private httpClient: HttpClient) {}

  enableLoading() {
    this.loading$.next(true)
  }

  disableLoading() {
    this.loading$.next(false)
  }

  post<T>(
    url: string,
    body: { [key: string]: any },
  ): Observable<ResponseDto<T>> {
    return this.httpClient.post<ResponseDto<T>>(url, body).pipe(
      take(1),
      tap(() => this.enableLoading()),
      finalize(() => this.disableLoading()),
    )
  }

  put<T>(
    url: string,
    body: { [key: string]: any },
  ): Observable<ResponseDto<T>> {
    return this.httpClient.put<ResponseDto<T>>(url, body).pipe(
      take(1),
      tap(() => this.enableLoading()),
      finalize(() => this.disableLoading()),
    )
  }

  get<T>(url: string): Observable<ResponseDto<T[] | T>> {
    return this.httpClient.get<ResponseDto<T[] | T>>(url).pipe(
      take(1),
      tap(() => this.enableLoading()),
      finalize(() => this.disableLoading()),
    )
  }

  delete(url: string): Observable<any> {
    return this.httpClient.delete<any>(url).pipe(
      take(1),
      tap(() => this.enableLoading()),
      finalize(() => this.disableLoading()),
    )
  }
}
