import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ResponseDto } from '@common/interfaces/dtos/response-dto'
import { ApiResponse } from '@core/data/api-response'
import { environment } from '@environments/environment'
import * as AspNetData from 'devextreme-aspnet-data-nojquery'
import CustomStore from 'devextreme/data/custom_store'
import { take } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class CrudService {
  public urlFull: string

  constructor(protected httpClient: HttpClient) {
    this.urlFull = environment.url
  }

  public getDataSource(
    idkey: string,
    complementoUrl: string = '',
    take: number = 0,
  ): CustomStore {
    const headers = this.getHeaders();
    return AspNetData.createStore({
      key: idkey,
      loadUrl: this.urlFull + (complementoUrl ? '/' + complementoUrl : ''),
      onBeforeSend: function (method, ajaxOptions) {
        if (ajaxOptions.data.filter)
          ajaxOptions.data.filter = ajaxOptions.data.filter.replace(
            'contains',
            '=',
          );

        ajaxOptions.data.take = take;
        ajaxOptions.headers = headers;
      },
      onAjaxError(e) {
        const erro = JSON.parse(e.xhr.response);
        e.error =
          erro.descricao +
          ': ' +
          erro.mensagem +
          '. Descrição: ' +
          erro.causa.message;
      },
    });
  }

  public getDataSourceFiltro(
    idkey: string,
    complementoUrl: string = '',
    take: number = 0,
    coluna: string = '',
    valor: string = '',
  ): CustomStore {
    const headers = this.getHeaders()
    return AspNetData.createStore({
      key: idkey,
      loadUrl: this.urlFull + (complementoUrl ? '/' + complementoUrl : ''),
      onBeforeSend: function (method, ajaxOptions) {
        if (coluna && valor) {
          if (ajaxOptions.data.filter) {
            if (ajaxOptions.data.filter.startsWith('[[')) {
              ajaxOptions.data.filter = ajaxOptions.data.filter.replace(
                '[[',
                `[["${coluna}","=","${valor}"],"and",`,
              )
            } else {
              ajaxOptions.data.filter =
                `[["${coluna}","=","${valor}"],"and",` +
                ajaxOptions.data.filter +
                ']'
            }
          } else {
            ajaxOptions.data.filter = `["${coluna}","=","${valor}"]`
          }
        }
        ajaxOptions.data.take = take
        ajaxOptions.headers = headers
      },
      onAjaxError(e) {
        const erro = JSON.parse(e.xhr.response)
        e.error =
          erro.descricao +
          ': ' +
          erro.mensagem +
          '. Descrição: ' +
          erro.causa.message
      },
    })
  }

  public getDataSourceFiltroComposto(
    idkey: string,
    complementoUrl: string = '',
    take: number = 0,
    filtro: string = '',
  ): CustomStore {
    const headers = this.getHeaders()
    return AspNetData.createStore({
      key: idkey,
      loadUrl: this.urlFull + (complementoUrl ? '/' + complementoUrl : ''),
      onBeforeSend: function (method, ajaxOptions) {
        if (filtro) {
          if (ajaxOptions.data.filter) {
            if (ajaxOptions.data.filter.startsWith('[[')) {
              ajaxOptions.data.filter = ajaxOptions.data.filter.replace(
                '[[',
                `[${filtro}},"and",`,
              )
            } else {
              ajaxOptions.data.filter =
                `[${filtro},"and",` + ajaxOptions.data.filter + ']'
            }
          } else {
            ajaxOptions.data.filter = `[${filtro}]`
          }
        }
        ajaxOptions.data.take = take
        ajaxOptions.headers = headers
      },
      onAjaxError(e) {
        const erro = JSON.parse(e.xhr.response)
        e.error =
          erro.descricao +
          ': ' +
          erro.mensagem +
          '. Descrição: ' +
          erro.causa.message
      },
    })
  }

  getSingleData<T>(url: string, params?: Object) {
    let httpParams: HttpParams = new HttpParams()
    params &&
      Object.keys(params).forEach(key => {
        httpParams = httpParams.append(key, params[key])
      })
    return this.httpClient.get<ApiResponse<T>>(`${url}`, {
      params: httpParams,
    })
  }

  getSingleStore(url: string, key: string, take: number = 10) {
    const headers = this.getHeaders()
    return AspNetData.createStore({
      key: key,
      loadUrl: `${this.urlFull}/${url}`,
      onBeforeSend: function (_, ajaxOptions) {
        ajaxOptions.data.take = take
        ajaxOptions.headers = headers
      },
    })
  }

  getSingleObject<T>(url: string, params?: Object) {
    let httpParams: HttpParams = new HttpParams()
    params &&
      Object.keys(params).forEach(key => {
        httpParams = httpParams.append(key, params[key])
      })
    return this.httpClient.get<ResponseDto<T>>(`${url}`, {
      params: httpParams,
    })
  }

  public getDataByFields(uri: string, fields: any[]) {
    const headers = this.getHeaders()
    const selectedFields = fields.toString()
    return this.httpClient.get<ResponseDto<any>>(
      `${uri}?campos=${selectedFields}`,
      {
        headers,
      },
    )
  }

  public getTypes(uri: string) {
    const headers = this.getHeaders()
    return this.httpClient.get<ResponseDto<any>>(`licitacao/${uri}`, {
      headers,
    })
  }

  private getHeaders() {
    const userData = JSON.parse(localStorage.getItem('userData'))
    const jwt = userData?.token
    return {
      authorization: jwt,
      'x-encryption': 'false',
      'x-permission': 'false',
      'x-validate': 'false',
      'x-entity-uuid': userData?.entidadeUuid,
      'x-exercise-uuid': userData?.exercicioUuid,
      'x-county-client-uuid': userData?.municipioClienteUuid,
      'x-client-uuid': userData?.clienteUuid,
    }
  }

  public getData<T>(url: string) {
    return this.httpClient.get<ApiResponse<T>>(url).pipe(take(1))
  }

  public postData<T>(url: string, body: any) {
    return this.httpClient.post<ApiResponse<T>>(url, body).pipe(take(1))
  }

  public putData<T>(url: string, body: any) {
    return this.httpClient.put<ApiResponse<T>>(url, body).pipe(take(1))
  }

  public deleteData<T>(url: string) {
    return this.httpClient.delete<ApiResponse<T>>(url).pipe(take(1))
  }
}
