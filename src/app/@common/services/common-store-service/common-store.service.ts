import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { first } from 'rxjs/operators';
import { CommonService } from '../common-service/common.service';

@Injectable({
  providedIn: 'root'
})
export class CommonStoreService<T> extends CommonService<T> {
  private store$ = new BehaviorSubject<T[]>([])
  constructor(protected http: HttpClient, public uri: string, disableApiPrefix: boolean = false) {
    super(http, uri, disableApiPrefix);
  }

  get store(): T[] {
    return this.store$.getValue()
  }

  set store(data: T[]) {
    this.store$.next(data)
  }

  loadStore() {
    return this.get()
      .pipe(first())
      .subscribe(res => this.store$.next(res.dados))
  }
}
