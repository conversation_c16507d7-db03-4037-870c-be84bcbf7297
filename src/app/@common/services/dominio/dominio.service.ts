import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DominioInterface } from './interfaces/dominio';
import { ResponseDto } from '@common/interfaces/dtos/response-dto';


@Injectable({
  providedIn: 'root'
})
export class DominioService {

  constructor(private _httpClient: HttpClient) { }

  getDomain(){
    return this._httpClient.get<ResponseDto<DominioInterface>>(`transparencia/dominio`)
  }
}
