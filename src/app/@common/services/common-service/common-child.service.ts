import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { FilterDto } from '@common/interfaces/dtos/filter-dto'
import { ResponseDto } from '@common/interfaces/dtos/response-dto'
import { Observable } from 'rxjs'
import { EQP_APP_CONFIGURATION as eqpConfig } from './../../../../eqp-configuration'

@Injectable({
  providedIn: 'root',
})
export class CommonChildService<T> {
  apiUri: string
  prefix: string

  constructor(
    protected http: HttpClient,
    public parentUri: string,
    public uri: string,
    disableApiPrefix: boolean = false,
  ) {
    this.apiUri = eqpConfig.app.apiUri
    this.prefix = disableApiPrefix ? '' : `${this.apiUri}/`
  }

  public get(
    parentUuid: string,
    filters?: FilterDto[],
  ): Observable<ResponseDto<T[]>> {
    const headers = new HttpHeaders()
    let params = new HttpParams()
    filters &&
      filters.forEach(filter => {
        params = params.append(filter.chave, filter.valor)
      })
    return this.http.get<ResponseDto<T[]>>(
      `${this.prefix}${this.parentUri}/${parentUuid}/${this.uri}`,
      {
        headers,
        params,
      },
    )
  }

  public getIndividual(
    parentUuid: string,
    uuid: string,
  ): Observable<ResponseDto<T>> {
    const headers = new HttpHeaders()
    return this.http.get<ResponseDto<T>>(
      `${this.prefix}${this.parentUri}/${parentUuid}/${this.uri}/${uuid}`,
      {
        headers,
      },
    )
  }

  public put(parentUuid: string, dto: T, uuid: string): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.put<ResponseDto<T>>(
      `${this.prefix}${this.parentUri}/${parentUuid}/${this.uri}/${uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(parentUuid: string, dto: T): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.post<ResponseDto<T>>(
      `${this.prefix}${this.parentUri}/${parentUuid}/${this.uri}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public delete(parentUuid: string, uuid: string): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.delete<any>(
      `${this.prefix}${this.parentUri}/${parentUuid}/${this.uri}/${uuid}`,
      {
        headers,
      },
    )
  }
}
