import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { FilterDto } from '@common/interfaces/dtos/filter-dto'
import { ResponseDto } from '@common/interfaces/dtos/response-dto'
import { Observable } from 'rxjs'
import { take } from 'rxjs/operators'
import { EQP_APP_CONFIGURATION as eqpConfig } from './../../../../eqp-configuration'
import { ToastrService } from './../toastr/toastr.service'

@Injectable({
  providedIn: 'root',
})
export class CommonService<T> {
  prefix: string

  constructor(
    protected http: HttpClient,
    public uri: string,
    disableApiPrefix: boolean = false,
  ) {
    this.prefix = disableApiPrefix ? '' : `${eqpConfig.app.apiUri}/`
  }

  public get(filters?: FilterDto[]): Observable<ResponseDto<T[]>> {
    const headers = new HttpHeaders()
    let params = new HttpParams()
    filters &&
      filters.forEach(filter => {
        params = params.append(filter.chave, filter.valor)
      })
    return this.http.get<ResponseDto<T[]>>(`${this.prefix}${this.uri}`, {
      headers,
      params,
    })
  }

  public getIndividual(uuid: string): Observable<ResponseDto<T>> {
    const headers = new HttpHeaders()
    return this.http.get<ResponseDto<T>>(`${this.prefix}${this.uri}/${uuid}`, {
      headers,
    })
  }

  public put(dto: T, uuid: string): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.put<ResponseDto<T>>(
      `${this.prefix}${this.uri}/${uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(dto: T): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.post<ResponseDto<T>>(`${this.prefix}${this.uri}`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: T[]): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.post<ResponseDto<T>[]>(
      `${this.prefix}${this.uri}/lote`,
      batch,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public delete(uuid: string): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.delete<any>(`${this.prefix}${this.uri}/${uuid}`, {
      headers,
    })
  }

  async syncFetchData<T>(
    data: T[],
    toastr: ToastrService,
    filters?: FilterDto[],
  ) {
    if (data.length > 0) return data
    const fetchData = await this.get(filters)
      .pipe(take(1))
      .toPromise()
      .catch(err =>
        toastr.send({
          title: 'Error',
          error: true,
          message: err.message,
        }),
      )
    return fetchData ? fetchData.dados : []
  }
}
