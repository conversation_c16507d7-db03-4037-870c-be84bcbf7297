import { Component, OnInit } from '@angular/core'
import { NbDialogRef } from '@nebular/theme'

@Component({
  selector: 'ngx-modal-confirmar',
  templateUrl: './modal-confirmar.component.html',
  styleUrls: ['./modal-confirmar.component.scss'],
})
export class ModalConfirmarComponent implements OnInit {
  constructor(protected ref: NbDialogRef<ModalConfirmarComponent>) {}

  public ngOnInit(): void {}

  public dismiss(): void {
    this.ref.close('N')
  }

  public excluir(): void {
    this.ref.close('S')
  }
}
