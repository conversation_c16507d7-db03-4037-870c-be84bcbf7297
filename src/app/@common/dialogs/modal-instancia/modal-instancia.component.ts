import { Component, Input, OnInit } from '@angular/core'
import { InstanciaSistemaDto } from '@common/interfaces/sistema-dto'
import { NbDialogRef } from '@nebular/theme'

@Component({
  selector: 'ngx-modal-instancia',
  templateUrl: './modal-instancia.component.html',
  styleUrls: ['./modal-instancia.component.scss'],
})
export class ModalInstanciaComponent implements OnInit {
  @Input() public dados: InstanciaSistemaDto[]

  constructor(protected ref: NbDialogRef<ModalInstanciaComponent>) {}

  public ngOnInit(): void {}

  public dismiss(url: string, idCliente: string, idSistema: string): void {
    this.ref.close({ url, idCliente, idSistema })
  }
}
