
<eqp-nebular-dialog
  [dialogTitle]="dialogTitle"
  [dialogSize]="dialogSize"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="confirmationContent?.cancelText ? confirmationContent?.cancelText: 'Cancelar'"
  [bottomLeftButtonType]="confirmationContent?.cancelType ? confirmationContent?.cancelType : 'info'"
  [bottomLeftButtonVisible]="bottomLeftButtonVisible"
  [bottomLeftButtonIconVisible]="bottomLeftButtonIconVisible"
  [bottomLeftButtonIcon]="confirmationContent?.cancelIcon ? confirmationContent?.cancelIcon : 'fas fa-undo-alt'"
  [bottomLeftButtonId]="'dispose-confirmation-dialog'"
  [bottomLeftButtonTitle]="confirmationContent?.cancelTitle ? confirmationContent?.cancelTitle : 'Voltar'"
  (bottomLeftButtonEmitter)="dispose()"
  [rightFirstButtonText]="confirmationContent?.confirmText ? confirmationContent?.confirmText: 'Confirmar'"
  [rightFirstButtonType]="confirmationContent?.confirmType ? confirmationContent?.confirmType : 'danger'"
  [rightFirstButtonVisible]="rightDenyButtonVisible"
  [rightFirstButtonIconVisible]="rightDenyButtonIconVisible"
  [rightFirstButtonIcon]="confirmationContent?.confirmIcon ? confirmationContent?.confirmIcon: 'fas fa-check-circle'"
  [rightFirstButtonId]="'confirm-entries-selection'"
  [rightFirstButtonTitle]="confirmationContent?.confirmTitle ? confirmationContent?.confirmTitle: 'Confirmar'"
  (rightFirstButtonEmitter)="confirm()">
  <p style="white-space: pre-line;">{{confirmationContent?.body}}</p>
</eqp-nebular-dialog>
