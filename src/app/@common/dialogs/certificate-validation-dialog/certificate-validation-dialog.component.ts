import { Component, Input, OnInit } from '@angular/core'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogRef } from '@nebular/theme'
import { CertificationValidationResponseInterface } from '@pages/shared/interfaces/certification-validation-response'

@Component({
  selector: 'eqp-certificate-validation-dialog',
  templateUrl: './certificate-validation-dialog.component.html',
  styleUrls: ['./certificate-validation-dialog.component.scss'],
})
export class CertificateValidationDialogComponent implements OnInit {
  @Input() public dialogSize:
    | 'small'
    | 'medium'
    | 'large'
    | 'extra-large'
    | 'full' = 'extra-large'
  @Input() public gridData = []
  @Input() public dataInfo: CertificationValidationResponseInterface
  @Input() public providerName: string
  @Input() public content = ''
  dialogTitle: 'Aviso' | 'Confirmação'
  dialogContent: string

  public certificateColumns: DxColumnInterface[] = [
    {
      caption: 'Certidão',
      dataField: 'certidaoNome',
      dataType: 'string',
    },
  ]
  public isWarningDialog: boolean
  public loading: boolean = false

  constructor(
    private dialogRef: NbDialogRef<CertificateValidationDialogComponent>,
  ) {}

  ngOnInit(): void {
    this.dialogContent = `Para o fornecedor ${this.providerName}. As certidões abaixo estão faltando ou estão sem validade. Por favor, verifique e tente novamente.`
    switch (this.dataInfo.acao) {
      case '1':
        this.dialogTitle = 'Confirmação'
        this.isWarningDialog = true
        break
      case '2':
        this.dialogTitle = 'Aviso'
        this.isWarningDialog = false
        break
    }
  }

  dispose() {
    this.dialogRef.close(false)
  }

  confirm() {
    this.dialogRef.close(true)
  }
}
