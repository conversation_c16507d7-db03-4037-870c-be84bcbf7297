import { Component } from '@angular/core'
import { FormGroup } from '@angular/forms'
import { Router } from '@angular/router'
import { StateGrid } from '@common/interfaces/dtos/StateGrid-dto'
import { MenuService } from '@pages/menu.service'
import { first } from 'rxjs/operators'

@Component({
  selector: 'ngx-base-telas',
  template: '<div>',
})
export class BaseTelasComponent {
  public model: FormGroup
  public statusList: any
  public nivelPermissao: string = 'FULL'

  constructor(public menuService: MenuService, public router: Router) {
    this.recuperarParametros()
    // this.permissao()
  }

  loadState = () => {
    return this.statusList
  }

  saveState = state => {
    this.statusList = state
  }

  public gravarParametros(): void {
    if (this.statusList)
      localStorage.setItem('statusList', JSON.stringify(this.statusList))
  }

  private recuperarParametros(): void {
    const statusList: StateGrid = JSON.parse(localStorage.getItem('statusList'))
    localStorage.removeItem('statusList')
    if (statusList) {
      this.statusList = statusList
    }
  }

  public permissao(rota: string): void {
    this.menuService
      .getValidacao(rota)
      .pipe(first())
      .subscribe(dados => {
        this.nivelPermissao = dados.dados
      })
  }

  public permissaoModal(rota: string): any {
    return this.menuService.getValidacao(rota)
  }
}
