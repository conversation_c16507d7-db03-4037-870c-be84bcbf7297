export default class ColorPicker {
  public static getPrimaryColorScheme(): any {
    const pallete = {
      domain: [
        '#b81cba',
        '#4796ba',
        '#ff436c',
        '#ec0e92',
        '#cfe37b',
        '#ff7350',
        '#f99e44',
        '#57ba9c',
        '#e5c354',
        '#a9c578',
        '#b9ffb0',
        '#30a4b8',
        '#36b0ae',
        '#7fc189',
        '#d5c572',
        '#ffc37a',
      ],
    }
    return pallete
  }
  public static getSecondaryColorScheme(): any {
    const pallete = {
      domain: [
        '#ff7a0d',
        '#5b8823',
        '#c58800',
        '#2c7e42',
        '#8d8c00',
        '#007257',
        '#006460',
        '#1c545c',
        '#30ff80',
        '#00e8b1',
        '#00cdd3',
        '#00afe1',
        '#008fd5',
        '#0f6eb6',
        '#52508a',
        '#56365c',
      ],
    }
    return pallete
  }

  public static getTerciaryColorScheme(): any {
    const pallete = {
      domain: [
        '#008fc5',
        '#ff1981',
        '#c35cca',
        '#e73fab',
        '#6081df',
        '#0791b0',
        '#a3d200',
        '#c1b900',
        '#f95400',
        '#ff0000',
        '#d99c00',
        '#ec7c00',
        '#2bff60',
        '#9572db',
        '#7bea10',
        '#238ad6',
      ],
    }
    return pallete
  }
}
