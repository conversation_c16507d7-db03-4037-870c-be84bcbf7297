import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import {
  ModalInstanciaComponent,
} from '@dialogs/modal-instancia/modal-instancia.component'
import { NbCardModule, NbIconModule } from '@nebular/theme'

import {
  SystemContainerComponent,
} from './system-container/system-container.component'

@NgModule({
  declarations: [SystemContainerComponent, ModalInstanciaComponent],
  imports: [CommonModule, CommonToolsModule, NbIconModule, NbCardModule],
  exports: [SystemContainerComponent],
})
export class SystemSelectorModule {}
