import { ScrollingModule } from '@angular/cdk/scrolling'
import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { EqpDesignToolsModule } from '@design-tools/eqp-design-tools.module'
import { NbDialogModule } from '@nebular/theme'
import { MoneyPipe } from '@pipes/money/money.pipe'
import { SanitizePipe } from '@pipes/sanitize/sanitize.pipe'
import { TextMaskModule } from 'angular2-text-mask'
import {
  ConfirmationComponent
} from './dialogs/confirmation/confirmation.component'
import { InfoComponent } from './dialogs/info/info.component'
import {
  ListaSelecaoComponent
} from './dialogs/lista-selecao/lista-selecao.component'
import { ModalConfirmarComponent } from './dialogs/modal-confirmar/modal-confirmar.component'
import { ViewPdfComponent } from './dialogs/view-pdf/view-pdf.component'
import { CpfCnpjPipe } from './pipes/cpf-cnpj/cpf-cnpj.pipe'
import { SafeHtmlPipe } from './pipes/safe-html/safe-html.pipe'
import { TelefonePipe } from './pipes/telefone-pipe/telefone.pipe'
import { CertificateValidationDialogComponent } from './dialogs/certificate-validation-dialog/certificate-validation-dialog.component'


@NgModule({
  declarations: [
    ModalConfirmarComponent,
    InfoComponent,
    ConfirmationComponent,
    ListaSelecaoComponent,
    ViewPdfComponent,
    CpfCnpjPipe,
    TelefonePipe,
    SafeHtmlPipe,
    SanitizePipe,
    MoneyPipe,
    CertificateValidationDialogComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    EqpDesignToolsModule,
    TextMaskModule,
    NbDialogModule.forRoot(),
    ScrollingModule,
  ],
  providers: [CpfCnpjPipe, TelefonePipe, SafeHtmlPipe, SanitizePipe, MoneyPipe],
  exports: [
    FormsModule,
    ReactiveFormsModule,
    EqpDesignToolsModule,
    TextMaskModule,
    InfoComponent,
    ConfirmationComponent,
    ListaSelecaoComponent,
    ViewPdfComponent,
    SafeHtmlPipe,
    CpfCnpjPipe,
    SanitizePipe,
    MoneyPipe,
    CertificateValidationDialogComponent
  ],
  entryComponents: [
    InfoComponent,
    ConfirmationComponent,
    ListaSelecaoComponent,
    ViewPdfComponent,
  ],
})
export class CommonToolsModule {}
