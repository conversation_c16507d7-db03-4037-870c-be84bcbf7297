import { Injectable } from '@angular/core';
import { FilterDto } from '@common/interfaces/dtos/filter-dto';
import { ResponseDto } from '@common/interfaces/dtos/response-dto';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CommonMockService<T> {
  constructor(protected collection: T[]) {}

  public get(
    filters?: FilterDto[],
  ): Observable<ResponseDto<T[]>> {
    return of({
      sucesso: true,
      dados: this.collection
    })
  }

  public getIndividual(uuid: string): Observable<ResponseDto<T>> {
    return of({
      sucesso: true,
      dados: this.collection[0]
    })
  }

  public put(dto: T, uuid: string): Observable<any> {
    return of({
      sucesso: true,
      dados: dto
    })
  }

  public post(dto: T): Observable<any> {
    return of({
      sucesso: true,
      dados: dto
    })
  }

  public postBatch(batch: T[]): Observable<any> {
    return of({
      sucesso: true,
      dados: this.collection
    })
  }

  public delete(uuid: number): Observable<any> {
    return of(true)
  }
}
