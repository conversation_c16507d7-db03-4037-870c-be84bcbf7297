import { MessageDto } from './message-dto'

export class EsMessagesDto {
  public mensagens: MessageDto[] = []

  public adicionar(mensagem: MessageDto): number {
    if (!this.mensagens) this.mensagens = []
    return this.mensagens.push(mensagem)
  }

  public contemAviso(): boolean {
    let contemAviso = false

    if (this.mensagens) {
      this.mensagens.forEach(mensagem => {
        if (mensagem.aviso === true) contemAviso = true
      })
    }
    return contemAviso
  }

  public contemErro(): boolean {
    let contemErro = false

    if (this.mensagens) {
      this.mensagens.forEach(mensagem => {
        if (mensagem.erro === true) contemErro = true
      })
    }
    return contemErro
  }
}
