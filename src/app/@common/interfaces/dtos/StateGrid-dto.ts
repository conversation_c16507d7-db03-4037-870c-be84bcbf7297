export interface StateGrid {
  allowedPageSizes: number[]
  columns: ColunsGrig[]
  filterPanel: FilterpanelGrig
  filterValue: string
  pageIndex: number
  pageSize: number
  searchText: string
}

export interface ColunsGrig {
  dataField: string
  dataType: string
  filterValue: number
  name: string
  selectedFilterOperation: string
  visible: boolean
  visibleIndex: number
  width: number
}
export interface FilterpanelGrig {
  filterEnabled: boolean
}
